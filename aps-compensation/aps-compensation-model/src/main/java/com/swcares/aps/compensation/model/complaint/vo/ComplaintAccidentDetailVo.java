package com.swcares.aps.compensation.model.complaint.vo;

import com.swcares.aps.compensation.model.complaint.entity.ComplaintAccidentInfoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "ComplaintAccidentDetailVo对象", description = "旅客投诉事故单详情")
public class ComplaintAccidentDetailVo {

    @ApiModelProperty(value = "旅客投诉事故单信息")
    ComplaintAccidentInfoEntity  complaintAccidentInfoEntity;

    @ApiModelProperty(value = "旅客投诉事故单旅客列表")
    List<ComplaintPassengerListVo> accidentConcatPassengerVos;

    @ApiModelProperty(value = "关联补偿单信息")
    List<AccidentConcatCompensationInfoVo> accidentConcatCompensationInfoVos;
}
