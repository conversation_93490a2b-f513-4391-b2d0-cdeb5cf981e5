package com.swcares.aps.workflow.flowable.service;

import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.dto.CompleteProcessParamsDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityVO;
import com.swcares.aps.workflow.dto.HistoryTaskAuditActivityDTO;
import com.swcares.aps.workflow.dto.HistoryTaskAuditActivityVO;
import com.swcares.aps.workflow.dto.StartProcessParamsDTO;
import com.swcares.aps.workflow.dto.SyncWorkflowInfoDTO;

import java.util.List;

/**
 * ClassName：FlowableWorkflowService <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/2/11 <br>
 * @version v1.0 <br>
 */
public interface FlowableWorkflowService {
    /**
     *
     * @param params
     * @return
     */
    CurrentTaskActivityVO startProcess(StartProcessParamsDTO params);

    /**
     *
     * @param params
     * @return
     */
    CurrentTaskActivityVO completeTask(CompleteProcessParamsDTO params);

    /**
     *
     * @param params
     * @return
     */
    CurrentTaskActivityVO currentUserTask(BaseQueryParamDTO params);

    /**
     *
     * @param params
     * @return
     */
    HistoryTaskAuditActivityVO historyTaskAuditActivity(BaseQueryParamDTO params);

    /**
     *
     * @param paramDTO
     * @param variableName
     * @return
     */
    Object getProcessVariable(BaseQueryParamDTO paramDTO, String variableName);

    /**
     * Title：getNextTask <br>
     * Description： 根据当前节点taskId,查下一个【用户节点】<br>
     * author：傅欣荣 <br>
     * date：2022/2/8 13:59 <br>
     * @param
     * @return
     */
    CurrentTaskActivityDTO getNextTask(String taskId);

    /**
     * @title syncProcess
     * @description 接受同步流程的消息
     * <AUTHOR>
     * @date 2024/6/14 16:08
     * @param params
     * @return
     */
    void syncProcess(SyncWorkflowInfoDTO params);
}
