package com.swcares.aps.compensation.model.compensation.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName：OfflineGrantDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/5/17 17:50
 * @version： v1.0
 */
@Data
public class OfflineGrantDTO {


    @ApiModelProperty(value = "补偿单ID")
    @NotEmpty
    private String compensationId;


    @ApiModelProperty(value = "旅客ID")
    @NotEmpty
    private String paxId;
}
