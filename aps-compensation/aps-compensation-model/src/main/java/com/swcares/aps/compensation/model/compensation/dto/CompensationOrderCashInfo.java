package com.swcares.aps.compensation.model.compensation.dto;

import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CompensationOrderCashInfo  extends CompensationOrderInfoDO {
    @ApiModelProperty(value = "补偿旅客")
    private String paxName;

    @ApiModelProperty(value = "已补偿/所有补偿")
    private String theCompensationOf;

    @ApiModelProperty(value = "补偿标准")
    private String adultStd;
}
