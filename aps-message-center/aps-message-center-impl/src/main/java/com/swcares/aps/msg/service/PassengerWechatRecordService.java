/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：PassengerWechatRecordService <br>
 * Package：com.swcares.reaptv.msg.service <br> 
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.msg.model.dto.MessageStatusStaticsDTO;
import com.swcares.aps.msg.model.dto.PassengerWechatSearchDTO;
import com.swcares.aps.msg.model.dto.PassengerWechatSubscribeMsgSendDTO;
import com.swcares.aps.msg.model.dto.PassengerWechatTemplateMsgSendDTO;
import com.swcares.aps.msg.model.entity.PassengerWechatRecord;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * InterfaceName：com.swcares.reaptv.msg.service.PassengerWechatRecordService <br>
 * Description：TODO：(这里用一句话描述这个接口的作用) <br>
 * <AUTHOR> <br>
 * date 2022/10/11 6:54 <br>
 * @version v1.0.0 <br>
 */
public interface PassengerWechatRecordService extends IService<PassengerWechatRecord> {
    IPage<PassengerWechatRecord> getPassengerWechatRecordPage(PassengerWechatSearchDTO dto);

    List<PassengerWechatRecord> getByIds(List<Long> ids);

    void download(PassengerWechatSearchDTO dto, HttpServletRequest request, HttpServletResponse response);

    void downloadById(List<Long> ids, HttpServletRequest request, HttpServletResponse response);

    boolean logicRemoveById(Long id);

    boolean batchDelete(List<Long> ids);

    List<MessageStatusStaticsDTO> getMessagesStatusStatics(PassengerWechatSearchDTO dto);
    
    PassengerWechatRecord sendWechatTemplateMessage(PassengerWechatTemplateMsgSendDTO dto);
    
    PassengerWechatRecord sendWechatSubscribeMessage(PassengerWechatSubscribeMsgSendDTO dto);
}
