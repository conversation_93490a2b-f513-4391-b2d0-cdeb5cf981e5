package com.swcares.aps.component.drools.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * ClassName：com.wl.learn.model.DroolsRule <br>
 * Description：规则管理实体对象 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021/10/19 15:43 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="规则管理实体对象", description="")
@TableName("drools_rule")
public class DroolsRuleDO extends BaseEntity{
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "id")
    private Long id;

//    private Long tenantId;

    @ApiModelProperty(value = "规则内容")
    @NotNull
    private String ruleContent;

    @ApiModelProperty(value = "规则代码")
    @NotNull
    private String code;

    @ApiModelProperty(value = "规则描述")
    private String remark;

    @ApiModelProperty(value = "是否逻辑删除(0否1是)")
    private int deleted;
}
