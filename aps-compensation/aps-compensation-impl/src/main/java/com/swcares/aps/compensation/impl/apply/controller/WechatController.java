package com.swcares.aps.compensation.impl.apply.controller;

import com.swcares.aps.compensation.impl.apply.service.WechatService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：WechatController <br>
 * Package：com.swcares.aps.apply.impl.controller <br> 
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2021年 11月25日 14:27 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/apply/wechat")
@Api(tags = "对接微信公众号接口")
@ApiVersion(value = "对接微信公众号接口 v1.0")
public class WechatController extends BaseController {

    @Autowired
    private WechatService wechatService;

    /**
     * Title：getAccessTokenByOfficialAccounts <br>
     * Description：公众号网页开发：通过code换取openid <br>
     * author：于琦海 <br>
     * date：2021/11/25 16:15 <br>
     * @params String code 用户授权同意获取的code
     * @return BaseResult<String>
     */
    @PostMapping("/offiaccount/openid")
    @ApiOperation(value = "公众号网页开发：通过code换取openid")
    public BaseResult<String> getAccessTokenByOfficialAccounts(@ApiParam(value = "公众号网页开发：用户授权同意获取的code", required = true) String code){
        return ok(wechatService.getAccessTokenByOfficialAccounts(code));
    }

    /**
     * @title getAccessTokenByMiniProgram
     * @description 小程序开发：通过code换取openid
     * <AUTHOR>
     * @date 2022/7/20 10:25
     * @param code 用户授权同意获取的code
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.String>
     */
    @GetMapping("/miniprogram/openid")
    @ApiOperation(value = "小程序开发：通过code换取openid")
    public BaseResult<String> getAccessTokenByMiniProgram(@ApiParam(value = "租户code", required = true)String tenantCode,@ApiParam(value = "小程序开发：通过code换取openid", required = true)String code){
        return ok(wechatService.getAccessTokenByMiniProgram(tenantCode.toUpperCase(),code));
    }

    /**
     * @title
     * @description 赔付的旅客端小程序登录
     * <AUTHOR>
     * @date 2022/8/15 9:12
     * @param openId
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.String>
     */
    @GetMapping("/miniprogram/login")
    @ApiOperation(value = "赔付的旅客端小程序登录")
    public BaseResult<Object> miniProgramLogin(@ApiParam(value = "小程序端的openId", required = true)String openId, String tenantCode){
        return ok(wechatService.miniProgramLogin(openId, tenantCode.toUpperCase()));
    }

    
}
