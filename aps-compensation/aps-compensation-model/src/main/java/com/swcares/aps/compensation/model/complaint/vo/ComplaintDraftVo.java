package com.swcares.aps.compensation.model.complaint.vo;

import com.swcares.aps.compensation.model.complaint.entity.ComplaintAccidentInfoEntity;
import com.swcares.aps.compensation.model.complaint.entity.PassengerAccidentInfoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "ComplaintDraftVo对象", description = "投诉事故单草稿查询返回")
public class ComplaintDraftVo {

    @ApiModelProperty(value = "事故单信息")
    private ComplaintAccidentInfoEntity complainAccidentInfoEntity;

    @ApiModelProperty(value = "旅客信息")
    private List<ComplaintPassengerListVo> passengerComplaintDtoList;
}
