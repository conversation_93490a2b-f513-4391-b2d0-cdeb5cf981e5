package com.swcares.aps.web.bff.service;


import com.swcares.aps.compensation.model.baggage.accident.dto.FindBaggageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.FindBaggageVO;
import com.swcares.baseframe.common.base.PagedResult;

import java.util.List;

/**
 * ClassName：com.swcares.aps.compensation.bff.web.service <br>
 * Description：箱包事故单单服务类 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest 唐康  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 3月4日 13:29 <br>
 * @version v1.0 <br>
 */
public interface BaggageAccidentWebService {
    PagedResult<List<FindBaggageVO>> findLuggageList(FindBaggageDTO dto);
}
