/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：FlightEventProcessor <br>
 * Package：com.swcares.reaptv.msg.flightevent <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.flightevent;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.swcares.aps.msg.flightevent.analyzer.FlightEventAnalyzeResult;
import com.swcares.aps.msg.flightevent.analyzer.FlightEventMessageAnalyzer;
import com.swcares.aps.msg.flightevent.taskbuilder.SemiAutoSmsTaskBuilder;
import com.swcares.aps.msg.flightevent.taskbuilder.SemiAutoSmsTaskBuilderFactory;
import com.swcares.aps.msg.model.dto.SemiAutoSmsTaskDTO;
import com.swcares.aps.msg.model.entity.*;
import com.swcares.aps.msg.service.*;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * ClassName：com.swcares.reaptv.msg.flightevent.FlightEventProcessor <br>
 * Description：将航变原始消息中的数据集成到旅服系统中
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/25 12:39 <br>
 * @version:
 */
@Component
@Slf4j
public class FlightEventIntegrator {
    @Autowired(required = false) FlightEventService flightEventService;
    @Autowired(required = false) FlightEventSourceMessageService flightEventSourceMessageService;

    @Autowired(required = false) FlightEventPassengerService flightEventPassengerService;

    @Autowired(required = false) GFPassengerInfoService gfPassengerInfoService;

    @Autowired(required = false) GFFlightinfoService gfFlightInfoService;

    @Autowired
    private SmsTaskService smsTaskService;
    
    @Transactional
    public void integrateFlightEvent(FlightEventSourceMessage flightEventSourceMessage) throws ParseException {
        processMessage(flightEventSourceMessage);
    }

    private void processMessage(FlightEventSourceMessage flightEventSourceMessage) throws ParseException {
        log.info("开始处理航变消息{}",flightEventSourceMessage.getId());
        //解析原始的航变事件消息
        FlightEventAnalyzeResult flightEventAnalyzeResult = FlightEventMessageAnalyzer.analyzeFlightEvent(flightEventSourceMessage);

        FlightEvent flightEvent = flightEventAnalyzeResult.getFlightEvent();
        log.info("保存航变消息{}中的航变事件到数据库",flightEvent.getMessageId());
        flightEventService.save(flightEvent);

        log.info("保存航变事件消息{}中的旅客信息到数据库", flightEvent.getMessageId());
        fullfillPassengerInfo(flightEventAnalyzeResult);
        for(FlightEventPassenger passenger : flightEventAnalyzeResult.getFlightEventPassengers()){
            String phoneNumber = passenger.getTelePhone();
            flightEventPassengerService.save(passenger);
            passenger.setTelePhone(phoneNumber);
        }

        mergeFlightEventIntoGFData(flightEventAnalyzeResult.getFlightEvent(), flightEventAnalyzeResult.getFlightDates());
        mergePassengerInfoGFData(flightEventAnalyzeResult.getFlightEventPassengers());

        buildSmsTask(flightEventAnalyzeResult);

        flightEventService.updateById(flightEvent);

        log.info("航变数据处理完成");
    }



    //将原始消息标记为重复的消息
    private void markAsDuplicate(FlightEventSourceMessage flightEventSourceMessage, Long duplicateSourceMessageId) {
        flightEventSourceMessage.setProcessed(true);
        flightEventSourceMessage.setResult("有重复的航变消息" + duplicateSourceMessageId);
        flightEventSourceMessageService.updateById(flightEventSourceMessage);
    }

    private void fullfillPassengerInfo(FlightEventAnalyzeResult flightEventAnalyzeResult){
        setFlightEventIdToAllPassenger(flightEventAnalyzeResult.getFlightEvent(), flightEventAnalyzeResult.getFlightEventPassengers());

    }

    private void setFlightEventIdToAllPassenger(FlightEvent flightEvent, List<FlightEventPassenger> passengers){
        for (FlightEventPassenger flightEventPassenger : passengers){
            flightEventPassenger.setFlightEventId(flightEvent.getId());
        }
    }

    private void mergePassengerInfoGFData(List<FlightEventPassenger> flightEventPassengers) {
        log.info("保存航变事件中的旅客数据到广分旅客数据表中");

        if (flightEventPassengers == null || flightEventPassengers.isEmpty()){
            return;
        }

        for (FlightEventPassenger flightEventPassenger : flightEventPassengers){
            //检查数据库中是否已经存在该旅客数据
            GFPassengerInfo gfPassenger = generateGFPassengerInfo(flightEventPassenger);
            GFPassengerInfo gfPassengerInDB = getDuplicatePassengerInDB(gfPassenger);

            if (gfPassengerInDB == null) {
                gfPassengerInfoService.save(gfPassenger);
                log.debug("保存了广分旅客数据{}", gfPassenger.getId());
            } else {
                gfPassengerInDB.setVip(gfPassenger.isVip());
                gfPassengerInDB.setStandby(gfPassenger.isStandby());
                gfPassengerInDB.setGroupMember(gfPassenger.isGroupMember());
                gfPassengerInDB.setName(gfPassenger.getName());
                gfPassengerInDB.setTelePhone(gfPassenger.getTelePhone());
                gfPassengerInDB.setSequenceNumber(gfPassenger.getSequenceNumber());

                gfPassengerInfoService.updateById(gfPassengerInDB);
            }
        }
    }

    private GFPassengerInfo getDuplicatePassengerInDB(GFPassengerInfo gfPassenger) {
        LambdaQueryWrapper<GFPassengerInfo> searchCriteria = new LambdaQueryWrapper<>();
        searchCriteria.eq(GFPassengerInfo::getFlightNumber, gfPassenger.getFlightNumber())
                .eq(GFPassengerInfo::getFlightDate, gfPassenger.getFlightDate())
                .eq(GFPassengerInfo::getOrg, gfPassenger.getOrg())
                .eq(GFPassengerInfo::getDest, gfPassenger.getDest())
                .eq(GFPassengerInfo::getPnr, gfPassenger.getPnr())
                .eq(GFPassengerInfo::getTicketNumber,gfPassenger.getTicketNumber());
        return gfPassengerInfoService.getOne(searchCriteria);
    }

    private GFPassengerInfo generateGFPassengerInfo(FlightEventPassenger flightEventPassenger) {
        GFPassengerInfo gfPassengerInfo = new GFPassengerInfo();
        gfPassengerInfo.setFlightNumber(flightEventPassenger.getFlightNumber());
        gfPassengerInfo.setFlightDate(flightEventPassenger.getFlightDate());
        gfPassengerInfo.setOrg(flightEventPassenger.getOrg());
        gfPassengerInfo.setDest(flightEventPassenger.getDest());
        gfPassengerInfo.setPnr(flightEventPassenger.getPnrCode());
        gfPassengerInfo.setSequenceNumber(flightEventPassenger.getSequenceNumber());
        gfPassengerInfo.setName(flightEventPassenger.getPassengerName());
        gfPassengerInfo.setGroupMember(flightEventPassenger.isGroupMember());
        gfPassengerInfo.setStandby(flightEventPassenger.isStandby());
        gfPassengerInfo.setVip(flightEventPassenger.isVip());
        gfPassengerInfo.setTelePhone(flightEventPassenger.getTelePhone());
        gfPassengerInfo.setTicketNumber(flightEventPassenger.getTicketNumber());

        return gfPassengerInfo;
    }

    private void mergeFlightEventIntoGFData(FlightEvent flightEvent, List<String> flightDates){
        if (flightEvent == null){
            return;
        }
        log.info("保存航变事件{}中的数据到广分航班数据表中", flightEvent.getId());
        for(String flightDate : flightDates) {
            GFFlightInfo flightInfo = generateGFFlightInfo(flightEvent, LocalDate.parse(flightDate));
            gfFlightInfoService.mergeFlightInfo(flightInfo);
        }
    }

    @NotNull private GFFlightInfo generateGFFlightInfo(FlightEvent flightEvent, LocalDate flightDate) {
        GFFlightInfo flightInfo = new GFFlightInfo();
        flightInfo.setFlightNumber(flightEvent.getFlightNumber());
        flightInfo.setFlightDate(flightDate);
        flightInfo.setOrg(flightEvent.getOrg());
        flightInfo.setDest(flightEvent.getDest());
        if(flightEvent.getDepTime() != null) {
            flightInfo.setDepTime(LocalDateTime.of(flightDate, flightEvent.getDepTime()));
        }
        flightInfo.setAcrossDay(true);
        if (flightEvent.getArrTime() != null) {
            if (flightEvent.isFlightOverNight()) {
                flightInfo.setArrTime(LocalDateTime.of(flightDate.plusDays(1), flightEvent.getArrTime()));
            } else {
                flightInfo.setArrTime(LocalDateTime.of(flightDate, flightEvent.getArrTime()));
            }
        }
        return flightInfo;
    }

    private void buildSmsTask(FlightEventAnalyzeResult flightEventAnalyzeResult) {
        SemiAutoSmsTaskBuilder taskBuilder =
                SemiAutoSmsTaskBuilderFactory.getSemiAutoSmsTaskBuilder(flightEventAnalyzeResult.getFlightEvent().getProcessType());
        
        List<SemiAutoSmsTaskDTO> taskDTOList = taskBuilder.buildSemiAutoTask(flightEventAnalyzeResult.getFlightEvent(),
                flightEventAnalyzeResult.getFlightEventPassengers());

        for (SemiAutoSmsTaskDTO taskDTO : taskDTOList){
            smsTaskService.createSemiAutoSmsTask(taskDTO);
        }

        flightEventAnalyzeResult.getFlightEvent().setSmsTaskCount(taskDTOList.size());
    }

}
