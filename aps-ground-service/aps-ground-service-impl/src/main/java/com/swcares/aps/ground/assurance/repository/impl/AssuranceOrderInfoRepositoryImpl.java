package com.swcares.aps.ground.assurance.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderInfoMapper;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderServiceChoiceSupplierMapper;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderServiceMapper;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderServiceVerifyMapper;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderUserMapper;
import com.swcares.aps.ground.datasync.repository.AssuranceOrderDataPushRepository;
import com.swcares.aps.ground.assurance.repository.AssuranceOrderInfoRepository;
import com.swcares.aps.ground.models.assurance.domain.AssuranceOrderDomain;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderInfoDO;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderServiceChoiceSupplierDO;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderServiceDO;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderServiceVerifyDO;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderUserDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName：AssuranceOrderInfoRepositoryImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/21 15:34
 * @version： v1.0
 */
@Component
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class AssuranceOrderInfoRepositoryImpl
        extends ServiceImpl<AssuranceOrderInfoMapper, AssuranceOrderInfoDO>
        implements AssuranceOrderInfoRepository {

    @Autowired
    private AssuranceOrderServiceMapper assuranceOrderServiceMapper;

    @Autowired
    private AssuranceOrderServiceChoiceSupplierMapper assuranceOrderServiceChoiceSupplierMapper;

    @Autowired
    private AssuranceOrderUserMapper assuranceOrderUserMapper;

    @Autowired
    private AssuranceOrderServiceVerifyMapper assuranceOrderServiceVerifyMapper;

    @Autowired
    private AssuranceOrderDomainFactory assuranceOrderDomainFactory;

    @Autowired
    private AssuranceOrderDataPushRepository assuranceOrderDataPushRepository;

    @Override
    public void saveDomain(AssuranceOrderDomain orderDomain) {
        this.save(orderDomain.getOrderInfoDO());
        orderDomain.refreshOrderId();
        saveServiceAndUserDomains(orderDomain);
    }

    public void saveServiceAndUserDomains(AssuranceOrderDomain orderDomain) {
        for(AssuranceOrderServiceDO orderServiceDO:orderDomain.getOrderServiceDOs()){
            assuranceOrderServiceMapper.insert(orderServiceDO);
        }
        orderDomain.refreshOrderServiceId();

        for(AssuranceOrderServiceChoiceSupplierDO assuranceOrderServiceChoiceSupplierDO:orderDomain.getServiceChoiceSupplierDOs()){
            assuranceOrderServiceChoiceSupplierMapper.insert(assuranceOrderServiceChoiceSupplierDO);
        }

        for(AssuranceOrderUserDO orderUserDO :orderDomain.getOrderUserDOs()){
            assuranceOrderUserMapper.insert(orderUserDO);
        }
        orderDomain.refreshOrderUserId();

        for(AssuranceOrderServiceVerifyDO serviceVerifyDO:orderDomain.getServiceCertificateDos()){
            assuranceOrderServiceVerifyMapper.insert(serviceVerifyDO);
        }
    }

    @Override
    public void updateDomain(AssuranceOrderDomain orderDomain) {
        AssuranceOrderDomain assuranceOrderDomain = assuranceOrderDomainFactory.createById(orderDomain.getDomainId());
        if(assuranceOrderDomain!=null){
            List<String> orderServiceIds = assuranceOrderDomain.getOrderServiceDOs().stream().map(AssuranceOrderServiceDO::getId).collect(Collectors.toList());
            assuranceOrderServiceMapper.deleteBatchIds(orderServiceIds);

            List<String> serviceChoiceSupplierIds = assuranceOrderDomain.getServiceChoiceSupplierDOs().stream().map(AssuranceOrderServiceChoiceSupplierDO::getId).collect(Collectors.toList());
            assuranceOrderServiceChoiceSupplierMapper.deleteBatchIds(serviceChoiceSupplierIds);

            List<String> orderUserIds = assuranceOrderDomain.getOrderUserDOs().stream().map(AssuranceOrderUserDO::getId).collect(Collectors.toList());
            assuranceOrderUserMapper.deleteBatchIds(orderUserIds);

            List<String> certificateDoIds = assuranceOrderDomain.getServiceCertificateDos().stream().map(AssuranceOrderServiceVerifyDO::getId).collect(Collectors.toList());
            assuranceOrderServiceVerifyMapper.deleteBatchIds(certificateDoIds);
        }
        AssuranceOrderInfoDO orderInfoDO = orderDomain.getOrderInfoDO();
        this.saveOrUpdate(orderInfoDO);
        orderDomain.refreshOrderId();
        saveServiceAndUserDomains(orderDomain);
    }

    @Override
    public void delete(AssuranceOrderDomain orderDomain) {
        this.baseMapper.logicRemoveById(orderDomain.getDomainId());

        for(AssuranceOrderServiceDO orderServiceDO:orderDomain.getOrderServiceDOs()){
            assuranceOrderServiceMapper.logicRemoveById(orderServiceDO.getId());
        }

        for(AssuranceOrderServiceChoiceSupplierDO assuranceOrderServiceChoiceSupplierDO:orderDomain.getServiceChoiceSupplierDOs()){
            assuranceOrderServiceChoiceSupplierMapper.logicRemoveById(assuranceOrderServiceChoiceSupplierDO.getId());
        }

        for(AssuranceOrderUserDO orderUserDO :orderDomain.getOrderUserDOs()){
            assuranceOrderUserMapper.logicRemoveById(orderUserDO.getId());
        }

        for(AssuranceOrderServiceVerifyDO serviceVerifyDO:orderDomain.getServiceCertificateDos()){
            assuranceOrderServiceVerifyMapper.logicRemoveById(serviceVerifyDO.getId());
        }
    }

    @Override
    public void updateDoInfo(AssuranceOrderInfoDO orderInfoDO) {
        this.updateById(orderInfoDO);
        assuranceOrderDataPushRepository.pushOrderInfo(orderInfoDO);
    }

    @Override
    public void updateOrderServiceVerifyDOInfo(List<AssuranceOrderServiceVerifyDO> assuranceOrderServiceVerifyDOS) {
        for(AssuranceOrderServiceVerifyDO assuranceOrderServiceVerifyDO:assuranceOrderServiceVerifyDOS){
            assuranceOrderServiceVerifyMapper.updateById(assuranceOrderServiceVerifyDO);
        }
        assuranceOrderDataPushRepository.pushOrderServiceVerifies(assuranceOrderServiceVerifyDOS);
    }

    @Override
    public void refreshServiceVerifyAmount(Set<String> orderServiceIds) {
        assuranceOrderServiceMapper.refreshServiceVerifyAmount(orderServiceIds);
    }
}
