package com.swcares.aps.compensation.model.overbook.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：OverBookH5CompensationDetailsVO
 * @Description 补偿次数
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/27 15:57
 * @version： v1.0
 */
@Data
@ApiModel(value="补偿次数VO对象", description="补偿次数VO对象")
public class CompensationNumListVO {
    @ApiModelProperty(value = "补偿单号")
    private String orderNo;

    @ApiModelProperty(value = "补偿类型-1不正常航班，2异常行李，3超售")
    private String accidentType;

    @ApiModelProperty(value = "补偿方式(补偿方式 1现金，2虚拟，3实物)")
    private String compensateType;

    @ApiModelProperty(value = "本次总金额")
    private String sumMoney;

    @ApiModelProperty(value = "补偿单状态(0草稿,1审核中,2审核不通过,3驳回,4审核通过,5生效,6关闭,7逾期)")
    private String status;

    @ApiModelProperty(value = "领取状态(0未领取,1已领取,2领取中)—>取数据字典")
    private String receiveStatus;

    @ApiModelProperty(value = "旅客申领资格开关(默认0有资格，1=冻结)")
    private String switchOff;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;

}
