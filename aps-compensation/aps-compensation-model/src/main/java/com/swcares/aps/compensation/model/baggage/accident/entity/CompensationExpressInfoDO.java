package com.swcares.aps.compensation.model.baggage.accident.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.compensation.entity.ExpressInfo <br>
 * Description：快递信息表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-03 <br>
 * @version v1.0 <br>
 */
@Data
@TableName("compensation_express_info")
@ApiModel(value="CompensationExpressInfo对象", description="赔偿_快递信息表")
public class CompensationExpressInfoDO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private Long tenantId;

    @ApiModelProperty(value = "收件人")
    private String addressee;

    @ApiModelProperty(value = "收件地址")
    private String address;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "快递类型(手动添加，补偿单线下发放等)")
    private String expressType;

    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    @ApiModelProperty(value = "快递公司")
    private String expressCompany;

    @ApiModelProperty(value = "邮寄物品")
    private String expressGoods;

    @ApiModelProperty(value = "补偿单号")
    private String orderNo;

    @ApiModelProperty(value = "事故单id")
    private Long accidentId;


}
