package com.swcares.aps.web.bff.controller.ground;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.swcares.aps.ground.AssuranceQueryApi;
import com.swcares.aps.ground.enums.VerificationState;
import com.swcares.aps.ground.models.assurance.dto.AssuranceVerifyQueryRequest;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderServiceVerifyUserDetailVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * @ClassName：AssuranceQueryController
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/21 16:30
 * @version： v1.0
 */
@RestController
@RequestMapping("/assurance/busi/verifyQuery")
@Api(tags = {"保障单业务信息查询接口"})
@ApiVersion({"保障单相关Api"})
public class AssuranceVerifyQueryController extends BaseController {

    @Autowired
    private AssuranceQueryApi assuranceQueryApi;

    @PostMapping("/pageList")
    @ApiOperation(value = "分页查询核销记录列表信息")
    public PagedResult<List<AssuranceOrderServiceVerifyUserDetailVO>> pageQueryOrderList(@RequestBody AssuranceVerifyQueryRequest request){
        //航班时间倒序，核销时间倒序
        request.setItems(OrderItem.descs("flightDate","confirmTime"));
        request.setVerificationState(Arrays.asList(VerificationState.CONFIRM.getCode()));
        PagedResult<List<AssuranceOrderServiceVerifyUserDetailVO>> result = assuranceQueryApi.getOrderVerifyPage(request);
        return result;
    }
}
