package com.swcares.aps.usercenter.remote.api.uc;

import com.swcares.aps.usercenter.model.uc.dto.ReaptvEmployeeDTO;
import com.swcares.aps.usercenter.model.uc.dto.ReaptvEmployeeUserDTO;
import com.swcares.aps.usercenter.model.uc.entity.ReaptvEmployee;
import com.swcares.aps.usercenter.model.uc.vo.ReaptvEmployeeDetailVO;
import com.swcares.aps.usercenter.model.uc.vo.ReaptvEmployeeVO;
import com.swcares.aps.usercenter.model.uc.vo.ReaptvUserEmployeeVO;
import com.swcares.aps.usercenter.vo.EmployeeWechatVO;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.common.sys.vo.UserEmpIdVO;
import com.swcares.common.uc.dto.EmployeeOrgDTO;
import com.swcares.common.uc.dto.EmployeePagedDTO;
import com.swcares.common.uc.dto.EmployeeQueryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.reaptv.usercenter.impl.uc.controller.ReaptvEmployeeController <br>;
 * Description：藏航系统所管理的人员 前端控制器 <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/5/26 10:41 <br>;
 * @version v1.0 <br>;
 */
@FeignClient(name = "aps-user-center", path = "/api/aps-user-center", contextId = "ReaptvEmployeeApi")
public interface ReaptvEmployeeApi {

    @PostMapping(value = "/uc/reaptv_employee/save", headers = "content-type=multipart/form-data")
    BaseResult<Object> save(@SpringQueryMap ReaptvEmployeeDTO dto, @RequestPart(name = "file", required = false) MultipartFile file);

    @PostMapping(value = "/uc/reaptv_employee/save_create_user", headers = "content-type=multipart/form-data")
    BaseResult<Object> saveOrUser(@SpringQueryMap ReaptvEmployeeUserDTO dto, @RequestPart(name = "file", required = false) MultipartFile file);

    @PostMapping("/uc/reaptv_employee/delete/{id}")
    BaseResult<Object> delete(@PathVariable("id") Long id);

    @PostMapping("/uc/reaptv_employee/delete")
    BaseResult<Object> deleteById(@RequestParam Long id);

    @PostMapping(value = "/uc/reaptv_employee/update", headers = "content-type=multipart/form-data")
    BaseResult<Object> update(@SpringQueryMap ReaptvEmployeeDTO dto, @RequestPart(name = "file", required = false) MultipartFile file);

    @GetMapping("/uc/reaptv_employee/get/{id}")
    BaseResult<ReaptvEmployeeVO> get(@PathVariable("id") Long id);

    @GetMapping("/uc/reaptv_employee/get")
    BaseResult<ReaptvEmployeeVO> getById(@RequestParam Long id);

    @GetMapping("/uc/reaptv_employee/get_detail")
    BaseResult<ReaptvEmployeeDetailVO> getDetailById(@RequestParam Long id);

    @GetMapping("/uc/reaptv_employee/get_by_org")
    BaseResult<List<ReaptvEmployeeDetailVO>> getEmployeeByOrg(@RequestParam EmployeeQueryDTO dto);

    @PostMapping("/uc/reaptv_employee/page")
    PagedResult<List<ReaptvEmployeeVO>> page(@RequestBody EmployeePagedDTO dto);

    @PostMapping("/uc/reaptv_employee/batch_chg_org")
    BaseResult<Object> batchChgOrg(@RequestBody EmployeeOrgDTO dto);

    @PostMapping("/uc/reaptv_employee/get_by_mgtorg")
    BaseResult<List<ReaptvUserEmployeeVO>> getByManagerScope(@RequestBody EmployeePagedDTO dto);

    @PostMapping("/uc/reaptv_employee/get_by_ids")
    BaseResult<List<ReaptvUserEmployeeVO>> getByIds(@RequestBody List<Long> ids);

    @PostMapping("/uc/reaptv_employee/sync/{id}")
    BaseResult<Object> sync(@PathVariable("id") Long id);

    @PostMapping("/uc/reaptv_employee/sync")
    BaseResult<Object> syncById(@RequestParam Long id);

    @GetMapping("/uc/reaptv_employee/get_by_id_card")
    BaseResult<ReaptvEmployee> getEmployeeByIdCard(@RequestParam String idCard);

    @PostMapping(value = "/uc/reaptv_employee/save_emp_user")
    BaseResult<Map<String, UserEmpIdVO>> saveEmpUser(@RequestBody List<ReaptvEmployeeUserDTO> dto);

    @GetMapping(value="/uc/reaptv_employee/get_employee_wechat")
    public BaseResult<EmployeeWechatVO> getEmployeeWechat(
            @RequestParam(required = true) Long userId, @RequestParam(required = true) String clientId);
}
