package com.swcares.aps.compensation.model.overbook.dto;

import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@ApiModel(value = "OverBookWorkflowSubmitDto",description = "超售审核补偿单单提交参数")
public class OverBookWorkflowSubmitDTO {
    //补偿总金额
    private String compensationAmount;
    //事故类型(补偿子类型)
    private String compensationSubType;
    // 补偿航站
    private String compensationAirport;
    // 补偿来源
    private String accidentSource;

}
