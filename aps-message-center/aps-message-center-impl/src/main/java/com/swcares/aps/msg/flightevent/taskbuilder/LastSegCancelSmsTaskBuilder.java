/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：LastSegCancelSmsTaskBuilder <br>
 * Package：com.swcares.reaptv.msg.flightevent.taskbuilder <br>
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.flightevent.taskbuilder;

import com.swcares.aps.component.dict.model.vo.DictCacheVO;
import com.swcares.aps.msg.model.entity.FlightEvent;
import com.swcares.aps.msg.model.entity.FlightEventPassenger;
import com.swcares.aps.msg.service.TempleteService;
import com.swcares.aps.usercenter.remote.api.uc.ReaptvDictApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.reaptv.msg.flightevent.taskbuilder.LastSegCancelSmsTaskBuilder <br>
 * Description：长航段后段取消的航变事件的半自动短信任务创建工具类
 * @author: 王翼 <br>
 * @CreatedAt: 2023/1/3 13:33 <br>
 * @version:
 */
@Component
public class LastSegCancelSmsTaskBuilder extends CancelSmsTaskBuilder{
    private static final String SUPPORTED_PROCESS_TYPE = "RPL";


    @Autowired
    private TempleteService templeteService;

    @Autowired
    private ReaptvDictApi reaptvDictApi;


    
    @Override public boolean isSupport(FlightEvent flightEvent) {
        return SUPPORTED_PROCESS_TYPE.equalsIgnoreCase(flightEvent.getProcessType());
    }

    @Override
    Map<String, List<FlightEventPassenger>> groupPassenger(FlightEvent flightEvent, List<FlightEventPassenger> passengers) {
        Map<String,List<FlightEventPassenger>> result = new HashMap<>();
        List<DictCacheVO> cabinClassConfig =  reaptvDictApi.getByDictType("cabin_class").getData();
        for (FlightEventPassenger passenger : passengers){
            String group = getTemplate(flightEvent, passenger, cabinClassConfig);
            putIntoGroup(result, group, passenger);
        }

        return result;
    }
    
    private String getTemplate(FlightEvent flightEvent, FlightEventPassenger passenger, List<DictCacheVO> cabinClassConfig){
        if (flightEvent.isCancelLastSeg()){
            return getTemplateForCancelLastSeg(flightEvent, passenger, cabinClassConfig);
        } else {
            return getTemplateForNormalCancel(flightEvent, passenger, cabinClassConfig);
        }
    }
    
    private String getTemplateForCancelLastSeg(FlightEvent flightEvent, FlightEventPassenger passenger, List<DictCacheVO> cabinClassConfig){
        
        if (isProtected(passenger)){
            if(isCabinDegraded(passenger, cabinClassConfig)){
                return CANCEL_LAST_SEG_PORTECTED_DEGRAD;  //长航段取消后段，有保护，有降舱
            } else {
                return CANCEL_LAST_SEG_PROTECTED_NOT_DEGRAD;   //长航段取消后段，无保护
            }
        } else {
            if (hasSupplementFlight(flightEvent)) {
                return CANCEL_LAST_SEG_NOT_PROTECTED;  //长航段取消后段，无保护
            } else {
                return CANCEL_LAST_SEG_NOT_SUPPLEMENT; //长航段取消后段，补班待定
            }
        }
    }
    
    private String getTemplateForNormalCancel(FlightEvent flightEvent, FlightEventPassenger passenger, List<DictCacheVO> cabinClassConfig){
        if (isProtected(passenger)){
            if(isCabinDegraded(passenger, cabinClassConfig)){
                return CANCEL_ALL_SEG_PORTECTED_DEGRAD;  //有保护，有降舱
            } else {
                return CANCEL_ALL_SEG_PROTECTED_NOT_DEGRAD;   //无保护
            }
        } else {
            if (hasSupplementFlight(flightEvent)) {
                return CANCEL_ALL_SEG_NOT_PROTECTED;  //无保护
            } else {
                return CANCEL_ALL_SEG_NOT_SUPPLEMENT; //补班待定
            }
        }
    }

    private void putIntoGroup(Map<String,List<FlightEventPassenger>> groups, String groupName, FlightEventPassenger passenger){
        if (!groups.containsKey(groupName)){
            groups.put(groupName, new ArrayList<>());
        }

        groups.get(groupName).add(passenger);
    }

    @PostConstruct
    public void registerToFactory(){
        SemiAutoSmsTaskBuilderFactory.register(SUPPORTED_PROCESS_TYPE, this);
    }
}
