<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.msg.mapper.PassengerSmsRecordMapper">
    <sql id="passengerSmsRecordDetail">
        SELECT
        ID,
        FLIGHT_NUMBER,
        FLIGHT_DATE,
        FLIGHT_EVENT,
        ORG_AIRPORT,
        DEST_AIRPORT,
        PNR,
        TICKET_NUMBER,
        CONTENT,
        BUSINESS_TYPE,
        PASSENGER_NAME,
        PHONE_NUMBER,
        SENDER_INFO,
        SENDER_ACCOUNT,
        SENT_TIME,
        STATUS,
        TRIGGER_TYPE,
        REASON,
        CREATED_TIME,
        CREATED_BY,
        UPDATED_TIME,
        UPDATED_BY
        FROM
        MSG_PASSENGER_SMS_RECORD MPSR
    </sql>
    <sql id="searchCriteriaOracle">
        WHERE 1=1
        <if test="dto.passengerName != null and dto.passengerName != '' ">
            AND MPSR.PASSENGER_NAME LIKE '%'||#{dto.passengerName}||'%'
        </if>
        <if test="dto.phoneNumber != null and dto.phoneNumber != '' ">
            AND MPSR.PHONE_NUMBER = #{dto.phoneNumber}
        </if>
        <if test="dto.flightNumber != null and dto.flightNumber != '' ">
            AND MPSR.FLIGHT_NUMBER = #{dto.flightNumber}
        </if>
        <if test="dto.flightDateStart != null and dto.flightDateStart != '' ">
            AND MPSR.FLIGHT_DATE <![CDATA[ >= ]]> to_date(#{dto.flightDateStart}, 'yyyy-MM-dd')
        </if>
        <if test="dto.flightDateEnd != null and dto.flightDateEnd != '' ">
            AND MPSR.FLIGHT_DATE <![CDATA[ < ]]> (to_date(#{dto.flightDateEnd}, 'yyyy-MM-dd')+1)
        </if>
        <if test="dto.ticketNumber != null and dto.ticketNumber != '' ">
            AND MPSR.TICKET_NUMBER LIKE  '%'||#{dto.ticketNumber}||'%'
        </if>
        <if test="dto.pnr != null and dto.pnr != ''">
            AND MPSR.PNR = #{dto.pnr}
        </if>
        <if test="dto.status != null">
            AND MPSR.STATUS = #{dto.status}
        </if>
        <if test="dto.flightEvent != null and dto.flightEvent != '' ">
            AND MPSR.FLIGHT_EVENT = #{dto.flightEvent}
        </if>
        <if test="dto.businessTypes != null and dto.businessTypes.size() >0">
            AND MPSR.BUSINESS_TYPE IN
            <foreach collection="dto.businessTypes" index="index" item="businessType" separator="," open="(" close=")">
                #{businessType}
            </foreach>
        </if>
        <if test = "dto.senderAccount != null and dto.senderAccount != '' ">
            AND (MPSR.SENDER_ACCOUNT = #{dto.senderAccount} or MPSR.SENDER_INFO LIKE '%'||#{dto.senderAccount}||'%')
        </if>
        <if test = "dto.triggerType != null">
            AND MPSR.TRIGGER_TYPE = #{dto.triggerType}
        </if>
        <if test="dto.sentDateStart != null and dto.sentDateStart != ''">
            AND MPSR.SENT_TIME <![CDATA[ >= ]]> to_date(#{dto.sentDateStart}, 'yyyy-MM-dd')
        </if>
        <if test="dto.sentDateEnd != null and dto.sentDateEnd != ''">
            AND MPSR.SENT_TIME <![CDATA[ < ]]> (to_date(#{dto.sentDateEnd}, 'yyyy-MM-dd')+1)
        </if>
    </sql>
    <select id="getPassengerSmsRecordPage" resultType="com.swcares.aps.msg.model.entity.PassengerSmsRecord" databaseId="oracle">
        <include refid="passengerSmsRecordDetail"></include>
        <include refid="searchCriteriaOracle"></include>
        ORDER BY SENT_TIME DESC
    </select>

    <select id="getByIds" resultType="com.swcares.aps.msg.model.entity.PassengerSmsRecord">
        <include refid="passengerSmsRecordDetail"></include>
        WHERE
        MPSR.ID IN
        <foreach collection="idList" index="index" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="messageStatusStatics" resultType="com.swcares.aps.msg.model.dto.MessageStatusStaticsDTO">
        select status, count(id) recordCount
        from MSG_PASSENGER_SMS_RECORD MPSR
        <include refid="searchCriteriaOracle"></include>
        group by status
    </select>
</mapper>
