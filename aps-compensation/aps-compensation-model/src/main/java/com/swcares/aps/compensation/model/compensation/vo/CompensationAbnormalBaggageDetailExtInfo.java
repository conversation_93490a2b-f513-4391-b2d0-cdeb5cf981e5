package com.swcares.aps.compensation.model.compensation.vo;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CompensationAbnormalBaggageDetailExtInfo
 * @Description：补偿单详情信息，异常行李补偿单详情信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 唐康
 * @Date： 2022/4/18 17:20
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationAbnormalBaggageDetailExtInfo对象", description="现金补偿单（异常行李）详情VO")
public class CompensationAbnormalBaggageDetailExtInfo extends CompensationDetailExtInfoVO {
    @ApiModelProperty(value = "补偿旅客")
    private String paxName;

    @ApiModelProperty(value = "补偿金额（旅客自定义的）")
    private String cpsNum;

    /**
     * 异常行李事故信息
     */
    @ApiModelProperty(value = "现金补偿单（异常行李）事故信息")
    private BaggageAccidentInfoDO baggageAccidentInfoDO;
}
