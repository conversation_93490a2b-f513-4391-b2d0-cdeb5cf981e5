/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：FlightEventHandler <br>
 * Package：com.swcares.reaptv.msg.service.impl <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.flightevent;

import com.swcares.aps.msg.config.MessageCenterErrors;
import com.swcares.aps.msg.model.entity.*;
import com.swcares.aps.msg.service.*;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/*
 * ClassName：com.swcares.reaptv.msg.flightevent.FlightEventHandler <br>
 * Description：对航变事件消息进行处理的组件
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/4 21:06 <br>
 * @version:
 */
@Component
@Slf4j
public class FlightEventHandler {

    @Autowired FlightEventSourceMessageService flightEventSourceMessageService;
    
    @Autowired
    private RedissonClient redissonClient;
    
    @Autowired
    private FlightEventIntegrator flightEventIntegrator;
    

    
    private static final String FLIGHT_EVENT_SOURCE_MESSAGE_LOCK_PREFIX = "MSG:FLIGHT_EVENT_SOURCE_MESSAGE:";

    /**  
     * Title：handleAfterCreate <br>
     * Description：在接收到航变事件后立即异步处理事件消息
     * @param flightEventSourceMessage : 
     * @Return void
     * @throws
     */  
    @Async
    public void handleMessageAsync(FlightEventSourceMessage flightEventSourceMessage) {
        log.info("对航变消息{}进行异步处理",flightEventSourceMessage.getId());
        doProcess(flightEventSourceMessage);
    }
    
    private void markSourceMessageToProcessed(FlightEventSourceMessage sourceMessage, String result){
        try {
            sourceMessage.setResult(result);
            flightEventSourceMessageService.markAsProcessed(sourceMessage);
        } catch (Exception e){
            log.error("无法将航变原始消息{}标记为已处理", sourceMessage.getId());
        }
    }
    
    public void handleMessage(FlightEventSourceMessage flightEventSourceMessage){
        log.info("对航变消息{}进行处理",flightEventSourceMessage.getId());
        doProcess(flightEventSourceMessage);
    }

    private void doProcess(FlightEventSourceMessage flightEventSourceMessage) {
        RLock processLock =
                redissonClient.getLock(FLIGHT_EVENT_SOURCE_MESSAGE_LOCK_PREFIX + flightEventSourceMessage.getId());

        try {
            if (processLock.tryLock()) {
                FlightEventSourceMessage messageInDB = flightEventSourceMessageService.getById(flightEventSourceMessage.getId());
                if (messageInDB.isProcessed()) {
                    log.info("航变消息{}已经被其他进程处理了，跳过该消息", flightEventSourceMessage.getId());
                } else {
                    flightEventIntegrator.integrateFlightEvent(flightEventSourceMessage);
                    markSourceMessageToProcessed(flightEventSourceMessage, null);
                    log.info("异步处理航变消息{}完成", flightEventSourceMessage.getId());
                }
            } else {
                log.info("航变消息{}正在被其他的进程处理，跳过该消息。", flightEventSourceMessage.getId());
            }
        }catch (Exception e) {
                markSourceMessageToProcessed(flightEventSourceMessage, e.getMessage());
                log.error("异步处理航变消息{}失败:{}", flightEventSourceMessage.getId(), e);
        } finally {
            if (processLock.isHeldByCurrentThread()) {
                processLock.unlock();
            }
        }
    }



}
