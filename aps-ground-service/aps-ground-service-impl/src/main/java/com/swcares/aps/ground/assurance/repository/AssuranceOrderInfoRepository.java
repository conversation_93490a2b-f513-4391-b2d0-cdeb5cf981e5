package com.swcares.aps.ground.assurance.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.ground.models.assurance.domain.AssuranceOrderDomain;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderInfoDO;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderServiceVerifyDO;

import java.util.List;
import java.util.Set;

/**
 * @ClassName：AssuranceOrderInfoRepository
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/21 15:32
 * @version： v1.0
 */
public interface AssuranceOrderInfoRepository extends IService<AssuranceOrderInfoDO> {

    void saveDomain(AssuranceOrderDomain orderDomain);

    void updateDomain(AssuranceOrderDomain orderDomain);

    void delete(AssuranceOrderDomain orderDomain);

    void updateDoInfo(AssuranceOrderInfoDO orderInfoDO);

    void updateOrderServiceVerifyDOInfo(List<AssuranceOrderServiceVerifyDO> assuranceOrderServiceVerifyDOS);

    void refreshServiceVerifyAmount(Set<String> orderServiceIds);
}
