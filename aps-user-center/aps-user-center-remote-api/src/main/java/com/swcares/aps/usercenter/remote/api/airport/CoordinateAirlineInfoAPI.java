package com.swcares.aps.usercenter.remote.api.airport;

import com.swcares.aps.cpe.coordinate.model.dispatcher.dto.BasicDataDispatchDTO;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.components.bd.entity.AirlineInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> <PERSON>
 * @Classname AirlineInfoAPI
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/24 08:54
 * @Version 1.0
 */
@FeignClient(name = "aps-user-center", path = "/api/aps-user-center", contextId = "airlineInfoApi")
public interface CoordinateAirlineInfoAPI {
    @PostMapping("/coordinate/airlineinfo/saveOrUpdate")
    public BaseResult<AirlineInfo> saveOrUpdateAirlineInfo(@RequestBody BasicDataDispatchDTO requestDTO);
}
