package com.swcares.aps.basic.data.remoteapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.aps.basic.data.businessimpl.model.vo.BaggagePaxInfoVO;
import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.entity.FltPassengerBaggageInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：FltPassengerBaggageInfo <br>
 * Package：com.swcares.reaptv.flightpassenger.impl.passenger.mapper <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2022年 04月21日 9:20 <br>
 * @version v1.0 <br>
 */
@Mapper
public interface FltPassengerBaggageInfoMapper extends BaseMapper<FltPassengerBaggageInfo> {


    /**
     * @title findBaggageNumberById
     * @description 旅客id获取行李信息
     * <AUTHOR>
     * @date 2024/5/20 11:37
     * @param paxId
     * @return java.util.List<com.swcares.aps.basic.data.businessimpl.model.vo.BaggagePaxInfoVO>
     */
    List<BaggagePaxInfoVO> findBaggageNumberById(@Param("id") String paxId);
    /**
     * @title findPassengerByBagTag
     * @description 根据行李号获取旅客信息
     * <AUTHOR>
     * @date 2024/5/20 11:37
     * @param bagTag
     * @return java.util.List<com.swcares.aps.basic.data.businessimpl.model.vo.BaggagePaxInfoVO>
     */
    List<BaggagePaxInfoVO> findPassengerByBagTag(String bagTag);

    /**
     * @title findPassengers
     * @description 旅客信息获取旅客信息
     * <AUTHOR>
     * @date 2024/5/20 11:38
     * @param dto
     * @return java.util.List<com.swcares.aps.basic.data.businessimpl.model.vo.BaggagePaxInfoVO>
     */
    List<BaggagePaxInfoVO> findPassengers(@Param("dto") PassengerQueryDTO dto);

}
