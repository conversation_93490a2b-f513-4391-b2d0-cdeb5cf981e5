package com.swcares.aps.ground.assurance.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.component.workflow.dto.AuditorInfoDTO;
import com.swcares.aps.ground.assurance.repository.impl.AssuranceOrderDomainFactory;
import com.swcares.aps.ground.assurance.service.AssuranceOrderManageService;
import com.swcares.aps.ground.assurance.service.AssuranceWorkflowDomainService;
import com.swcares.aps.ground.models.assurance.domain.AssuranceOrderDomain;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderServiceListRequest;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderListRequest;
import com.swcares.aps.ground.models.assurance.dto.AssuranceVerifyQueryRequest;
import com.swcares.aps.ground.models.assurance.vo.*;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.security.UserContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName：AssuranceQueryController
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/21 16:30
 * @version： v1.0
 */
@RestController
@RequestMapping("/assurance/busi/query")
@Api(tags = {"保障单业务信息查询接口"})
@ApiVersion({"保障单相关Api"})
@Slf4j
public class AssuranceQueryController  extends BaseController {

    @Autowired
    private AssuranceOrderManageService assuranceOrderManageService;

    @Autowired
    private AssuranceOrderDomainFactory assuranceOrderDomainFactory;

    @Autowired
    private AssuranceWorkflowDomainService assuranceWorkflowDomainService;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询供保障单列表信息")
    public PagedResult<List<AssuranceOrderListVO>> pageQueryOrderList(@RequestBody AssuranceOrderListRequest request){
        IPage<AssuranceOrderListVO> result=assuranceOrderManageService.pageQueryOrderList(request);
        return ok(result);
    }

    @GetMapping("/get/{id}")
    @ApiOperation(value = "获取保障单全量详情信息")
    public BaseResult<AssuranceOrderDomain> getFullAssuranceOrderInfo(@PathVariable @ApiParam(value = "主键id", required = true) String id){
        AssuranceOrderDomain orderDomain = assuranceOrderDomainFactory.createById(id);
        return ok(orderDomain);
    }

    @PostMapping("/workflow/current/auth")
    public BaseResult<List<AssuranceWorkflowAuditorOrderVO>> getCurrentUserAuthOrders(@RequestBody List<String> orderIds){
        String userId = String.valueOf(UserContext.getUserId());
        List<AssuranceWorkflowAuditorOrderVO> assuranceWorkflowAuditorOrderVOS = assuranceWorkflowDomainService.getOrderAuthUsers(orderIds,userId);
        return ok(assuranceWorkflowAuditorOrderVOS);
    }

    @GetMapping("/workflow/findReviewer")
    @ApiOperation(value = "条件查询可选审核人，前端调用")
    public BaseResult<List<AuditorInfoDTO>> findReviewer(@ApiParam(value = "保障单id")String orderId) {
        List<AuditorInfoDTO> reviewerVOS=assuranceWorkflowDomainService.findReviewer(null,orderId);
        return ok(reviewerVOS);
    }

    @GetMapping("/workflow/findAuditHistory")
    @ApiOperation(value = "查看审核记录")
    public BaseResult<List<AssuranceWorkflowAuditHistoryVO>> findAuditHistory(@ApiParam(value = "保障单id")String orderId) {
        List<AssuranceWorkflowAuditHistoryVO> auditHistoryVOS=assuranceWorkflowDomainService.findAuditHistory(orderId);
        return ok(auditHistoryVOS);
    }

    @PostMapping("/orderService/page")
    @ApiOperation(value = "保障单服务项列表信息")
    public PagedResult<List<AssuranceOrderServiceListVO>> pageQueryOrderServiceList(@RequestBody AssuranceOrderServiceListRequest request){
        IPage<AssuranceOrderServiceListVO> result =assuranceOrderManageService.pageQueryOrderServiceList(request);
        return ok(result);
    }

    @GetMapping("/orderService/get/{id}")
    @ApiOperation(value = "保障单服务项详情信息")
    public BaseResult<AssuranceOrderServiceDetailsVo> getOrderServiceDetails(@PathVariable @ApiParam(value = "主键id", required = true) String id){
        return ok(assuranceOrderManageService.getOrderServiceDetail(id));
    }

    @PostMapping("/verify/page")
    @ApiOperation(value = "旅客权益核销信息查询")
    public PagedResult<List<AssuranceOrderServiceVerifyUserDetailVO>> getOrderVerifyPage(@RequestBody AssuranceVerifyQueryRequest request){
        return ok(assuranceOrderManageService.getOrderServiceVerifyDetailList(request));
    }

    @GetMapping("/orderService/distinctServiceName")
    @ApiOperation(value = "保障单服务项中所有的服务项名称")
    public BaseResult<List<String>> getDistinctServiceName(){
        return ok(assuranceOrderManageService.getDistinctServiceName());
    }

}
