package com.swcares.aps.compensation.model.irregularflight.vo;

import com.swcares.baseframe.utils.lang.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.ObjectUtils;

import java.util.*;

/**
 * ClassName：com.swcares.aps.compensating.mode.irregularflight.vo.AccidentFindSegmentVO <br>
 * Description：新建事故单航班返回对象 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021/10/28 15:53 <br>
 * @version v1.0 <br>
 */

@ApiModel(value="AccidentFindFlightVO对象", description="")
public class AccidentFindFlightVO {

    @ApiModelProperty(value = "航班ID")
    private String flightId;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "航班类型")
    private String flightType;

    @ApiModelProperty(value = "起飞机场")
    private String departPort;

    @ApiModelProperty(value = "到达机场")
    private String arrivalPort;

    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    @ApiModelProperty(value = "预计起飞时间")
    private String etd;

    @ApiModelProperty(value = "实际起飞时间")
    private String atd;

    @ApiModelProperty(value = "机号")
    private String acReg;

    @ApiModelProperty(value = "机型")
    private String acType;

    @ApiModelProperty(value = "实际到达时间")
    private String ata;

    @ApiModelProperty(value = "计划到达时间")
    private String sta;

    @ApiModelProperty(value = "预计到达时间")
    private String eta;

    @ApiModelProperty(value = "订座数")
    private String bookSeat;

    @ApiModelProperty(value = "总座数")
    private String seatNum;

    @ApiModelProperty(value = "航班状态")
    private String flightRunState;

    @ApiModelProperty(value = "延误原因（对内对外）")
    private List<Map<String,String>> delayReason;

    @ApiModelProperty(value = "航班状态 D-延误|C-取消")
    private String flightStatus;

    @ApiModelProperty(value = "备降/返航标志,RC 返航 VC 备降")
    private String flgVr;

    @ApiModelProperty(value = "不正常对内原因")
    private String innerReason;

    @ApiModelProperty(value = "不正常对外原因")
    private String exterReason;

    @ApiModelProperty(value = "起飞三字码")
    private String pod;

    @ApiModelProperty(value = "到达三字码")
    private String poa;

    @ApiModelProperty(value = "航段三字码")
    private String segment;

    @ApiModelProperty(value = "航段中文")
    private String segmentCh;

    @ApiModelProperty(value = "经停站")
    private Set<String> stopoverStation;

    /** 为了方便让前端进行计算，添加三个字段  */
    @ApiModelProperty(value = "计划起飞时间（含日期）")
    private String stdDate;

    @ApiModelProperty(value = "预计起飞时间（含日期）")
    private String etdDate;

    @ApiModelProperty(value = "实际起飞时间（含日期）")
    private String atdDate;
//    private String vAbnormalReason;//返航
//    private String rAbnormalReason;//备降
//    private String csAbnormalReason;//取消
//    private String delayAbnormalReason;//延误

    public String getFlightRunState() {
        return flightRunState;
    }

    public void setFlightRunState(String flightRunState) {
        this.flightRunState = flightRunState;
    }

    public String getStdDate() {
        return stdDate;
    }

    public void setStdDate(String stdDate) {
        this.stdDate = stdDate;
    }

    public String getEtdDate() {
        return etdDate;
    }

    public void setEtdDate(String etdDate) {
        this.etdDate = etdDate;
    }

    public String getAtdDate() {
        return atdDate;
    }

    public void setAtdDate(String atdDate) {
        this.atdDate = atdDate;
    }

    public String getFlightId() {
        return flightId;
    }

    public void setFlightId(String flightId) {
        this.flightId = flightId;
    }

    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo;
    }

    public String getFlightDate() {
        return flightDate;
    }

    public void setFlightDate(String flightDate) {
        String date = flightDate;
        if(ObjectUtils.isNotEmpty(flightDate)){
            date = DateUtils.formatDate(DateUtils.parseDate(flightDate), DateUtils.PTN_YMD);
        }
        this.flightDate = date;
    }

    public String getFlightType() {
        return flightType;
    }

    public void setFlightType(String flightType) {
        this.flightType = flightType;
    }

    public String getDepartPort() {
        return departPort;
    }

    public void setDepartPort(String departPort) {
        this.departPort = departPort;
    }

    public String getArrivalPort() {
        return arrivalPort;
    }

    public void setArrivalPort(String arrivalPort) {
        this.arrivalPort = arrivalPort;
    }

    public String getStd() {
        return std;
    }

    public void setStd(String std) {
        String date = std;
        if(ObjectUtils.isNotEmpty(std)){
            date = DateUtils.formatDate(DateUtils.parseDate(std), "HH:mm");
        }
        this.std = date;
    }

    public String getEtd() {
        return etd;
    }

    public void setEtd(String etd) {
        String date = etd;
        if(ObjectUtils.isNotEmpty(etd)){
            date = DateUtils.formatDate(DateUtils.parseDate(etd), "HH:mm");
        }
        this.etd = date;
    }

    public String getAtd() {
        return atd;
    }

    public void setAtd(String atd) {
        String date = atd;
        if(ObjectUtils.isNotEmpty(atd)){
            date = DateUtils.formatDate(DateUtils.parseDate(atd), "HH:mm");
        }
        this.atd = date;
    }

    public String getAcReg() {
        return acReg;
    }

    public void setAcReg(String acReg) {
        this.acReg = acReg;
    }

    public String getAcType() {
        return acType;
    }

    public void setAcType(String acType) {
        this.acType = acType;
    }

    public String getAta() {
        return ata;
    }

    public void setAta(String ata) {
        String date = ata;
        if(ObjectUtils.isNotEmpty(ata)){
            date = DateUtils.formatDate(DateUtils.parseDate(ata), "HH:mm");
        }
        this.ata = date;
    }

    public String getSta() {
        return sta;
    }

    public void setSta(String sta) {
        String date = sta;
        if(ObjectUtils.isNotEmpty(sta)){
            date = DateUtils.formatDate(DateUtils.parseDate(sta), "HH:mm");
        }
        this.sta = date;
    }

    public String getEta() {
        return eta;
    }

    public void setEta(String eta) {
        String date = eta;
        if(ObjectUtils.isNotEmpty(eta)){
            date = DateUtils.formatDate(DateUtils.parseDate(eta), "HH:mm");
        }
        this.eta = date;
    }

    public String getBookSeat() {
        return bookSeat;
    }

    public void setBookSeat(String bookSeat) {
        this.bookSeat = bookSeat;
    }

    public String getSeatNum() {
        return seatNum;
    }

    public void setSeatNum(String seatNum) {
        this.seatNum = seatNum;
    }

    public String getFlightStatus() {
        return flightStatus;
    }

    public void setFlightStatus(String flightStatus) {
        this.flightStatus = flightStatus;
    }

    public List<Map<String, String>> getDelayReason() {
        return delayReason;
    }

    public void setDelayReason(List<Map<String, String>> delayReason) {
        this.delayReason = delayReason;
    }

    public String getPod() {
        return pod;
    }

    public void setPod(String pod) {
        this.pod = pod;
    }

    public String getPoa() {
        return poa;
    }

    public void setPoa(String poa) {
        this.poa = poa;
    }

    public String getSegment() {
        return segment;
    }

    public void setSegment(String segment) {
        this.segment = segment;
    }

    public String getSegmentCh() {
        return segmentCh;
    }

    public void setSegmentCh(String segmentCh) {
        this.segmentCh = segmentCh;
    }

    public Set<String> getStopoverStation() {
        return stopoverStation;
    }

    public void setStopoverStation(Set<String> stopoverStation) {
        this.stopoverStation = stopoverStation;
    }

    public String getFlgVr() {
        return flgVr;
    }

    public void setFlgVr(String flgVr) {
        this.flgVr = flgVr;
    }

    public String getInnerReason() {
        return innerReason;
    }

    public void setInnerReason(String innerReason) {
        this.innerReason = innerReason;
    }

    public String getExterReason() {
        return exterReason;
    }

    public void setExterReason(String exterReason) {
        this.exterReason = exterReason;
    }
}
