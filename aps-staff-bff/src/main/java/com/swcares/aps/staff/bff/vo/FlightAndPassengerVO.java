package com.swcares.aps.staff.bff.vo;

import com.swcares.aps.compensation.model.irregularflight.vo.AccidentFindFlightVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderPaxInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName： FlightAndPassengerVO <br>
 * Description： <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/7 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "航班旅客VO",description = "")
public class FlightAndPassengerVO {

	@ApiModelProperty(value = "异常行李旅客信息")
	private CompensationOrderPaxInfoVO compensationOrderPaxInfoVO;

	@ApiModelProperty(value = "异常行李航班信息")
	private AccidentFindFlightVO accidentFindFlightVO;
}
