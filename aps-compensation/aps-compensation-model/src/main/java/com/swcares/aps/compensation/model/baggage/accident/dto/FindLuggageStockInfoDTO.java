package com.swcares.aps.compensation.model.baggage.accident.dto;


import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: FindLuggageStockInfoDTO
 * @projectName aps
 * @description: 查询箱包库存管理记录信息封装类
 * @date 2022/2/9 09:23
 */
@Data
@ApiModel(value="查询箱包库存管理记录信息封装DTO", description="查询箱包库存管理记录信息封装DTO")
public class FindLuggageStockInfoDTO extends PagedDTO {

    @ApiModelProperty(value = "箱包Id")
    private Long luggageId;

    @ApiModelProperty(value = "箱包编号")
    private String luggageNo;

    @ApiModelProperty(value = "执行动作(1:新建2:减少3:添加)")
    private String status;

    @ApiModelProperty(value = "操作人（姓名+工号）")
    private String executor;

    @ApiModelProperty(value = "操作起始日期")
    private String executionStartTime;

    @ApiModelProperty(value = "操作结束日期")
    private String executionEndTime;



}
