package com.swcares.aps.compensation.model.irregularflight.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * ClassName：com.swcares.aps.compensating.mode.irregularflight.vo <br>
 * Description：赔偿单已选旅客信息 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 10月29日 10:14 <br>
 * @version v1.0 <br>
 */
@Data
@SecretInfoEntity
@ApiModel(value="CompensationChoicePaxVO对象", description="赔偿单已选旅客信息")
public class CompensationChoicePaxVO {

    @ApiModelProperty(value = "赔偿单ID")
    private Long orderId;

    @ApiModelProperty(value = "旅客ID")
    private String paxId;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "证件类型—>取数据字典")
    private String idType;

    @ApiModelProperty(value = "证件号码")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "性别 M(男) F(女)C(儿童)—>取数据字典")
    private String sex;

    @ApiModelProperty(value = "手机号")
    @SecretValue
    private String telephone;

    @ApiModelProperty(value = "当前领取金额")
    private String currentAmount;

    @ApiModelProperty(value = "儿童旅客标识")
    private String isChild;

    @ApiModelProperty(value = "默认N,Y携带")
    private String withBaby;

    @ApiModelProperty(value = "携带婴儿姓名")
    private String babyPaxName;

    @ApiModelProperty(value = "申领次数")
    private String applyCount;

    @ApiModelProperty(value = "总补偿次数")
    private String payCount;

    @ApiModelProperty(value = "航段中文")
    private String segmentCh;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "主仓位")
    private String mainClass;

    @ApiModelProperty(value = "子仓位")
    private String subClass;

    @ApiModelProperty(value = "PNR票号")
    private String pnr;

    @ApiModelProperty(value = "旅客状态")
    private String paxStatus;

    @ApiModelProperty(value = "领取状态(0未领取,1已领取,2领取中)—>取数据字典")
    private String receiveStatus;

    @ApiModelProperty(value = "旅客申领资格开关(默认0有资格，1=冻结)")
    private String switchOff;

    @ApiModelProperty(value = "购票时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime tktIssueDate;

    @ApiModelProperty(value = "旅客取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date cancelTime;

    @ApiModelProperty(value = "支付状态 —>取数据字典 暂时没有")
    private String payStatus;

    @ApiModelProperty(value = "支付失败原因")
    private String payFailRemark;

    @ApiModelProperty(value = "是否标红 0否 1是")
    private String isMarkedRed;


}
