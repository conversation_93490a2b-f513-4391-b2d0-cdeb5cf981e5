package com.swcares.aps.web.bff.controller;

import com.swcares.aps.compensation.model.privilege.dto.CooperativeCustomerDTO;
import com.swcares.aps.compensation.model.privilege.dto.SearchCooperativeCustomerDTO;
import com.swcares.aps.compensation.model.privilege.enums.CustomerCategoryEnum;
import com.swcares.aps.compensation.remote.api.irregularflight.CooperativeCustomerApi;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> <PERSON> Yi
 * @Classname CooperativeCustomerController
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/20 10:14
 * @Version 1.0
 */
@RestController
@RequestMapping("/cooperativeCustomer")
public class CooperativeCustomerController extends BaseController {
    @Autowired
    CooperativeCustomerApi cooperativeCustomerApi;

    @GetMapping("/availableAirports")
    public BaseResult<List<CooperativeCustomerDTO>> getAvailableAirportList(){
        SearchCooperativeCustomerDTO searchCriteria = new SearchCooperativeCustomerDTO();
        searchCriteria.setStatus("0");
        searchCriteria.setCustomerCategory(CustomerCategoryEnum.AIRPORT.getCode());
        return cooperativeCustomerApi.list(searchCriteria);
    }
}
