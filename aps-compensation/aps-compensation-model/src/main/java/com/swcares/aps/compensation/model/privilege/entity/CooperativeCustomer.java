package com.swcares.aps.compensation.model.privilege.entity;

import com.swcares.baseframe.common.base.entity.BaseEntity;
import lombok.Data;

/**
 * <AUTHOR> <PERSON>
 * @Classname CooperativeCustomer 可以协作的客户信息
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/20 09:02
 * @Version 1.0
 */
@Data
public class CooperativeCustomer extends BaseEntity {
    private String code;

    private String name;

    private String shortName;

    private String customerCategory;

    private boolean disabled;

    private String remark;
}
