package com.swcares.aps.compensation.model.irregularflight.vo;

import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * ClassName：com.swcares.aps.compensation.model.irregularflight.vo <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 01月25日 17:22 <br>
 * @version v1.0 <br>
 */

@Data
@ApiModel(value="FlightExistsAccidentVO对象", description="赔偿航班信息")
@SecretInfoEntity
public class PaxCompensationCountVO {

    @ApiModelProperty(value = "赔偿单单号")
    private String orderNo;

    @ApiModelProperty(value = "补偿方式 1现金，2虚拟，3实物")
    private String compensateType;

    @ApiModelProperty(value = "事故单类型- 1.不正常航班 2异常行李 3超售")
    private String accidentType;

    @ApiModelProperty(value = "0草稿 1驳回 2审核中 3审核通过 4生效 5关闭 6逾期 7审核不通过")
    private String status;

    @ApiModelProperty(value = "旅客申领资格开关(默认0有资格，1取消领取资格=冻结)")
    private String switchOff;

    @ApiModelProperty(value = "本次申领金额")
    private String sumMoney;

    @ApiModelProperty(value = "电话")
    @SecretValue(separator = ",")
    private String tel;

    @ApiModelProperty(value = "领取状态(0未领取,1已领取,2处理中3已逾期)")
    private String receiveStatus;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

}
