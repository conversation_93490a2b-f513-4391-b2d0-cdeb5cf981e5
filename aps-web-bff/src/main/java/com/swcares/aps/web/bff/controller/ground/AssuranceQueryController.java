package com.swcares.aps.web.bff.controller.ground;

import com.swcares.aps.component.workflow.dto.AuditorInfoDTO;
import com.swcares.aps.ground.AssuranceQueryApi;
import com.swcares.aps.ground.enums.AssuranceOrderState;
import com.swcares.aps.ground.models.assurance.domain.AssuranceOrderDomain;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderListRequest;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderEditVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderFullDetailVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderListVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceWorkflowAuditHistoryVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceWorkflowAuditorOrderVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName：AssuranceQueryController
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/21 16:30
 * @version： v1.0
 */
@RestController
@RequestMapping("/assurance/busi/query")
@Api(tags = {"保障单业务信息查询接口"})
@ApiVersion({"保障单相关Api"})
public class AssuranceQueryController extends BaseController {

    @Autowired
    private AssuranceQueryApi assuranceQueryApi;

    @PostMapping("/page")
    @ApiOperation(value = "分页查询供保障单列表信息")
    public PagedResult<List<AssuranceOrderListVO>> pageQueryOrderList(@RequestBody AssuranceOrderListRequest request){
        PagedResult<List<AssuranceOrderListVO>> result = assuranceQueryApi.pageQueryOrderList(request);
        List<AssuranceOrderListVO> claimOrderListVOS = result.getData();
        if(CollectionUtils.isEmpty(claimOrderListVOS)){return result;}
        List<String> orderIds = claimOrderListVOS
                .stream()
                .filter(t-> StringUtils.equals(AssuranceOrderState.AUDIT_ING.getCode(),t.getOrderState()))
                .map(AssuranceOrderListVO::getId)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(orderIds)){ return result; }
        List<AssuranceWorkflowAuditorOrderVO> workflowAuditorOrderVOS = assuranceQueryApi.
                getCurrentUserAuthOrders(orderIds).getData();
        if(CollectionUtils.isEmpty(workflowAuditorOrderVOS)){return result;}

        Map<String, String> orderIdToTaskId = workflowAuditorOrderVOS.
                stream().
                collect(Collectors.toMap(AssuranceWorkflowAuditorOrderVO::getOrderId, AssuranceWorkflowAuditorOrderVO::getTaskId, (o, n) -> n));
        claimOrderListVOS.forEach(t->{
            if(!orderIdToTaskId.containsKey(t.getId())){return;}
            String taskId = orderIdToTaskId.get(t.getId());
            t.setTaskId(taskId);
            t.setNeedAudit("Y");
        });
        return result;
    }

    @GetMapping("/edit/{id}")
    @ApiOperation(value = "编辑时获取保障单信息")
    public BaseResult<AssuranceOrderEditVO> getEditAssuranceOrderInfo(@PathVariable @ApiParam(value = "主键id", required = true) String id){
        AssuranceOrderDomain orderDomain = assuranceQueryApi.getFullAssuranceOrderInfo(id).getData();
        if(orderDomain==null){return ok(); }
        AssuranceOrderEditVO vo = AssuranceOrderEditVO.createByDomain(orderDomain);
        return ok(vo);
    }

    @GetMapping("/get/{id}")
    @ApiOperation(value = "查看详情时获取保障单信息")
    public BaseResult<AssuranceOrderFullDetailVO> getFullAssuranceOrderInfo(@PathVariable @ApiParam(value = "主键id", required = true) String id){
        AssuranceOrderDomain orderDomain = assuranceQueryApi.getFullAssuranceOrderInfo(id).getData();
        if(orderDomain==null){return ok(); }
        AssuranceOrderFullDetailVO vo = AssuranceOrderFullDetailVO.createPcDetailVOByDomain(orderDomain);
        if(AssuranceOrderState.AUDIT_ING.getCode().equals(orderDomain.getDomainState())){
            List<AssuranceWorkflowAuditorOrderVO> workflowAuditorOrderVOS = assuranceQueryApi.
                    getCurrentUserAuthOrders(Collections.singletonList(orderDomain.getDomainId())).getData();
            if(CollectionUtils.isNotEmpty(workflowAuditorOrderVOS)){
                vo.setCurrentTaskId(workflowAuditorOrderVOS.get(0).getTaskId());
                vo.setNeedAudit("Y");
            }
        }
        return ok(vo);
    }

    @GetMapping("/workflow/findReviewer")
    @ApiOperation(value = "条件查询可选审核人，前端调用")
    public BaseResult<List<AuditorInfoDTO>> findReviewer(@ApiParam(value = "部门id") Long orgId, @ApiParam(value = "节点id",required=true) String taskId, @ApiParam(value = "审核人信息：姓名/工号") String userInfo,@ApiParam(value = "赔偿单id")String orderId) {
        BaseResult<List<AuditorInfoDTO>> reviewerResult = assuranceQueryApi.findReviewer(orderId);
        List<AuditorInfoDTO> reviewerVOS = reviewerResult.getData();
        if(CollectionUtils.isEmpty(reviewerVOS)){return reviewerResult;}

        if(orgId!=null){
            reviewerVOS=reviewerVOS.stream().filter(t->t.getOrgId().longValue()==orgId.longValue()).collect(Collectors.toList());
            reviewerResult.setData(reviewerVOS);
        }

        if(CollectionUtils.isEmpty(reviewerVOS)){return reviewerResult;}
        if(StringUtils.isNotBlank(userInfo)){
            reviewerVOS = reviewerVOS.stream().filter(t -> StringUtils.contains(t.getReviewerNameNo(), userInfo)).collect(Collectors.toList());
            reviewerResult.setData(reviewerVOS);
        }

       return reviewerResult;
    }

    @GetMapping("/workflow/findAuditHistory")
    @ApiOperation(value = "查看审核记录")
    public BaseResult<List<AssuranceWorkflowAuditHistoryVO>> findAuditHistory(@ApiParam(value = "保障单id")String orderId) {
        return assuranceQueryApi.findAuditHistory(orderId);
    }
}
