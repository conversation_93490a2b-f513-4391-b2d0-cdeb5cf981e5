package com.swcares.aps.compensation.model.dataconfig.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.aps.compensation.model.replace.vo.ReplaceRuleVO <br>
 * Description：返回展示对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-10 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="ReplacePayPeriodRuleVO对象", description="代人领取支付等待期规则VO")
public class ReplacePayPeriodRuleVO {

    @ApiModelProperty(value = "支付等待期，默认值为 5分钟，最大允许设置60分钟",example = "5")
    private Integer paymentWaitingPeriod;
}
