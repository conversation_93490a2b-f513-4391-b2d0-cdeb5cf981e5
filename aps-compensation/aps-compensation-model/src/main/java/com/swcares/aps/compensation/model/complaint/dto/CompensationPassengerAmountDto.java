package com.swcares.aps.compensation.model.complaint.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "CompensationPassengerAmountDto对象", description = "旅客赔偿金额对照信息")
public class CompensationPassengerAmountDto {

    @ApiModelProperty(value = "客票号",required = true)
    @NotBlank(message = "客票号不能为空")
    private String ticketNo;

    @ApiModelProperty(value = "旅客姓名",required = true)
    @NotBlank(message = "旅客姓名不能为空")
    private String passengerName;

    @ApiModelProperty(value = "当前金额",required = true)
    @NotBlank(message = "当前金额不能为空")
    private String currentAmount;
}
