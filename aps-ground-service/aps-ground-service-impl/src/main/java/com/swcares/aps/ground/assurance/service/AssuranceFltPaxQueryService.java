package com.swcares.aps.ground.assurance.service;

import com.swcares.aps.basic.data.remoteapi.model.dto.FlightInfoDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightUnitInfoVO;
import com.swcares.aps.ground.models.assurance.dto.PaxQueryDTO;
import com.swcares.aps.ground.models.assurance.vo.AssurancePassengerVO;
import com.swcares.aps.ground.models.assurance.vo.ProtectFltInfoVO;

import java.util.List;

/**
 * @ClassName：FlightPassengerQueryService
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/22 10:16
 * @version： v1.0
 */
public interface AssuranceFltPaxQueryService {

    ProtectFltInfoVO getFltInfo(String flightNo, String flightDate);

    List<AssurancePassengerVO> queryPax(PaxQueryDTO dto);

    List<FlightUnitInfoVO> getFlightUnitInfo(List<FlightInfoDTO> flightInfoDTO);
}
