package com.swcares.aps.compensation.model.irregularflight.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * ClassName：com.swcares.irregularflight.entity.DpFlightAccidentInfo <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-14 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="FlightAccidentInfo对象", description="")
@TableName("flight_accident_info")
public class FlightAccidentInfoDO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private Long tenantId;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "事故单状态（0草稿、1待处理、2处理中、3已结案、4作废）")
    @NotNull
    private String accidentStatus;

    @ApiModelProperty(value = "事故单类型- 1.不正常航班 2异常行李 3超售")
    @NotNull
    private String accidentType;

    @ApiModelProperty(value = "航变类型（1延误，2取消，3备降，4返航，5补班）")
    @NotNull
    private String fcType;

    @ApiModelProperty(value = "航变类型原因归属-字典枚举值fc_type_owner")
    private String  fcTypeOwner;

    @ApiModelProperty(value = "航班号")
    @NotNull
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    @NotNull
    private String flightDate;

    @ApiModelProperty(value = "航段")
    @NotNull
    private String segment;

    @ApiModelProperty(value = "航段-中文格式")
    @NotNull
    private String choiceSegmentCh;

    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    @ApiModelProperty(value = "预计起飞时间")
    private String etd;

    @ApiModelProperty(value = "实际起飞时间")
    private String atd;

    @ApiModelProperty(value = "延误时长")
    private String delayInterval;

    @ApiModelProperty(value = "事故原因")
    @Size(max = 1000)
    private String lateReason;

    @ApiModelProperty(value = "作废人ID")
    private String tovoidId;

    @ApiModelProperty(value = "作废时间")
    private LocalDateTime tovoidTime;

    @ApiModelProperty(value = "归属航司")
    private String belongAirline;

    @ApiModelProperty(value = "归属航司-中文简称")
    private String belongAirlineAbbr;

    @ApiModelProperty(value = "事故来源")
    private String accidentSource;
}
