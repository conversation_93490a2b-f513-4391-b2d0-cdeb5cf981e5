package com.swcares.aps.compensation.impl.baggage.accident.service.impl;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.component.workflow.dto.AuditorInfoDTO;
import com.swcares.aps.component.workflow.entity.WorkflowAuditorIdInfoDO;
import com.swcares.aps.component.workflow.enums.AuditStatusEnum;
import com.swcares.aps.component.workflow.service.WorkflowAuditorIdInfoService;
import com.swcares.aps.workflow.dto.*;
import com.swcares.aps.workflow.remote.api.util.WorkflowUtils;
import com.swcares.baseframe.common.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationConstant;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageTransportInfoDO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageWorkflowAuditResultVO;
import com.swcares.aps.component.workflow.entity.WorkflowModelCodeInfoDO;
import com.swcares.aps.component.workflow.enums.ApsProjectEnum;
import com.swcares.aps.component.workflow.service.WorkflowModelCodeInfoService;
import com.swcares.aps.workflow.remote.api.WorkflowApi;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.aps.compensation.model.privilege.enums.CustomerCategoryEnum;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 行李运输工作流领域服务
 * <p>
 * 该服务负责处理行李运输相关的工作流程，包括流程的启动、提交、审核等操作。
 * 主要功能包括：
 * 1. 启动行李运输工作流
 * 2. 处理工作流提交操作
 * 3. 管理工作流审核人
 * 4. 处理工作流节点状态转换
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-22
 */
@Component
@Slf4j
public class BaggageTransportationWorkflowDomainService {

    /**
     * 通用业务错误码
     */
    public final static int COMMON_ERROR = 51001;

    /**
     * 工作流API服务，用于与工作流引擎交互
     */
    @Autowired
    private WorkflowApi workflowApi;

    /**
     * 工作流模型代码信息服务，用于获取工作流模型配置
     */
    @Autowired
    private WorkflowModelCodeInfoService workflowModelCodeInfoService;

    /**
     * 工作流审核人信息服务，用于管理审核人信息
     */
    @Autowired
    private WorkflowAuditorIdInfoService workflowAuditorIdInfoService;

    /**
     * 启动行李运输流程引擎
     * <p>
     * 该方法负责初始化并启动行李运输工作流程。主要步骤包括：
     * 1. 获取当前用户和租户信息
     * 2. 查找并配置工作流模型
     * 3. 设置流程启动参数
     * 4. 调用工作流引擎启动流程
     * 5. 执行流程提交操作
     * </p>
     * 
     * @param baggageTransportInfoDO 行李运输信息对象，包含流程所需的业务数据
     * @return 工作流审核结果，包含下一步操作的相关信息
     */
    public BaggageWorkflowAuditResultVO startProcess(BaggageTransportInfoDO baggageTransportInfoDO) {
        log.info("【aps-compensation-impl】-行李运输工作流-startProcess【开始】启动流程实例, 行李事故ID[{}]", baggageTransportInfoDO.getId());

        // 获取当前用户ID
        String userId = String.valueOf(UserContext.getUserId());

        // 获取租户信息
        String tenantName = UserContext.getCurrentUser().getTenantName();
        String tenantCode = UserContext.getCurrentUser().getTenantCode();

        // 获取工作流模型配置
        WorkflowModelCodeInfoDO workflowModelCodeInfoDO = workflowModelCodeInfoService.findByProjectAndBusiness(
                ApsProjectEnum.BAGGAGE_IMPL.getProjectType(),
                CompensationConstant.BAGGAGE_TRANSPORTATION_BUSINESS);

        // 设置租户特定的模型代码
        workflowModelCodeInfoDO.setModelCode(workflowModelCodeInfoDO.getModelCode() + "_" + tenantCode);
        log.info("【aps-compensation-impl】在workflow_model_code_info报表中查找异常行李运输工作流模型配置表");

        // 设置租户相关的信息
        CustomerDTO customerDTO = new CustomerDTO();
        customerDTO.setCustomer(tenantCode);
        customerDTO.setCustomerCategory(CustomerCategoryEnum.AIRPORT.getCode());
        customerDTO.setBusinessName(tenantName);

        // 构建流程启动参数
        StartProcessParamsDTO startProcessParamsDTO = new StartProcessParamsDTO();
        startProcessParamsDTO.setAssignee("userId:" + userId);
        startProcessParamsDTO.setBusinessKey(String.valueOf(baggageTransportInfoDO.getId()));
        startProcessParamsDTO.setProcDefKey(workflowModelCodeInfoDO.getModelCode());
        startProcessParamsDTO.setCustomerDTO(customerDTO);
        startProcessParamsDTO.setProcessFlag(StartProcessParamsDTO.COMMON);

        // 使用Spring StopWatch记录执行时间
        StopWatch stopWatch = new StopWatch("启动行李运输工作流");
        stopWatch.start("调用workflow启动流程");

        // 调用workflow启动流程
        BaseResult<CurrentTaskActivityVO> startResult = workflowApi.startProcess(startProcessParamsDTO);

        // 停止计时
        stopWatch.stop();

        // 记录执行时间和结果
        log.info("【aps-compensation-impl】耗时统计——startProcess启动流程实例【结束】，当前步骤耗时：\n{}",
                stopWatch.prettyPrint());
        log.info("【aps-compensation-impl】-行李运输工作流-startProcess启动流程实例【结束】，行李事故ID[{}],启动参数【{}】----启动流程实例返回信息[{}]",
                baggageTransportInfoDO.getId(), JSONUtil.toJsonStr(startProcessParamsDTO), startResult);

        // 执行流程提交操作
        return this.processSubmit(baggageTransportInfoDO);
    }

    /**
     * 处理工作流提交操作
     * <p>
     * 该方法负责执行工作流的提交操作，主要步骤包括：
     * 1. 获取当前任务信息
     * 2. 验证当前节点是否为提交节点
     * 3. 构建提交参数
     * 4. 调用工作流引擎完成任务
     * 5. 处理任务完成后的结果
     * </p>
     *
     * @param baggageTransportInfoDO 行李运输信息对象
     * @return 工作流审核结果，包含下一步操作的相关信息
     */
    public BaggageWorkflowAuditResultVO processSubmit(BaggageTransportInfoDO baggageTransportInfoDO) {
        log.info("【aps-compensation-impl】-行李运输工作流-processSubmit【开始】提交流程, 行李事故ID[{}]", baggageTransportInfoDO.getId());

        // 获取当前任务信息
        String businessKey = baggageTransportInfoDO.getId().toString();
        BaseQueryParamDTO queryParam = BaseQueryParamDTO.builder()
                .businessKey(businessKey)
                .build();
        log.info("【aps-compensation-impl】-行李运输工作流-processSubmit 查询当前任务, 业务键[{}]", businessKey);
        CurrentTaskActivityVO taskActivityVO = workflowApi.currentUserTask(queryParam).getData();
        log.info("【aps-compensation-impl】-行李运输工作流-processSubmit 获取当前任务结果: [{}]", JSONUtil.toJsonStr(taskActivityVO));

        // 获取当前任务节点
        CurrentTaskActivityDTO currentTaskActivityDTO = taskActivityVO.getCurrentTaskActivityDTOS().get(0);
        log.info("【aps-compensation-impl】-行李运输工作流-processSubmit 当前节点Key: [{}], 任务ID: [{}]",
                currentTaskActivityDTO.getNodeKey(), currentTaskActivityDTO.getTaskId());

        // 验证当前节点是否为提交节点
        if (!WorkflowUtils.isSubmitterTask(currentTaskActivityDTO.getNodeKey())) {
            log.info("【aps-compensation-impl】-行李运输工作流-processSubmit 当前节点不是提交节点, 节点Key: [{}]",
                    currentTaskActivityDTO.getNodeKey());
            throw new BusinessException(COMMON_ERROR, "不能进行提交操作【非提交节点】");
        }

        // 构建提交参数
        CompleteProcessParamsDTO completeProcessParamsDTO = new CompleteProcessParamsDTO();
        completeProcessParamsDTO.setTaskId(currentTaskActivityDTO.getTaskId());
        completeProcessParamsDTO.setBusinessKey(businessKey);
        completeProcessParamsDTO.setOptionCode(AuditStatusEnum.SUBMIT.getKey());
        completeProcessParamsDTO.setBusiData(JSONUtil.parseObj(baggageTransportInfoDO));
        completeProcessParamsDTO.setUserId("userId:" + UserContext.getUserId());
        log.info("【aps-compensation-impl】-行李运输工作流-processSubmit 提交参数: [{}]",
                JSONUtil.toJsonStr(completeProcessParamsDTO));

        // 调用工作流引擎完成任务
        log.info("【aps-compensation-impl】-行李运输工作流-processSubmit 开始调用工作流引擎完成任务");
        CurrentTaskActivityVO activityVO = workflowApi.completeTask(completeProcessParamsDTO).getData();
        log.info("【aps-compensation-impl】-行李运输工作流-processSubmit 完成任务结果: [{}]", JSONUtil.toJsonStr(activityVO));

        // 处理任务完成后的结果
        log.info("【aps-compensation-impl】-行李运输工作流-processSubmit【结束】提交流程, 行李事故ID[{}]", baggageTransportInfoDO.getId());
        return createCurrentTaskActivityVO(activityVO, baggageTransportInfoDO);
    }

    /**
     * 创建工作流审核结果对象
     * <p>
     * 该方法负责处理工作流节点状态转换后的结果，并构建包含下一步操作信息的审核结果对象。
     * 主要处理逻辑包括：
     * 1. 根据节点类型（结束节点、提交节点、普通节点）执行不同的处理逻辑
     * 2. 管理审核人信息（删除旧审核人、添加新审核人）
     * 3. 处理特殊情况（如驳回后重新提交）
     * 4. 构建并返回包含下一步操作所需信息的结果对象
     * </p>
     *
     * @param activityVO             工作流引擎返回的当前任务活动信息
     * @param baggageTransportInfoDO 行李运输信息对象
     * @return 工作流审核结果，包含下一步操作的相关信息
     */
    private BaggageWorkflowAuditResultVO createCurrentTaskActivityVO(CurrentTaskActivityVO activityVO,
            BaggageTransportInfoDO baggageTransportInfoDO) {
        log.info("【aps-compensation-impl】-行李运输工作流-createCurrentTaskActivityVO【开始】处理工作流节点状态, 行李事故ID[{}]",
                baggageTransportInfoDO.getId());

        // 获取当前任务节点
        CurrentTaskActivityDTO currentTask = activityVO.getCurrentTaskActivityDTOS().get(0);
        log.info("【aps-compensation-impl】-行李运输工作流-createCurrentTaskActivityVO 当前节点Key: [{}], 任务ID: [{}], 是否结束节点: [{}]",
                currentTask.getNodeKey(), currentTask.getTaskId(), currentTask.getIsEndActivity());

        // 创建结果对象
        BaggageWorkflowAuditResultVO resultVO = new BaggageWorkflowAuditResultVO(activityVO);

        // 业务ID
        String businessKey = baggageTransportInfoDO.getId().toString();

        // 清除之前的审核人信息
        log.info("【aps-compensation-impl】-行李运输工作流-createCurrentTaskActivityVO 清除之前的审核人信息, 业务键: [{}]", businessKey);
        this.deleteWorkflowAuditorIds(businessKey);

        // 处理结束节点
        if (currentTask.getIsEndActivity()) {
            log.info("【aps-compensation-impl】-行李运输工作流-createCurrentTaskActivityVO 当前为结束节点, 流程已结束");
            return resultVO; // 流程已结束，不需要进一步操作
        }

        // 处理提交节点
        if (WorkflowUtils.isSubmitterTask(currentTask.getNodeKey())) {
            log.info("【aps-compensation-impl】-行李运输工作流-createCurrentTaskActivityVO 当前为提交节点, 节点Key: [{}]",
                    currentTask.getNodeKey());
            resultVO.setTransportId(businessKey);
            resultVO.setTaskId(currentTask.getTaskId());

            // 如果是被驳回后的提交节点，获取上一个审核人信息
            if (StringUtils.equals(AuditStatusEnum.REJECT.getKey(), activityVO.getPreOptionCode())
                    && StringUtils.isNotBlank(currentTask.getLastAssignee())) {
                log.info("【aps-compensation-impl】-行李运输工作流-createCurrentTaskActivityVO 被驳回后的提交节点, 上一个审核人: [{}]",
                        currentTask.getLastAssignee());
                List<AuditorInfoDTO> auditorList = workflowAuditorIdInfoService.findAuditorList(
                        Collections.singletonList(currentTask.getLastAssignee()));
                log.info("【aps-compensation-impl】-行李运输工作流-createCurrentTaskActivityVO 获取到上一个审核人信息: [{}]",
                        JSONUtil.toJsonStr(auditorList));
                resultVO.setAuditorList(auditorList);
            }
            log.info("【aps-compensation-impl】-行李运输工作流-createCurrentTaskActivityVO【结束】处理提交节点");
            return resultVO;
        }

        // 处理普通审核节点
        log.info("【aps-compensation-impl】-行李运输工作流-createCurrentTaskActivityVO 处理普通审核节点, 节点Key: [{}]",
                currentTask.getNodeKey());
        List<AuditorInfoDTO> auditorList = null;

        // 尝试获取审核人列表
        // 1. 如果是被驳回的情况，尝试获取上一个审核人
        if (StringUtils.equals(AuditStatusEnum.REJECT.getKey(), activityVO.getPreOptionCode())
                && StringUtils.isNotBlank(currentTask.getLastAssignee())) {
            log.info("【aps-compensation-impl】-行李运输工作流-createCurrentTaskActivityVO 被驳回的普通节点, 尝试获取上一个审核人: [{}]",
                    currentTask.getLastAssignee());
            auditorList = workflowAuditorIdInfoService.findAuditorList(
                    Collections.singletonList(currentTask.getLastAssignee()));
            log.info("【aps-compensation-impl】-行李运输工作流-createCurrentTaskActivityVO 从上一个审核人获取到审核人列表: [{}]",
                    JSONUtil.toJsonStr(auditorList));
        }

        // 2. 如果没有找到审核人，从当前任务的可分配人员中获取
        if (CollectionUtils.isEmpty(auditorList)) {
            List<String> assignees = currentTask.getAssignees();
            log.info("【aps-compensation-impl】-行李运输工作流-createCurrentTaskActivityVO 从当前任务获取可分配人员: [{}]",
                    JSONUtil.toJsonStr(assignees));
            if (CollectionUtils.isNotEmpty(assignees)) {
                auditorList = workflowAuditorIdInfoService.findAuditorList(assignees);
                log.info("【aps-compensation-impl】-行李运输工作流-createCurrentTaskActivityVO 从可分配人员获取到审核人列表: [{}]",
                        JSONUtil.toJsonStr(auditorList));
            }
        }

        // 如果没有找到审核人，记录信息并返回基本结果
        if (CollectionUtils.isEmpty(auditorList)) {
            log.info("【aps-compensation-impl】-行李运输工作流-createCurrentTaskActivityVO 未找到审核人，节点信息: {}",
                    JSONUtil.toJsonStr(activityVO));
            throw new BusinessException(COMMON_ERROR, "行李运输工作流未找到审核人");
        }

        // 将审核人信息保存到数据库
        List<Long> auditorInfoUserIds = auditorList.stream()
                .map(AuditorInfoDTO::getReviewerId)
                .collect(Collectors.toList());
        log.info("【aps-compensation-impl】-行李运输工作流-createCurrentTaskActivityVO 保存审核人信息, 审核人ID列表: [{}]",
                JSONUtil.toJsonStr(auditorInfoUserIds));
        this.addWorkflowAuditorIds(currentTask.getTaskId(), businessKey, auditorInfoUserIds);

        // 设置结果对象的相关属性
        resultVO.setTransportId(businessKey);
        resultVO.setTaskId(currentTask.getTaskId());
        resultVO.setAuditorList(auditorList);

        log.info("【aps-compensation-impl】-行李运输工作流-createCurrentTaskActivityVO【结束】处理工作流节点状态, 行李事故ID[{}]",
                baggageTransportInfoDO.getId());
        return resultVO;
    }

    /**
     * 处理工作流审核操作
     * <p>
     * 该方法负责执行工作流的审核操作，主要步骤包括：
     * 1. 构建审核参数
     * 2. 调用工作流引擎完成任务
     * 3. 处理任务完成后的结果
     * </p>
     *
     * @param baggageTransportInfoDO 行李运输信息对象
     * @param taskId                 任务ID
     * @param auditStatus           审核状态
     * @param comment               审核意见
     * @return 工作流审核结果，包含下一步操作的相关信息
     */
    public BaggageWorkflowAuditResultVO processAudit(BaggageTransportInfoDO baggageTransportInfoDO, 
                                                     String taskId, String auditStatus, String comment) {
        log.info("【aps-compensation-impl】-行李运输工作流-processAudit【开始】审核流程, 行李事故ID[{}], 任务ID[{}], 审核状态[{}]", 
                baggageTransportInfoDO.getId(), taskId, auditStatus);

        // 构建审核参数
        CompleteProcessParamsDTO completeDto = new CompleteProcessParamsDTO();
        completeDto.setBusinessKey(baggageTransportInfoDO.getId().toString());
        completeDto.setTaskId(taskId);
        completeDto.setUserId("userId:" + UserContext.getUserId());
        completeDto.setOptionCode(auditStatus);
        completeDto.setComment(comment);
        completeDto.setBusiData(JSONUtil.parseObj(baggageTransportInfoDO));

        log.info("【aps-compensation-impl】-行李运输工作流-processAudit 审核参数: [{}]", 
                JSONUtil.toJsonStr(completeDto));

        // 调用工作流引擎完成任务
        CurrentTaskActivityVO activityVO = workflowApi.completeTask(completeDto).getData();
        log.info("【aps-compensation-impl】-行李运输工作流-processAudit 完成任务结果: [{}]", 
                JSONUtil.toJsonStr(activityVO));

        // 处理任务完成后的结果
        log.info("【aps-compensation-impl】-行李运输工作流-processAudit【结束】审核流程, 行李事故ID[{}]", 
                baggageTransportInfoDO.getId());
        return createCurrentTaskActivityVO(activityVO, baggageTransportInfoDO);
    }

    /**
     * 添加工作流审核人信息
     * <p>
     * 将审核人信息保存到数据库中，用于后续的审核操作。
     * </p>
     *
     * @param taskId             任务ID
     * @param businessValue      业务值（通常是行李运输ID）
     * @param auditorInfoUserIds 审核人用户ID列表
     */
    private void addWorkflowAuditorIds(String taskId, String businessValue, List<Long> auditorInfoUserIds) {
        log.info("【aps-compensation-impl】-行李运输工作流-addWorkflowAuditorIds【开始】添加工作流审核人信息, 任务ID: [{}], 业务值: [{}]",
                taskId, businessValue);

        if (CollectionUtils.isEmpty(auditorInfoUserIds)) {
            log.info("【aps-compensation-impl】-行李运输工作流-addWorkflowAuditorIds 添加工作流审核人信息失败：审核人列表为空");
            return;
        }

        // 构建审核人信息对象列表
        List<WorkflowAuditorIdInfoDO> auditorInfoList = auditorInfoUserIds.stream()
                .map(userId -> {
                    WorkflowAuditorIdInfoDO workflowAuditorIdInfoDO = new WorkflowAuditorIdInfoDO();
                    workflowAuditorIdInfoDO.setAuditorId(String.valueOf(userId));
                    workflowAuditorIdInfoDO.setProject(ApsProjectEnum.BAGGAGE_IMPL.getProjectType());
                    workflowAuditorIdInfoDO.setBusiness(CompensationConstant.BAGGAGE_TRANSPORTATION_BUSINESS);
                    workflowAuditorIdInfoDO.setBusinessValue(businessValue);
                    workflowAuditorIdInfoDO.setTaskId(taskId);
                    return workflowAuditorIdInfoDO;
                })
                .collect(Collectors.toList());

        // 保存审核人信息
        workflowAuditorIdInfoService.saveOrUpdateRecords(auditorInfoList);
        log.info("【aps-compensation-impl】-行李运输工作流-addWorkflowAuditorIds【结束】已添加{}个审核人到任务[{}], 审核人ID列表: [{}]",
                auditorInfoList.size(), taskId, JSONUtil.toJsonStr(auditorInfoUserIds));
    }

    /**
     * 删除工作流审核人信息
     * <p>
     * 根据业务值删除对应的审核人信息记录。
     * </p>
     *
     * @param businessValue 业务值（通常是行李运输ID）
     */
    private void deleteWorkflowAuditorIds(String businessValue) {
        log.info("【aps-compensation-impl】-行李运输工作流-deleteWorkflowAuditorIds【开始】删除工作流审核人信息, 业务值: [{}]", businessValue);

        if (StringUtils.isBlank(businessValue)) {
            log.info("【aps-compensation-impl】-行李运输工作流-deleteWorkflowAuditorIds 删除工作流审核人信息失败：业务值为空");
            return;
        }

        // 构建删除条件
        WorkflowAuditorIdInfoDO deleteCondition = new WorkflowAuditorIdInfoDO();
        deleteCondition.setProject(ApsProjectEnum.BAGGAGE_IMPL.getProjectType());
        deleteCondition.setBusiness(CompensationConstant.BAGGAGE_TRANSPORTATION_BUSINESS);
        deleteCondition.setBusinessValue(businessValue);

        // 执行删除操作
        workflowAuditorIdInfoService.deleteWorkflowAuditorIds(deleteCondition);
        log.info("【aps-compensation-impl】-行李运输工作流-deleteWorkflowAuditorIds【结束】已删除业务值[{}]相关的审核人信息", businessValue);
    }

    /**
     * 查询运输单可选审核人
     * <p>
     * 根据运输单ID和任务ID查询当前可选的审核人列表。
     * </p>
     *
     * @param transportId 运输单ID
     * @param taskId      任务ID，可选参数
     * @return 审核人列表
     */
    public List<AuditorInfoDTO> findReviewer(Long transportId, String taskId) {
        log.info("【aps-compensation-impl】-行李运输工作流-findReviewer【开始】查询可选审核人, 运输单ID[{}], 任务ID[{}]", 
                transportId, taskId);

        if (transportId == null) {
            log.warn("【aps-compensation-impl】-行李运输工作流-findReviewer 运输单ID为空");
            return Collections.emptyList();
        }

        // 构建查询条件
        WorkflowAuditorIdInfoDO queryCondition = new WorkflowAuditorIdInfoDO();
        queryCondition.setProject(ApsProjectEnum.BAGGAGE_IMPL.getProjectType());
        queryCondition.setBusiness(CompensationConstant.BAGGAGE_TRANSPORTATION_BUSINESS);
        queryCondition.setBusinessValue(transportId.toString());

        if (StringUtils.isNotBlank(taskId)) {
            queryCondition.setTaskId(taskId);
        }

        // 构建查询条件
        LambdaQueryWrapper<WorkflowAuditorIdInfoDO> queryWrapper = Wrappers.lambdaQuery(WorkflowAuditorIdInfoDO.class);
        queryWrapper.eq(WorkflowAuditorIdInfoDO::getProject, ApsProjectEnum.BAGGAGE_IMPL.getProjectType())
                .eq(WorkflowAuditorIdInfoDO::getBusiness, CompensationConstant.BAGGAGE_TRANSPORTATION_BUSINESS)
                .eq(WorkflowAuditorIdInfoDO::getBusinessValue, transportId.toString());

        if (StringUtils.isNotBlank(taskId)) {
            queryWrapper.eq(WorkflowAuditorIdInfoDO::getTaskId, taskId);
        }

        // 查询审核人信息
        List<WorkflowAuditorIdInfoDO> auditorInfoList = workflowAuditorIdInfoService.list(queryWrapper);
        
        if (CollectionUtils.isEmpty(auditorInfoList)) {
            log.info("【aps-compensation-impl】-行李运输工作流-findReviewer 未找到审核人信息, 运输单ID[{}]", transportId);
            return Collections.emptyList();
        }

        // 提取用户ID列表
        List<String> userIds = auditorInfoList.stream()
                .map(WorkflowAuditorIdInfoDO::getAuditorId)
                .distinct()
                .collect(Collectors.toList());

        // 构建用户ID查询参数
        List<String> userIdParams = userIds.stream()
                .map(userId -> "userId:" + userId)
                .collect(Collectors.toList());

        // 查询审核人详细信息
        List<AuditorInfoDTO> auditorList = workflowAuditorIdInfoService.findTransportAuditorList(userIdParams);

        log.info("【aps-compensation-impl】-行李运输工作流-findReviewer【结束】查询到{}个可选审核人, 运输单ID[{}]", 
                auditorList.size(), transportId);
        
        return auditorList;
    }

    /**
     * 保存运输单审核人
     * <p>
     * 该方法参照 AssuranceWorkflowDomainServiceImpl.saveReviewer 方法实现，
     * 负责保存运输单的审核人信息，主要步骤包括：
     * 1. 验证审核人ID的合法性
     * 2. 删除旧的审核人信息
     * 3. 保存新的审核人信息
     * 4. 返回保存后的审核人详细信息
     * </p>
     *
     * @param transportId 运输单ID
     * @param taskId      任务ID
     * @param auditorIds  审核人ID数组
     * @return 保存后的审核人列表
     */
    public List<AuditorInfoDTO> saveReviewer(Long transportId, String taskId, Long[] auditorIds) {
        log.info("【aps-compensation-impl】-行李运输工作流-saveReviewer【开始】保存审核人, 运输单ID[{}], 任务ID[{}], 审核人数量[{}]", 
                transportId, taskId, auditorIds.length);

        // 1. 获取当前任务的所有可选审核人（参照 AssuranceWorkflowDomainServiceImpl 逻辑）
        List<AuditorInfoDTO> auditorInfoDTOS = this.findReviewer(transportId, taskId);
        if (CollectionUtils.isEmpty(auditorInfoDTOS)) {
            throw new BusinessException(COMMON_ERROR, "错误的审核人数据【当前taskId没有找到任何审核人信息】");
        }

        // 2. 验证提交的审核人ID是否在允许的范围内
        List<String> allowUserIds = auditorInfoDTOS.stream()
                .map(AuditorInfoDTO::getReviewerId)
                .map(String::valueOf)
                .collect(Collectors.toList());

        List<Long> userIds = Arrays.asList(auditorIds);
        Optional<Long> any = userIds.stream()
                .filter(t -> !allowUserIds.contains(String.valueOf(t)))
                .findAny();

        if (any.isPresent()) {
            throw new BusinessException(COMMON_ERROR, "错误的审核人数据");
        }

        // 3. 删除旧的审核人信息
        this.deleteWorkflowAuditorIds(transportId.toString());

        // 4. 保存新的审核人信息
        this.addWorkflowAuditorIds(taskId, transportId.toString(), userIds);

        // 5. 返回保存后的审核人信息（参照 AssuranceWorkflowDomainServiceImpl 逻辑）
        List<String> collect = userIds.stream()
                .map(String::valueOf)
                .collect(Collectors.toList());

        List<AuditorInfoDTO> savedAuditors = auditorInfoDTOS.stream()
                .filter(t -> collect.contains(String.valueOf(t.getReviewerId())))
                .collect(Collectors.toList());

        log.info("【aps-compensation-impl】-行李运输工作流-saveReviewer【结束】保存{}个审核人, 运输单ID[{}]", 
                savedAuditors.size(), transportId);

        return savedAuditors;
    }

}
