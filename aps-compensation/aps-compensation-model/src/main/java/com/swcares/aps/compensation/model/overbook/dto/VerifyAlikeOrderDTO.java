package com.swcares.aps.compensation.model.overbook.dto;

import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：VerifyAlikeOrderDTO
 * @Description：验证事故单||赔偿单
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/6/5 11:05
 * @version： v1.0
 */
@SecretInfoEntity
@Data
public class VerifyAlikeOrderDTO {
    @ApiModelProperty(value = "校验类型【1事故单 2补偿单】")
    private String checkType;

    @ApiModelProperty(value = "事故单号【编辑必传】")
    private String accidentNo;

    @ApiModelProperty(value = "事故类型：1改签，2退票")
    private String type;

    @ApiModelProperty(value = "赔偿单单号【编辑必传】")
    private String orderNo;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "证件类型")
    private String idType;

    @ApiModelProperty(value = "证件号")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "补偿事故单类型- 1.不正常航班 2异常行李 3超售")
    private String accidentType;

    @ApiModelProperty(value = "原航班-航班号")
    private String flightNo;

    @ApiModelProperty(value = "原航班-航班日期")
    private String flightDate;


}
