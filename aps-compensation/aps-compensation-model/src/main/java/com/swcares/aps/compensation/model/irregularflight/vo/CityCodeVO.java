package com.swcares.aps.compensation.model.irregularflight.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * ClassName：com.swcares.com/swcares/aps/flight/passenger/remote/api/a.vo.CityCodeVO <br>
 * Description：机场三四字码表返回展示对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-11-03 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="CityCodeVO对象", description="机场中文三字码")
public class CityCodeVO {

    private String cityNameCode;

}
