package com.swcares.aps.compensation.model.compensation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：CompensationAuditInfoRequestDTO
 * @Description：补偿单审核人信息查询接口请求参数
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/9 16:01
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationAuditInfoRequestDTO对象", description="补偿单审核人信息查询接口请求参数")
public class CompensationAuditInfoRequestDTO {

    @ApiModelProperty(value = "补偿单ids")
    private List<String> orderIds;

    @ApiModelProperty(value = "审核人ID")
    private String userId;
}
