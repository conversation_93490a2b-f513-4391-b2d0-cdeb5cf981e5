package com.swcares.aps.usercenter.remote.api.uc;

import com.swcares.aps.usercenter.model.uc.vo.ReaptvUserAllVO;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.common.uc.vo.RoleAuthorizedUserVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * ClassName：com.swcares.reaptv.usercenter.impl.uc.controller.UserController <br>;
 * Description：藏航 用户中心 前端控制器 <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/5/26 17:50 <br>;
 * @version v1.0 <br>;
 */
@FeignClient(name = "aps-user-center", path = "/api/aps-user-center", contextId = "ReaptvUserRoleJoinApi")
public interface ReaptvUserRoleJoinApi {
    @GetMapping("/uc/reaptv_user_role/getUsersByRoleId")
    @ApiOperation(value = "根据角色id取所有用户接口")
    BaseResult<List<RoleAuthorizedUserVO>> getUsersByRoleId(@RequestParam Long roleId);

    @PostMapping("/common/user/get_by_ids")
    BaseResult<List<ReaptvUserAllVO>> getByIds(@RequestBody List<Long> ids);
}
