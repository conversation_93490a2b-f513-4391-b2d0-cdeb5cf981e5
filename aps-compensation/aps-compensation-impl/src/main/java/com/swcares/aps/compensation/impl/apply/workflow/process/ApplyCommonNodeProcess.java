package com.swcares.aps.compensation.impl.apply.workflow.process;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.compensation.impl.apply.constants.ApplyConstants;
import com.swcares.aps.compensation.impl.apply.enums.ApplyWorkflowNodeBusiTypeEnum;
import com.swcares.aps.component.workflow.NodeNoticeProcess;
import com.swcares.aps.component.workflow.constants.WorkflowConstants;
import com.swcares.aps.component.workflow.dto.AuditorInfoDTO;
import com.swcares.aps.component.workflow.dto.NodeExtVarsDTO;
import com.swcares.aps.component.workflow.dto.NodeNoticeDTO;
import com.swcares.aps.component.workflow.dto.NodeNoticeProcessResult;
import com.swcares.aps.component.workflow.entity.WorkflowAuditorIdInfoDO;
import com.swcares.aps.component.workflow.enums.ApsProjectEnum;
import com.swcares.aps.component.workflow.service.WorkflowAuditorIdInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * ClassName：CommonNodeProcess <br>
 * Description：申领单普通通知节点处理;写入下一个节点审核人ID<br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/1/17 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class ApplyCommonNodeProcess implements NodeNoticeProcess {

    @Autowired
    private WorkflowAuditorIdInfoService workflowAuditorIdInfoService;

    @Override
    public NodeNoticeProcessResult process(NodeNoticeDTO noticeDTO) {
        if(CollectionUtils.isEmpty(noticeDTO.getAssignees())){
            log.error("【aps-apply-impl】-申领单工作流-普通节点回调异常，节点没有没有执行人,notice:{}", JSONUtil.toJsonStr(noticeDTO));
        }
        List<AuditorInfoDTO> auditorList = workflowAuditorIdInfoService.findAuditorList(noticeDTO.getAssignees());
        if(CollectionUtils.isEmpty(auditorList)){
            log.error("【aps-apply-impl】-申领单工作流-普通节点回调异常，节点没有没有执行人,notice:{}", JSONUtil.toJsonStr(noticeDTO));
        }
        List<WorkflowAuditorIdInfoDO> collect = auditorList.stream().map(t -> {
            WorkflowAuditorIdInfoDO workflowAuditorIdInfoDO = new WorkflowAuditorIdInfoDO();
            workflowAuditorIdInfoDO.setAuditorId(String.valueOf(t.getReviewerId()));
            workflowAuditorIdInfoDO.setProject(ApsProjectEnum.APPLY_IMPL.getProjectType());
            workflowAuditorIdInfoDO.setBusiness(ApplyConstants.APPLY_WORKFLOW_BUSINESS);
            workflowAuditorIdInfoDO.setBusinessValue(noticeDTO.getBusiKey());
            workflowAuditorIdInfoDO.setTaskId(noticeDTO.getTaskId());
            return workflowAuditorIdInfoDO;
        }).collect(Collectors.toList());

        workflowAuditorIdInfoService.saveOrUpdateRecords(collect);
        return new NodeNoticeProcessResult();
    }

    @Override
    public boolean canProcess(NodeNoticeDTO noticeDTO) {
        boolean result=false;

        NodeExtVarsDTO extVarsDTO = noticeDTO.getExtVars();
        if(StringUtils.equalsIgnoreCase(extVarsDTO.getProject(), ApsProjectEnum.APPLY_IMPL.getProjectType())
                && StringUtils.equalsIgnoreCase(extVarsDTO.getBusiness(), ApplyConstants.APPLY_WORKFLOW_BUSINESS)
                && isCommonNode(noticeDTO)){
            result=true;
        }

        return result;
    }

    private boolean isCommonNode(NodeNoticeDTO noticeDTO) {
        String nodeBusinessType = noticeDTO.getNodeBusinessType();
        return StringUtils.equalsIgnoreCase(nodeBusinessType, ApplyWorkflowNodeBusiTypeEnum.COMMON.getType())
                || StringUtils.equalsIgnoreCase(nodeBusinessType, WorkflowConstants.NODE_DEFAULT_BUSINESS_TYPE);
    }


}
