package com.swcares.aps.compensation.model.irregularflight.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.aps.compensation.model.irregularflight.vo <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 02月14日 16:21 <br>
 * @version v1.0 <br>
 */
@Data
public class IrregularFlightAuditResultVO {

    private String orderId;
    private boolean isCancel;
    private String taskId;
    private List<CompensationReviewerInfoVO> orderAuditorList;
    private boolean isPrompt;

    private Map<String,Object> resultMap;

    public IrregularFlightAuditResultVO(String orderId, boolean isCancel, String taskId, List<CompensationReviewerInfoVO> orderAuditorList, boolean isPrompt) {
        this.orderId = orderId;
        this.isCancel = isCancel;
        this.taskId = taskId;
        this.orderAuditorList = orderAuditorList;
        this.isPrompt = isPrompt;
        resultMap.put("orderId", orderId);
        resultMap.put("isCancel", isCancel);
        resultMap.put("taskId", taskId);
        resultMap.put("orderAuditorList",orderAuditorList);
        resultMap.put("isPrompt", isPrompt);
    }
}
