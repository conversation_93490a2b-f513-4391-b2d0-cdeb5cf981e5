package com.swcares.aps.compensation.impl.compensation.service;

import com.swcares.aps.compensation.model.compensation.dto.CompensationAddCommand;
import com.swcares.aps.compensation.model.compensation.dto.CompensationEditCommand;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditOperationVO;

/**
 * @ClassName：CompensationAddAndEditService
 * @Description：补偿单创建编辑接口
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 16:59
 * @version： v1.0
 */
public interface CompensationAddAndEditService<S extends CompensationAddCommand,T extends CompensationEditCommand> {
    /**
     * @title add
     * @description 创建补偿单
     * <AUTHOR>
     * @date 2022/3/14 9:36
     * @param addCompensationCommand
     * @return 补偿单ID
     */
    String add(S addCompensationCommand);
    /**
     * @title edit
     * @description 编辑补偿单
     * <AUTHOR>
     * @date 2022/3/14 9:36
     * @param editCompensationCommand
     * @return 补偿单ID
     */
    String edit(T editCompensationCommand);

    /**
     * @title reSubmit
     * @description 重新提交补偿单信息
     * <AUTHOR>
     * @date 2022/5/10 14:43
     * @param editCompensationCommand
     * @return 补偿单ID
     */
    CompensationAuditOperationVO reSubmit(T editCompensationCommand);
}
