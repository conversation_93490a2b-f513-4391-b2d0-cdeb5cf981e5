<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.privilege.mapper.CooperativeCustomerMapper">
    <sql id="cooperativeCustomerBaseSelect">
            select
                id,
                name,
                short_name,
                customer_category,
                disabled,
                remark,
                code,
                created_time,
                created_by,
                updated_time,
                updated_by
            from
                cooperative_customer cc
    </sql>

    <resultMap id="baseCooperativeCustomerResult" type="com.swcares.aps.compensation.model.privilege.entity.CooperativeCustomer">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="short_name" property="shortName"/>
        <result column="customer_category" property="customerCategory"/>
        <result column="disabled" property="disabled"/>
        <result column="remark" property="remark"/>
        <result column="code" property="code"/>
        <result column="created_time" property="createdTime"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="updated_by" property="updatedBy"/>
    </resultMap>

    <select id="getCooperativeCustomerList" resultMap="baseCooperativeCustomerResult">
        <include refid="cooperativeCustomerBaseSelect"/>
        <where>
            <if test="dto.status != null and dto.status != ''">
                cc.disabled = #{dto.status}
            </if>
            <if test="dto.code != null and dto.code != ''">
                and cc.code = #{dto.code}
            </if>
            <if test="dto.shortName != null and dto.shortName != ''">
                and cc.short_name like concat(concat('%',#{dto.shortName}),'%')
            </if>
            <if test="dto.customerCategory != null and dto.customerCategory != ''">
                and cc.customer_category = #{dto.customerCategory}
            </if>
        </where>
    </select>
</mapper>