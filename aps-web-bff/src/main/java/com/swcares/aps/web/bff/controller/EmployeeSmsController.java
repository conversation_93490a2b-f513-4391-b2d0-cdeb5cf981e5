/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：EmployeeSmsController <br>
 * Package：com.swcares.reaptv.user.center.bff.controller.msg.controller <br> 
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.web.bff.controller;

import com.swcares.aps.msg.api.EmployeeSmsRecordApi;
import com.swcares.aps.msg.model.dto.DeleteRecordDTO;
import com.swcares.aps.msg.model.dto.EmployeeSmsSearchDTO;
import com.swcares.aps.msg.model.dto.EmployeeSmsSendDTO;
import com.swcares.aps.msg.model.vo.EmployeeSmsRecordVO;
import com.swcares.aps.msg.model.vo.MessageStatusStaticsVO;
import com.swcares.aps.msg.model.vo.SmsSendResultVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.security.UserContext;
import feign.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.IOUtils;
import org.apache.ibatis.logging.Log;
import org.apache.ibatis.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Collection;
import java.util.List;

/**
 * ClassName：com.swcares.reaptv.user.center.bff.controller.msg.controller.EmployeeSmsController <br>
 * Description：员工短信发送记录前端接口
 * <AUTHOR> <br>
 * date 2022/9/14 5:44 <br>
 * @version v1.0.0 <br>
 */
@RestController
@RequestMapping({"/msg/sms/employee"})
@Api(tags = {"员工短信功能"})
@ApiVersion({"Sms Api v1.0"})
public class EmployeeSmsController {

    @Autowired
    EmployeeSmsRecordApi employeeSmsRecordApi;

    protected Log log = LogFactory.getLog(getClass());

    @PostMapping("/send")
    public BaseResult<SmsSendResultVO> send(@RequestBody EmployeeSmsSendDTO dto){
        if (UserContext.getUser() != null){
            dto.setSenderId(UserContext.getUser().getId());
        }
        return employeeSmsRecordApi.sendSms(dto);
    }

    @PostMapping("/delete")
    public BaseResult<Boolean> delete(@RequestBody DeleteRecordDTO dto){
        return employeeSmsRecordApi.delete(dto);
    }

    @PostMapping("/list")
    @ApiOperation("员工短信发送记录分页查询")
    public PagedResult<List<EmployeeSmsRecordVO>> getEmployeeSmsRecord(@RequestBody
                                                                       EmployeeSmsSearchDTO criteriaDTO){
        return employeeSmsRecordApi.getEmployeeSmsRecord(criteriaDTO);
    }

    @PostMapping("/statics")
    @ApiOperation("获取满足指定的条件的短信发送状态统计")
    public BaseResult<MessageStatusStaticsVO> getMessageStatusStatics(@RequestBody EmployeeSmsSearchDTO searchDto){
        return employeeSmsRecordApi.getMessageStatusStatics(searchDto);
    }

    @GetMapping("/detail")
    @ApiOperation("获取指定记录的详情")
    public BaseResult<EmployeeSmsRecordVO> getDetail(@RequestParam(name="id", required = true)Long id){
        return employeeSmsRecordApi.getDetail(id);
    }

    @PostMapping("/download")
    @ApiOperation("按照查询条件导出(下载)员工短信发送记录")
    public void downloadPassengerSms(@RequestBody EmployeeSmsSearchDTO searchDto, HttpServletResponse response,
            HttpServletRequest request){

        Response feignResponse = employeeSmsRecordApi.download(searchDto);
        downloadByFeign(response, feignResponse);


    }

    @PostMapping("/downloadByIds")
    @ApiOperation("按照指定的id列表导出(下载)员工短信发送记录")
    public void downloadPassengerSms(@RequestBody List<Long> idList, HttpServletResponse response,
            HttpServletRequest request){
        Response feignResponse = employeeSmsRecordApi.downloadById(idList);
        downloadByFeign(response, feignResponse);
    }

    private void downloadByFeign(HttpServletResponse response, Response feignResponse) {
        response.setContentType("application/x-download;charset=UTF8");
        Collection<String> contentDispositionHeader = feignResponse.headers().get("Content-Disposition");
        response.setHeader("Content-Disposition",
                contentDispositionHeader.isEmpty()?"":contentDispositionHeader.iterator().next());

        Response.Body feignBody = feignResponse.body();
        try (OutputStream outputStream = response.getOutputStream();
                InputStream inputStream = feignBody.asInputStream()) {
            IOUtils.copy(inputStream, outputStream);
            outputStream.flush();
        } catch (IOException e) {
            log.error("导出员工短信发送记录出现异常",  e);
        }
    }
}
