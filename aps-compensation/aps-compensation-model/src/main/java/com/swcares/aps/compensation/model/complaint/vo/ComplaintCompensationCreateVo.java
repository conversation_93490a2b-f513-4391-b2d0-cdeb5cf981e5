package com.swcares.aps.compensation.model.complaint.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "ComplaintCompensationCreateVo对象", description = "新增旅客投诉补偿单校验返回")
public class ComplaintCompensationCreateVo {

    @ApiModelProperty(value = "提示信息")
    private String msg;

    @ApiModelProperty(value = "补偿单信息")
    private List<ComplaintCompensationCheckInfoVo> complaintAccidentCheckInfoVos;

    @ApiModelProperty(value = "提示标识")
    private Boolean flag;
}
