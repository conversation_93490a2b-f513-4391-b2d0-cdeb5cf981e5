package com.swcares.aps.compensation.impl.compensation.controller;

import com.swcares.aps.compensation.impl.compensation.service.CompensationMaterialQueriesService;

import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialDetailDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialListDTO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationMaterialDetailFinalVO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationMaterialQueriesListVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName：CompensationOrderCommandsController
 * @Description：箱包补偿单查询类接口
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 唐康
 * @Date： 2022/4/13 15:36
 * @version： v1.0
 */
@RestController
@RequestMapping("/compensation/material")
@Api(tags = "实物补偿单类查询接口")
@ApiVersion(value = "补偿单命令类接口 v1.0.1")
@Slf4j
public class CompensationMaterialQueriesController extends BaseController {
    @Autowired
    private CompensationMaterialQueriesService compensationMaterialQueriesService;

    @PostMapping("/findCompensationLuggageList")
    @ApiOperation(value = "箱包补偿单列表查询")
    public PagedResult<List<CompensationMaterialQueriesListVO>> findCompensationLuggageList(@RequestBody CompensationMaterialListDTO dto ){
        return ok(compensationMaterialQueriesService.findCompensationLuggageList(dto));
    }

    @PostMapping("/findCompensationLuggageDetailInfo")
    @ApiOperation(value = "箱包补偿单详情信息")
    public BaseResult<CompensationMaterialDetailFinalVO> findCompensationLuggageDetailInfo(@RequestBody @Valid CompensationMaterialDetailDTO dto)  {
        return ok(compensationMaterialQueriesService.findCompensationLuggageDetailInfo(dto));
    }



}
