package com.swcares.aps.compensation.model.irregularflight.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * ClassName：com.swcares.aps.compensating.mode.irregularflight.vo <br>
 * Description：事故单详情 VO<br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 10月28日 10:20 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="详情AccidentDetailsVO对象", description="")
public class AccidentDetailsVO {

    @ApiModelProperty(value = "事故单信息")
    FlightAccidentInfoDetailsVO flightAccidentInfoDetailsVO;

    @ApiModelProperty(value = "事故单下关联赔偿单列表")
    List<AccidentCompensationOrderVO> compensationOrderVoList;
}
