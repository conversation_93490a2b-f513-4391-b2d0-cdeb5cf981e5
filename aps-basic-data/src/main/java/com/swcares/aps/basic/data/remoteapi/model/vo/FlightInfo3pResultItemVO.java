package com.swcares.aps.basic.data.remoteapi.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">...</a> <br>
 * Title ：FlightInfo3pItemVO <br>
 * Package ：com.swcares.base.common.vo <br>
 * Copyright 2024 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company ：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 * date 2024年 03月05日 11:30 <br>
 * @version v1.0 <br>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode
public class FlightInfo3pResultItemVO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "航空公司两字码")
    @JSONField
    private String airlineCode;

    @ApiModelProperty(value = "出发机场三字码")
    private String departureAirportCode;

    @ApiModelProperty(value = "到达机场三字码")
    private String destinationAirportCode;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "前序航班号")
    private String preorderFlightNo;

    @ApiModelProperty(value = "行李转盘序号")
    private String baggageCarouselSerialNumber;

    @ApiModelProperty(value = "进出港拼接字符串")
    private String arrDepFlightNo;

    @ApiModelProperty(value = "航班日期")
//    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private LocalDate flightDate;

    @ApiModelProperty(value = "航班日期")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime flightDateTime;

    @ApiModelProperty(value = "航段")
    private String flightSegment; //TODO

    @ApiModelProperty(value = "航段属性")
    private String flightSegmentProperty; //TODO

    @ApiModelProperty(value = "航线")
    private String airline;

    @ApiModelProperty(value = "航线性质")
    private String airlineProperty;  //TODO

    @ApiModelProperty(value = "任务标识")
    private String task;

    @ApiModelProperty(value = "延误时间长度")
    private Integer lengthOfDelay;

    @ApiModelProperty(value = "机号")
    private String aircraftNo;

    @ApiModelProperty(value = "机型")
    private String aircraftModel;

    @ApiModelProperty(value = "机位")
    private String aircraftParking;

    @ApiModelProperty(value = "航班状态")
    private String status;

    @ApiModelProperty(value = "登机口")
    private String gate;

    @ApiModelProperty(value = "计划到达时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime planLandingDatetime;

    @ApiModelProperty(value = "预计到达时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime predictLandingDatetime;

    @ApiModelProperty(value = "实际到达时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime realLandingDatetime;

    @ApiModelProperty(value = "计划起飞时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime planTakeOffDatetime;

    @ApiModelProperty(value = "预计起飞时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime predictTakeOffDatetime;

    @ApiModelProperty(value = "实际起飞时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime realTakeOffDatetime;

    @ApiModelProperty(value = "实际撤轮档时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime cobt;

    @ApiModelProperty(value = "目标撤轮挡时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime tobt;

    @ApiModelProperty(value = "计算起飞时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime ctot;

    @ApiModelProperty(value = "计算航班数")
    private Integer total;

    @ApiModelProperty(value = "进出港标识;")
    private String isArrv;

    @ApiModelProperty(value = "acdm删除标识(1：删除 0：正常)")
    private Integer acdmDeleted;

    @ApiModelProperty(value = "同步更新时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime syncUpdatedTime;

    @ApiModelProperty(value = "关联航班id")
    private Long connectFlightId;

    @ApiModelProperty(value = "创建者")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新者")
    private String updatedBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
//    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;
}
