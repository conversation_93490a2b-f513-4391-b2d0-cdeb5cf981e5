/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：SmsTaskBuilderFactory <br>
 * Package：com.swcares.reaptv.msg.flightevent.taskbuilder <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.flightevent.taskbuilder;

import java.util.HashMap;
import java.util.Map;

/**
 * ClassName：com.swcares.reaptv.msg.flightevent.taskbuilder.SmsTaskBuilderFactory <br>
 * Description：短信任务构造器工厂
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/5 9:00 <br>
 * @version:
 */
public class SemiAutoSmsTaskBuilderFactory {
    private static Map<String, SemiAutoSmsTaskBuilder> smsTaskBuilders = new HashMap<>();
    public static SemiAutoSmsTaskBuilder getSemiAutoSmsTaskBuilder(String adjustType){
        SemiAutoSmsTaskBuilder builder = smsTaskBuilders.get(adjustType);
        return  builder;
    }
    
    public static void register(String adjustType, SemiAutoSmsTaskBuilder builder){
        smsTaskBuilders.put(adjustType, builder);
    }
}
