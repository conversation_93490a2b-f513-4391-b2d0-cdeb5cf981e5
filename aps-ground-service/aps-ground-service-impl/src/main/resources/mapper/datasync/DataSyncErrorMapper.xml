<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.ground.datasync.mapper.DataSyncErrorMapper">
    <select id="getNeedExecDatas" resultType="com.swcares.aps.ground.models.datasync.entity.DataSyncErrorDO">
        SELECT
            *
        FROM
            GROUND_DATA_SYNC_ERROR
        WHERE
            EXEC_TIMES > 0
        AND TENANT_TYPE = #{tenantType}
        AND ( EXEC_STATUS = 0
            OR (EXEC_STATUS = 3 AND EXEC_TIMES &lt; 5)
        )
    </select>
</mapper>