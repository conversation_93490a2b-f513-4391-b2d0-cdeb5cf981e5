package com.swcares.aps.compensation.model.overbook.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.entity.CompensationOrderInfo <br>
 * Description：赔偿单信息 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="CompensationOrderInfoDO对象", description="赔偿单信息")
public class CompensationOrderBasicVO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "事故单ID")
    private Long accidentId;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "赔偿单单号")
    private String orderNo;

    @ApiModelProperty(value = "补偿方式 1现金，2虚拟，3实物")
    private String compensateType;

    @ApiModelProperty(value = "服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "所选航段")
    private String choiceSegment;

    @ApiModelProperty(value = "所选航段中文")
    private String choiceSegmentCh;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "保障服务（0不存在，1存在）")
    private String ensureType;

    @ApiModelProperty(value = "0草稿 1驳回 2审核中 3审核通过 4生效 5关闭 6逾期 7审核不通过")
    private String status;

    @ApiModelProperty(value = "航班ID")
    private String flightId;

    @ApiModelProperty(value = "申领有效期(补偿单生效)默认一年有效")
    private LocalDateTime expiryDate;

    @ApiModelProperty(value = "合计金额")
    private BigDecimal sumMoney;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "生效状态操作人")
    private String issUser;

    @ApiModelProperty(value = "关闭人")
    private String closeUser;

    @ApiModelProperty(value = "关闭时间")
    private LocalDateTime closeTime;

    @ApiModelProperty(value = "补偿类型-1不正常航班，2异常行李，3超售")
    private String accidentType;

    @ApiModelProperty(value = "补偿子类型-1延误，2取消，3备降，4返航，5补班; 21破损,22少收,23多收,24内件缺失,25丢失")
    private String accidentSubType;

    @ApiModelProperty(value = "所属航班全航段")
    private String fullSegment;

}
