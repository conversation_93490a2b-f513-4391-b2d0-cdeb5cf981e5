package com.swcares.aps.compensation.model.overbook.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName：OverBookSaveDTO
 * @Description：超售事故单-保存对象
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/28 11:13
 * @version： v1.0
 */
@Data
public class OverBookSaveDTO {
    @ApiModelProperty(value = "0保存草稿 1.只创建事故单 2创建事故单及生成补偿单草稿 3创建事故单及补偿单 ")
    @NotBlank(message = "保存类型不能为空")
    private Integer saveType;
    @ApiModelProperty(value = "旅客信息")
    OverBookPaxSaveDTO paxVO;
    @ApiModelProperty(value = "事故单信息")
    OverBookAccidentSaveDTO accidentSaveDTO;
    @ApiModelProperty(value = "补偿单信息【saveType=2||3 保存补偿单草稿或创建补偿单 必传】")
    OverBookCompensationSaveDTO compensationSaveDTO;

}
