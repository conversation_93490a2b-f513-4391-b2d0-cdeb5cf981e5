package com.swcares.aps.workflow.flowable.service;


import com.swcares.aps.workflow.dto.SyncWorkflowInfoDTO;

import java.util.Map;
import java.util.Set;

/**
 * ClassName：com.swcares.aps.workflow.flowable.service.FlowableVariableService <br>
 * Description：参数服务 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/2/10 <br>
 * @version v1.0 <br>
 */
public interface FlowableVariableService {
    /**
     *获取流程参数
     * @param processInstanceId 流程实例ID
     * @param variableName 参数名称
     * @return
     */
    Object getProcessVariable(String processInstanceId, String variableName);

    /**
     * 获取任务的本地参数
     * @param taskIds
     * @return
     */
    Map<String, Object> getTaskLocalVariable(Set<String> taskIds, String variableName);

    /**
     * 同步流程参数
     * @param params
     */
    void syncProcessVariable(SyncWorkflowInfoDTO params);
}
