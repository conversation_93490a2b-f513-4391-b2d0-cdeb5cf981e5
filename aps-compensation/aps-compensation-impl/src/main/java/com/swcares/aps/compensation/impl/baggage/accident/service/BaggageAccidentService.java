package com.swcares.aps.compensation.impl.baggage.accident.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.baggage.accident.dto.*;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageAccidentInfoVO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageDetailFinalVO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageTransportListVO;
import com.swcares.aps.compensation.model.baggage.accident.vo.MatchResultVO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.FindBaggageVO;
import com.swcares.aps.compensation.model.irregularflight.dto.PaxMaintainSearchDTO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageAccidentDropdownVO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageTransportDetailVO;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @title: BaggageAccidentService
 * @projectName aps
 * @description:
 * @date 2022/3/4 9:44
 */
public interface BaggageAccidentService extends IService<BaggageAccidentInfoDO> {
    /**
     * @title saveAccident
     * @description 创建异常行李事故单
     * <AUTHOR>
     * @date 2022/3/8 15:33
     * @param dto
     * @return boolean
     */
    Object saveAccident(BaggageAccidentInfoDTO dto) throws IOException;

    /**
     * @title editAccident
     * @description 编辑异常行李事故单
     * <AUTHOR>
     * @date 2022/3/8 15:33
     * @param accidentId
     * @return com.swcares.aps.compensation.model.baggage.accident.vo.BaggageAccidentInfoVO
     */
    BaggageAccidentInfoVO editAccident(String accidentId);

    /**
     * @title toLost
     * @description 少收转丢失
     * <AUTHOR>
     * @date 2022/3/8 15:32
     * @param accidentId 事故单id
     * @return boolean
     */
    boolean toLost(String accidentId);

    /**
     * @title toMatch
     * @description 匹配查询多/少收
     * <AUTHOR>
     * @date 2022/3/8 15:34
     * @param accidentId 事故单id
     * @return com.swcares.aps.compensation.model.baggage.accident.vo.MatchResultVO
     */
    List<MatchResultVO> toMatch(String accidentId);

    /**
     * @title findLuggageList
     * @description 异常行李事故单列表查询
     * <AUTHOR>
     * @date 2022/3/4 9:47
     * @param dto
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.compensation.model.baggage.luggage.vo.FindLuggageStockInfoVO>
     */
    IPage<FindBaggageVO> findBaggageAccidentList(FindBaggageDTO dto);

    /**
     * @title findBaggageDetail
     * @description 异常行李事故单详情信息
     * <AUTHOR>
     * @date 2022/3/8 13:21
     * @param dto
     * @return com.swcares.aps.compensation.model.baggage.accident.vo.FindBaggageDetailVO
     */
    BaggageDetailFinalVO findBaggageDetail(FindBaggageDetailDTO dto);

    /**
     * @title saveMatch
     * @description 匹配绑定多/少收事故单
     * <AUTHOR>
     * @date 2022/3/9 15:39
     * @param accidentId 当前事故单的id
     * @param accidentNo 匹配结果的事故单号
     * @return void
     */
    boolean saveMatch(String accidentId, String accidentNo);

    /**
     * @title relieveMatch
     * @description 解除匹配多/少收事故单
     * <AUTHOR>
     * @date 2022/3/10 13:52
     * @param accidentId 当前事故单的id
     * @param accidentNo 匹配结果的事故单号
     * @return void
     */
    boolean relieveMatch(String accidentId, String accidentNo);

    /**
     * @title deleteById
     * @description 通过id删除异常行李事故单
     * <AUTHOR>
     * @date 2022/3/15 19:54
     * @param id
     * @return java.lang.Boolean
     */
    Boolean deleteById(Long id);

    /**
     * @title getBaggageAccidentNumber
     * @description 获取已经生成的补偿单数量
     * <AUTHOR>
     * @date 2022/3/17 16:20
     * @param dto
     * @return java.lang.Long
     */
    Integer getBaggageAccidentNumber(PaxMaintainSearchDTO dto);

    /***
     * @title updAccidentStatusToDo
     * @description 批量更新事故单状态
     * <AUTHOR>
     * @date 2022/9/16 12:16
     * @param accidentNo
     * @param accidentStatus
     * @return boolean
     */
    boolean updBaggageAccidentStatusBatch(List<String> accidentNo, String accidentStatus);

    /**
     * @title getExistAccidentNumber
     * @description 已存在的事故单数量
     * <AUTHOR>
     * @date 2024/6/19 14:19
     * @param dto
     * @return java.lang.Integer
     */
    Integer getExistAccidentNumber(VerifyAlikePaxOrderDTO dto);

    /**
     * @title getExistCompensationNumber
     * @description 已存在的补偿单数量
     * <AUTHOR>
     * @date 2024/6/19 14:19
     * @param dto
     * @return java.lang.Integer
     */
    Integer getExistCompensationNumber(VerifyAlikePaxOrderDTO dto);

    /***
     * @title getOvertimeAccident
     * @description 待处理少收事故单若已超时，需要发送站内信
     * <AUTHOR>
     * @date 2024/7/26 15:14
     * 
     * @return java.util.List<com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO>
     */
    List<BaggageAccidentInfoDO> getOvertimeAccident();

    /**
     * 获取航班上未运输且未合并的行李事故单下拉列表
     * 
     * @param flightNo   航班号
     * @param flightDate 航班日期
     * @return 行李事故单下拉列表
     */
    List<BaggageAccidentDropdownVO> getUndeliveredAccidentDropdown(UndeliveredAccidentDTO dto);

    /**
     * 判断是否可以编辑运输单
     * 
     * @param accidentNo 事故单号
     * @return 是否可以编辑运输单
     */
    Boolean canEditTransport(String accidentNo);

    /**
     * 保存或修改运输信息
     * 
     * @param dto 运输信息DTO
     * @return 是否保存成功
     */
    boolean saveTransportInfo(BaggageTransportInfoDTO dto);

    /**
     * 查询异常行李运输单列表
     * 
     * @param dto 查询条件
     * @return 分页查询结果
     */
    IPage<BaggageTransportListVO> queryBaggageTransportList(BaggageTransportQueryDTO dto);

    /**
     * 查询运输单详情
     * 
     * @param transportId 运输单ID
     * @return 运输单详情
     */
    BaggageTransportDetailVO getTransportDetail(Long transportId);

    boolean updateTransportAddress(BaggageTransportAddressDTO dto);

    /**
     * 提交运输单到工作流
     * 
     * @param transportId 运输单ID
     * @return 工作流审核结果
     */
    Object submitTransport(Long transportId);

    /**
     * 审核运输单
     * 
     * @param dto 审核信息DTO
     * @return 工作流审核结果
     */
    Object auditTransport(BaggageTransportAuditDTO dto);

    /**
     * 查询运输单可选审核人
     * 
     * @param transportId 运输单ID
     * @param taskId      任务ID，可选
     * @return 审核人列表
     */
    Object getTransportReviewers(Long transportId, String taskId);

    /**
     * 保存运输单审核人
     * 
     * @param dto 审核人保存DTO
     * @return 保存后的审核人列表
     */
    Object saveTransportReviewers(BaggageTransportReviewerSaveDTO dto);
}
