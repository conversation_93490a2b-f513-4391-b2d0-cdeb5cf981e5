package com.swcares.aps.compensation.model.complaint.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "PassengerCompensationInfoDto对象", description = "填写事故单信息")
public class PassengerCompensationInfoDto {

    @ApiModelProperty(value = "事故类型",required = true)
    @NotBlank(message = "事故类型不能为空")
    private String accidentSubType;

    @ApiModelProperty(value = "原因类型",required = true)
    @NotBlank(message = "原因类型不能为空")
    private String reasonType;

    @ApiModelProperty(value = "旅客投诉部门",required = true)
    @NotBlank(message = "旅客投诉部门不能为空")
    private String complaintDep;

    @ApiModelProperty(value = "旅客投诉渠道",required = true)
    @NotBlank(message = "旅客投诉渠道不能为空")
    private String complaintChannel;

    @ApiModelProperty(value = "事故说明",required = true)
    @NotBlank(message = "事故说明不能为空")
    private String accidentDes;

    @ApiModelProperty(value = "旅客电话",required = true)
    @NotBlank(message = "旅客电话不能为空")
    private String contactInfo;

    @ApiModelProperty(value = "附件")
    private String fileUrl;
}
