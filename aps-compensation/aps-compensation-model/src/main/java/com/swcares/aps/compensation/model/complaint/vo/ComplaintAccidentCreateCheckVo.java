package com.swcares.aps.compensation.model.complaint.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "ComplaintAccidentCreateCheckVo对象", description = "新增旅客投诉事故单校验返回")
public class ComplaintAccidentCreateCheckVo {

    @ApiModelProperty(value = "提示信息")
    private String msg;

    @ApiModelProperty(value = "事故单信息")
    private List<ComplaintAccidentCheckInfoVo> complaintAccidentCheckInfoVos;

    @ApiModelProperty(value = "提示标识")
    private Boolean flag;
}
