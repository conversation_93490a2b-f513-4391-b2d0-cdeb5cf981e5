/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：EmployeeSnsController <br>
 * Package：com.swcares.reaptv.msg.controller <br> 
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.msg.model.dto.*;
import com.swcares.aps.msg.model.entity.EmployeeSnsRecord;
import com.swcares.aps.msg.model.enums.SendStatusEnum;
import com.swcares.aps.msg.model.util.ModelConverter;
import com.swcares.aps.msg.model.vo.EmployeeSnsRecordVO;
import com.swcares.aps.msg.model.vo.MessageStatusStaticsVO;
import com.swcares.aps.msg.service.EmployeeSnsRecordService;
import com.swcares.aps.msg.service.impl.SearchCriteriaUtils;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * ClassName：com.swcares.reaptv.msg.controller.EmployeeSnsController <br>
 * Description：员工社交软件信息发送记录接口
 * <AUTHOR> <br>
 * date 2022/10/8 14:59 <br>
 * @version v1.0.0 <br>
 */
@RestController
@RequestMapping({"/msg/sns/employee"})
@Api(tags = {"员工社交软件信息发送记录接口"})
@ApiVersion({"Sns Api v1.0"})
public class EmployeeSnsController {
    @Autowired
    EmployeeSnsRecordService employeeSnsRecordService;

    @Autowired
    SearchCriteriaUtils searchCriteriaUtils;

    @PostMapping("/list")
    @ApiOperation("分页获取员工社交信息发送记录")
    public PagedResult<List<EmployeeSnsRecordVO>> getEmployeeSnsRecord(@RequestBody
                                                                       EmployeeSnsSearchDTO searchDto){
        transferBusinessTypeSearchCriteria(searchDto);
        IPage<EmployeeSnsRecord> datas = employeeSnsRecordService.getEmployeeSnsRecordPage(searchDto);
        List<EmployeeSnsRecordVO> result = ModelConverter.convertToEmployeeSnsRecordVO(datas.getRecords());
        return PagedResult.ok(result, datas.getTotal(), datas.getPages(), datas.getSize(), datas.getCurrent());
    }

    @PostMapping("/send_template_wechat_msg")
    @ApiOperation("给员工发送模板消息")
    public BaseResult<MultiMsgSendResult> sendTemplateWechatMessage(
            @RequestBody EmployeeWechatTemplateMsgSendDTO dto){
        MultiMsgSendResult result = employeeSnsRecordService.sendTemplateWechatMessage(dto);
        return BaseResult.ok(result);
    }

    private void transferBusinessTypeSearchCriteria(EmployeeSnsSearchDTO dto){
        if (dto == null || dto.getBusinessTypes() == null){
            return;
        }

        List<String> transferedBusinessType = searchCriteriaUtils.getEmployeeBusinessTypeDictValue(dto.getBusinessTypes());
        dto.setBusinessTypes(transferedBusinessType);
    }


    @RequestMapping("/statics")
    @ApiOperation("获取满足指定的条件的短信发送状态统计")
    public BaseResult<MessageStatusStaticsVO> getMessageStatusStatics(@RequestBody
            EmployeeSnsSearchDTO searchDto){
        transferBusinessTypeSearchCriteria(searchDto);
        List<MessageStatusStaticsDTO> staticsDTO = employeeSnsRecordService.getMessagesStatusStatics(searchDto);

        MessageStatusStaticsVO messageStatusStaticsVO = new MessageStatusStaticsVO();
        if (staticsDTO != null && !staticsDTO.isEmpty()){
            for(MessageStatusStaticsDTO dto : staticsDTO){
                messageStatusStaticsVO.addTotalCount(dto.getRecordCount());

                if (SendStatusEnum.SUCCESS.equals(dto.getStatus())){
                    messageStatusStaticsVO.addSuccessCount(dto.getRecordCount());
                }
            }
        }

        return BaseResult.ok(messageStatusStaticsVO);
    }

    @GetMapping("/detail")
    @ApiOperation("获取指定记录的详情")
    public BaseResult<EmployeeSnsRecordVO> getDetails(@RequestParam(name="id", required = true)Long id){
        EmployeeSnsRecord record = employeeSnsRecordService.getById(id);
        EmployeeSnsRecordVO vo = ModelConverter.convertToEmployeeSnsRecordVO(record);

        return BaseResult.ok(vo);
    }

    @PostMapping("/delete")
    @ApiOperation("逻辑删除指定的记录")
    public BaseResult<Boolean> delete(@RequestBody DeleteRecordDTO dto){
        Boolean result = employeeSnsRecordService.batchDelete(dto.getIds());
        return BaseResult.ok(result);
    }

    @PostMapping("/download")
    @ApiOperation("根据查询条件下载指定的员工社交信息发送记录")
    public void download(@RequestBody EmployeeSnsSearchDTO searchDto, HttpServletRequest request, HttpServletResponse response){
        transferBusinessTypeSearchCriteria(searchDto);
        employeeSnsRecordService.download(searchDto, request, response);
    }

    @PostMapping("/downloadById")
    @ApiOperation("下载通过id指定的员工社交信息发送记录")
    public void downloadById(@RequestBody List<Long> ids, HttpServletRequest request, HttpServletResponse response){
        employeeSnsRecordService.downLoad(ids, request, response);
    }
}
