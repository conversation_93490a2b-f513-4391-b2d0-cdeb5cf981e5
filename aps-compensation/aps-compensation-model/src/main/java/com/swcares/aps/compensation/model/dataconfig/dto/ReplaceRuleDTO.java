package com.swcares.aps.compensation.model.dataconfig.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.aps.compensation.model.replace.dto.ReplaceRuleDTO <br>
 * Description： 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-10 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="ReplaceRuleDTO对象", description="代人领取RULE配置DTO")
public class ReplaceRuleDTO  implements BaseDTO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否包含本人 0:不包含 1:包含")
    private String isContainSelf;

    @ApiModelProperty(value = "是否同航班旅客 0:不是 1:是")
    private String isSameFlight;

    @ApiModelProperty(value = "最大可申领取乘机人数")
    private Integer maxApplyPassenger;

    @ApiModelProperty(value = "支付等待期，默认值为 5分钟，最大允许设置60分钟")
    private Integer paymentWaitingPeriod;

}
