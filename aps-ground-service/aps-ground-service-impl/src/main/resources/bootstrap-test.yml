########################################## nacos配置  ###################################
spring:
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.17.231:8848
        namespace: test
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        refresh-enabled: true
        file-extension: yml
        namespace: test
        group: test
        shared-configs:
          - data-id: application-bash-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            refresh: true
            group: test
logging:
  config: classpath:logback.xml
