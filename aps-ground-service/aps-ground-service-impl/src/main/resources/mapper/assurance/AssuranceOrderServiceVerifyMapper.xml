<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.ground.assurance.mapper.AssuranceOrderServiceVerifyMapper">
    <update id="logicRemoveById">
        update ASSURANCE_ORDER_SERVICE_VERIFY set DELETED=1 where id = #{id}
    </update>

    <select id="getOrderServiceVerifyDetailList" resultType="com.swcares.aps.ground.models.assurance.vo.AssuranceOrderServiceVerifyUserDetailVO">
        select
         DISTINCT
        <!-- 保障单信息-->
        aov.ORDER_ID,
        gof.order_no,
        gof.BELONG_TENANT_TYPE  orderBelongTenantType ,
        gof.FLIGHT_NO,
        gof.FLIGHT_DATE ,
        gof.ORDER_STATE,
        gof.ORDER_REMARK,
        <!--服务项-->
        aos.SERVICE_TARGET,
        aos.SERVICE_NAME ,
        aos.SERVICE_AIRPORT  serviceAirport,
        get_city_name(aos.SERVICE_AIRPORT) serviceAirportCh,
        <!--旅客信息-->
        aou.PAX_TYPE_CODE,
        aou.PAX_CATEGORY_CODE paxCategoryCode,
        aou.PAX_ID_NO,
        aou.PAX_TICKET_NO,
        aou.PAX_ORG,
        aou.PAX_DST,
        get_city_name(aou.PAX_ORG) paxOrgCh,
        get_city_name(aou.PAX_DST) paxDstCh,
        aou.USER_NAME paxName,
        aou.USER_NAME userName,
        aou.CREW_WORK_NO,
        aou.CREW_WORK_POSITION,
        <!--核销信息-->

        aov.id orderServiceVerifyId,
        aov.SUPPLIER_ID,
        aov.SUPPLIER_NAME,
        aov.SUPPLIER_BELONG_TENANT_TYPE,
        aov.PRICE,
        aov.CONFIRM_TIME confirmTime,
        aov.CONFIRM_USER,
        aov.VERIFICATION_INDEX,
        aov.VERIFICATION_STATE,
        aov.VERIFICATION_TYPE,
        aov.ORDER_USER_ID,
        aov.ORDER_SERVICE_ID,
        aov.SERVICE_ID,
        aov.SUPPLIER_TYPE,
        aov.CONFIRM_TENANT_TYPE,
        gof.ASSIGN_SUPPLIER assignSupplier


        FROM ASSURANCE_ORDER_SERVICE_VERIFY aov
        JOIN ASSURANCE_ORDER_INFO gof on AOV.ORDER_ID = gof."ID"
        JOIN ASSURANCE_ORDER_SERVICE aos on aov.ORDER_SERVICE_ID = aos.id
        JOIN ASSURANCE_ORDER_USER aou on aov.ORDER_USER_ID = aou.id

        WHERE gof.DELETED =0 and AOV.DELETED =0 and aos.DELETED = 0

        <if test="dto.flightNo!=null and dto.flightNo!='' "> and  gof.FLIGHT_NO = #{dto.flightNo}</if>
        <if test="dto.flightNoFuzzy!=null and dto.flightNoFuzzy!='' "> and  gof.FLIGHT_NO  like concat(concat('%',#{dto.flightNoFuzzy}),'%')</if>
        <if test="dto.airlineCodes!=null and dto.airlineCodes.size()>0">
            and
            <foreach collection="dto.airlineCodes" item="airlineCode" separator="or" open="(" close=")">
                gof.FLIGHT_NO  like concat(concat('%',#{airlineCode}),'%')
            </foreach>
        </if>
        <if test="dto.flightDate!=null">and gof.FLIGHT_DATE = #{dto.flightDate}</if>
        <if test="dto.startFlightDate!=null">and GOF.FLIGHT_DATE  &gt;= #{dto.startFlightDate}</if>
        <if test="dto.endFlightDate!=null">and GOF.FLIGHT_DATE &lt;=  #{dto.endFlightDate} </if>
        <if test="dto.paxName!=null and dto.paxName!=''">and aou.USER_NAME like  concat(concat('%',#{dto.paxName}),'%')  </if>
        <if test="dto.idNo!=null and dto.idNo!=''"> and aou.PAX_ID_NO =  #{dto.idNo}</if>
        <if test="dto.ticketNo!=null and dto.ticketNo!=''"> and aou.PAX_TICKET_NO =#{dto.ticketNo}</if>
        <if test="dto.paxOrg!=null and dto.paxOrg!=''"> and aou.PAX_ORG =#{dto.paxOrg}</if>
        <if test="dto.paxDst!=null and dto.paxDst!=''"> and aou.PAX_DST =#{dto.paxDst}</if>
        <if test="dto.crewWorkNo!=null and dto.crewWorkNo!=''"> and aou.CREW_WORK_NO like '%' || #{dto.crewWorkNo} || '%'</if>
        <if test="dto.orderState!=null  and dto.orderState!=''"> and  gof.ORDER_STATE = #{dto.orderState}</if>
        <if test="dto.verificationType!=null and dto.verificationType!=''"> aov.VERIFICATION_TYPE = #{dto.verificationType}</if>
        <if test="dto.airport!=null and dto.airport!=''"> and aos.SERVICE_AIRPORT = #{dto.airport}</if>
        <if test="dto.orderServiceVerifyId!=null and dto.orderServiceVerifyId!=''"> and aov.id = #{dto.orderServiceVerifyId}</if>
        <if test="dto.serviceId!=null and dto.serviceId!=''"> and aov.SERVICE_ID = #{dto.serviceId}</if>
        <if test="dto.serviceName!=null and dto.serviceName!=''"> and aos.SERVICE_NAME like '%' ||  #{dto.serviceName} || '%'</if>


        <if test="dto.supplierBelongTenantType!=null and dto.supplierBelongTenantType!=''"> and aov.SUPPLIER_BELONG_TENANT_TYPE = #{dto.supplierBelongTenantType}</if>
        <!--        <if test="dto.belongTenantType!=null and dto.belongTenantType!='' ">and gof.BELONG_TENANT_TYPE  = #{dto.belongTenantType}</if>-->
        <if test="dto.choiceSupplierBelongTenantId!=null">
            and aov.ORDER_SERVICE_ID in
            (select asg.ORDER_SERVICE_ID
            FROM ASSURANCE_ORDER_SERVICE_CHOICE asg
            where aov.ORDER_SERVICE_ID = asg.ORDER_SERVICE_ID
              and aov.VERIFICATION_STATE = 'WAIT'
            and asg.SUPPLIER_BELONG_TENANT_ID = #{dto.choiceSupplierBelongTenantId}
            )
        </if>
        <if test="dto.verificationState!=null and dto.verificationState.size()>0 ">
            and aov.VERIFICATION_STATE in
            <foreach collection="dto.verificationState" open="(" close=")" separator="," item="vState">
                #{vState}
            </foreach>
        </if>


        <if test="dto.orderNo!=null and dto.orderNo!=''"> and  gof.order_no = #{dto.orderNo}</if>
        <if test="dto.userName!=null and dto.userName!=''">
            and aou.USER_NAME  like  concat(concat('%',#{dto.userName}),'%')
        </if>
        <if test="dto.serviceTarget!=null and dto.serviceTarget!=''"> and aos.SERVICE_TARGET = #{dto.serviceTarget}</if>
        <if test="dto.supplierName!=null and dto.supplierName!=''">
            and aov.SUPPLIER_NAME  like concat(concat('%',#{dto.supplierName}),'%')

        </if>
        <if test="dto.orderBelongTenantType!=null and dto.orderBelongTenantType!=''">
            and gof.BELONG_TENANT_TYPE = #{dto.orderBelongTenantType}
        </if>
        <if test="dto.serviceAirports!=null and dto.serviceAirports.size()>0 ">
            and aos.SERVICE_AIRPORT in
            <foreach collection="dto.serviceAirports" open="(" close=")" separator="," item="serviceAirport">
                #{serviceAirport}
            </foreach>
        </if>

        <if test="dto.confirmTenantTypes!=null and dto.confirmTenantTypes.size()>0 ">
            and aov.CONFIRM_TENANT_TYPE in
            <foreach collection="dto.confirmTenantTypes" open="(" close=")" separator="," item="confirmTenantType">
                #{confirmTenantType}
            </foreach>
        </if>

        <if test="dto.verificationTypes!=null and dto.verificationTypes.size()>0 ">
            and aov.VERIFICATION_TYPE in
            <foreach collection="dto.verificationTypes" open="(" close=")" separator="," item="verificationType">
                #{verificationType}
            </foreach>
        </if>
        <if test="dto.excludeVerificationTypes!=null and dto.excludeVerificationTypes.size()>0 ">
            and (aov.VERIFICATION_TYPE is null or aov.VERIFICATION_TYPE not in
            <foreach collection="dto.excludeVerificationTypes" open="(" close=")" separator="," item="verificationType">
                #{verificationType}
            </foreach>
            )
        </if>

        <if test="dto.supplierTypes!=null and dto.supplierTypes.size()>0 ">
            and aov.SUPPLIER_TYPE in
            <foreach collection="dto.supplierTypes" open="(" close=")" separator="," item="supplierType">
                #{supplierType}
            </foreach>
        </if>
        <if test="dto.startConfirmTime!=null and dto.endConfirmTime!=null">
            and to_char(aov.CONFIRM_TIME, 'yyyy-mm-dd') between to_char( #{dto.startConfirmTime}, 'yyyy-mm-dd') and  to_char( #{dto.endConfirmTime}, 'yyyy-mm-dd')
        </if>

        <if test="dto.confirmUser!=null and dto.confirmUser!=''"> and aov.CONFIRM_USER like concat(concat('%',#{dto.confirmUser}),'%') </if>

        <if test="dto.businessQueryType!=null and @com.swcares.aps.ground.models.assurance.dto.AssuranceVerifyQueryRequest@isSupplierWebListQuery(dto.businessQueryType)">
            and ( (aos.ASSIGN_SUPPLIER='1' and aos.ASSIGN_SUPPLIER_COUNT=1)
                 or (aov.VERIFICATION_STATE = 'CONFIRM' and aov.SUPPLIER_CODE=aov.TENANT_CODE)
                )
            and aov.VERIFICATION_STATE in ('CONFIRM','WAIT')
        </if>
        <if test="dto.businessQueryType!=null and @com.swcares.aps.ground.models.assurance.dto.AssuranceVerifyQueryRequest@isSupplierH5ListQuery(dto.businessQueryType)">
            and ( (aos.ASSIGN_SUPPLIER='1' and aos.ASSIGN_SUPPLIER_COUNT=1)
                    or (aov.VERIFICATION_STATE = 'CONFIRM' and aov.SUPPLIER_CODE=aov.TENANT_CODE)
                )
            and aov.VERIFICATION_STATE in ('CONFIRM','WAIT')
        </if>





        <!--排序参数-->



        <if test="dto.verificationStateSort!=null and @com.swcares.aps.ground.models.assurance.dto.AssuranceVerifyQueryRequest@isBusinessQueryListSort(dto.verificationStateSort)">
            order by DECODE(VERIFICATION_STATE,
            'WAIT', 1,
            'CONFIRM', 2,
            'LOSE', 3)
        </if>

        <if test="dto.businessQueryType!=null and @com.swcares.aps.ground.models.assurance.dto.AssuranceVerifyQueryRequest@isSupplierWebListQuery(dto.businessQueryType)">
            ORDER BY gof.FLIGHT_DATE desc,gof.FLIGHT_NO desc ,gof.ORDER_STATE asc,DECODE(VERIFICATION_STATE,
            'WAIT', 1,
            'CONFIRM', 2,
            'LOSE', 3)
        </if>
    </select>
</mapper>