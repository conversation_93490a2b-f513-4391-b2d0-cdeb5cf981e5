package com.swcares.aps.web.bff.controller;

import com.swcares.aps.component.dict.model.vo.DictCacheVO;
import com.swcares.aps.component.dict.service.FindDictService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.aps.compensation.bff.web.controller <br>
 * Description：数据字典 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 10月26日 9:59 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/dict")
@Api(tags = "数据字典下拉框")
public class SysDictionaryController  extends BaseController {

    @Autowired
    private FindDictService findDictService;

    @ApiOperation(value = "加载全部数据字典")
    @GetMapping("/all")
    public BaseResult<Map<String,List<DictCacheVO>>> getAll(@RequestParam(required = false) @ApiParam(value = "字典类型")List<String> dictType){
        String[] dc = {};
        if(ObjectUtils.isNotEmpty(dictType)){
            dc = dictType.toArray(new String[dictType.size()]);

        }
        return ok(findDictService.findDicts(null, dc));
    }

}
