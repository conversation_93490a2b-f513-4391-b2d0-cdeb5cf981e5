package com.swcares.aps.compensation.model.privilege.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @Classname BusinessPrivilegeRequestDTO
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/11 15:11
 * @Version 1.0
 */
@Data
@ApiModel(value="BusinessPrivilegeRequestDTO", description="业务权限授权DTO,用于添加和修改对机场的业务授权")
public class BusinessPrivilegeRequestDTO {
    @ApiModelProperty(value = "被授权航司的三字码")
    private String recipientCode;

    @ApiModelProperty(value = "被授权航司的名称")
    private String recipientName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否禁用")
    private boolean disabled;

    @ApiModelProperty(value = "被授权的业务类型的编码")
    private List<String> businessTypeCodeList = new ArrayList<>();

    @ApiModelProperty(value = "被授予资金权限的业务类型的编码")
    private List<String> bankrollBusinessTypeCodeList = new ArrayList<>();

}
