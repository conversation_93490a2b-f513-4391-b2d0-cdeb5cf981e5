package com.swcares.aps.compensation.model.irregularflight.vo;

import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: CompensationReviewerVO
 * @projectName aps
 * @description:
 * @date 2021/11/25 14:58
 */
@Data
@SecretInfoEntity
@ApiModel(value="CompensationReviewerVO对象", description="审核人信息")
public class CompensationReviewerInfoVO {

    @ApiModelProperty(value = "操作人id")
    private Long reviewerId;
    @ApiModelProperty(value = "操作人姓名+工号")
    private String reviewerNameNo;
    @SecretValue
    @ApiModelProperty(value = "操作人联系方式")
    private String reviewerPhone;
    @ApiModelProperty(value = "操作人所在机构名称")
    private String orgName;
    @ApiModelProperty(value = "操作人所在机构id")
    private Long orgId;
    @ApiModelProperty(value = "操作人用户名")
    private String userName;
    @ApiModelProperty(value = "员工ID")
    private String employeeId;
    /**
     * 操作人姓名
     */
    private String reviewerName;
    /**
     * 操作人工号
     */
    private String reviewerNo;

}
