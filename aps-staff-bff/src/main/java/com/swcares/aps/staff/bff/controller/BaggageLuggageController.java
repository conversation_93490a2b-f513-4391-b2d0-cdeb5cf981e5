package com.swcares.aps.staff.bff.controller;

import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageInfoPageDTO;
import com.swcares.aps.staff.bff.service.BaggageLuggageService;
import com.swcares.aps.staff.bff.vo.StaffBaggageLuggageVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * ClassName：BaggageLuaggageController <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/3 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/h5/luggage")
@Api(tags = "实物箱包相关接口")
@ApiVersion(value = "工作人员端v1.0")
public class BaggageLuggageController extends BaseController {

    @Autowired
    private BaggageLuggageService baggageLuggageService;

    @PostMapping("/page")
    @ApiOperation(value = "箱包信息列表查询")
    public PagedResult<List<StaffBaggageLuggageVO>> page(@RequestBody LuggageInfoPageDTO dto){
        return baggageLuggageService.page(dto);
    }
}
