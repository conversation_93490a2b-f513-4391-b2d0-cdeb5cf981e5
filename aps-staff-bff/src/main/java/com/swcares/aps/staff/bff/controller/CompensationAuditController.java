package com.swcares.aps.staff.bff.controller;

import com.swcares.aps.compensation.model.compensation.dto.CompensationCashEditCommandDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialEditCommandDTO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditOperationVO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditReviewerVO;
import com.swcares.aps.compensation.model.irregularflight.dto.AuditProcessorDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationAuditInfoDTO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationAuditRecordVO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationAuditApi;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName：CompensationAuditController
 * @Description：补偿单审核接口
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/5/9 19:45
 * @version： v1.0
 */
@RestController
@RequestMapping("/h5/compensation/audit")
@Api(tags = "补偿单接口")
@ApiVersion(value = "补偿单审核接口v1.0.1")
@Slf4j
public class CompensationAuditController {


    private CompensationAuditApi compensationAuditApi;

    @Autowired
    public CompensationAuditController(CompensationAuditApi compensationAuditApi){
        this.compensationAuditApi=compensationAuditApi;
    }

    @GetMapping("/findAuditRecord")
    @ApiOperation(value = "查看审核记录")
    public BaseResult<List<CompensationAuditRecordVO>> findAuditRecord(@ApiParam(value = "赔偿单id",required=true)Long orderId,
                                                                       @ApiParam(value = "赔偿单号",required=true)String orderNo) {
        return compensationAuditApi.findAuditRecord(orderId,orderNo);
    }

    @GetMapping("/findReviewer")
    @ApiOperation(value = "查询可选审核人")
    public BaseResult<CompensationAuditReviewerVO> findReviewer(@ApiParam(value = "部门id") Long orgId, @ApiParam(value = "节点id",required=true) String taskId, @ApiParam(value = "审核人信息：姓名/工号") String userInfo, @ApiParam(value = "赔偿单id")Long orderId) {
        return compensationAuditApi.findAuditReviewer(orgId, userInfo,taskId,orderId);
    }

    @PostMapping("/saveReviewer")
    @ApiOperation(value = "审核人确认")
    public BaseResult<Object> saveReviewer(@RequestBody CompensationAuditInfoDTO dto) {
        return compensationAuditApi.saveAuditReviewer(dto);
    }

    @PostMapping("/operation")
    @ApiOperation(value = "审核操作-同意、不同意、驳回")
    public BaseResult<CompensationAuditOperationVO> auditOperation(@RequestBody AuditProcessorDTO dto) {
        return compensationAuditApi.auditOperation(dto);
    }

    /**
     * @title addMaterialCompensation
     * @description 创建实物补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单id
     */
    @PostMapping("/reSubmitMaterialCompensation")
    @ApiOperation(value = "重新提交实物补偿单接口")
    public BaseResult<CompensationAuditOperationVO> reSubmitMaterialCompensation(@RequestBody CompensationMaterialEditCommandDTO request){
        return compensationAuditApi.reSubmitMaterialCompensation(request);
    }

    /**
     * @title addCashCompensation
     * @description 创建现金补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单id
     */
    @PostMapping("/reSubmitCashCompensation")
    @ApiOperation(value = "重新提交现金补偿单接口")
    public BaseResult<CompensationAuditOperationVO> reSubmitCashCompensation(@RequestBody CompensationCashEditCommandDTO request){
        return compensationAuditApi.reSubmitCashCompensation(request);
    }

}
