<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.complaint.mapper.ComplaintCompensationInfoMapper">

    <select id="selectCompensationCountByPassengerInfo"
            resultType="com.swcares.aps.compensation.model.complaint.vo.CompensationCountByPassengerInfoVo">
        SELECT
        cpi.TKT_NO AS ticketNumber,
        COUNT( cpi.ID_NO ) AS frequency
        FROM
        COMPENSATION_ORDER_INFO coi
        LEFT JOIN COMPENSATION_PAX_INFO cpi ON coi.ID = cpi.ORDER_ID
        <where>
            AND cpi.TKT_NO IS NOT NULL AND coi.STATUS != '0'
            <if test="dto.flightNo != null and dto.flightNo != ''">
                AND coi.FLIGHT_NO = #{dto.flightNo}
            </if>
            <if test="dto.flightDate != null and dto.flightDate != ''">
                AND coi.FLIGHT_DATE = #{dto.flightDate}
            </if>
            <if test="dto.compensateType != null and dto.compensateType != ''">
                AND (
                (#{dto.compensateType} = '4' AND coi.ACCIDENT_TYPE IN ('1', '2', '3','4'))
                OR (#{dto.compensateType} != '4' AND coi.ACCIDENT_TYPE = #{dto.compensateType})
                )
            </if>
        </where>
        GROUP BY cpi.TKT_NO, cpi.ID_NO
    </select>

    <select id="selectCompensationList"
            resultType="com.swcares.aps.compensation.model.complaint.vo.CompensationCountVo">
        SELECT
        coi.STATUS as status,
        coi.CREATED_BY as createdUser,
        coi.CREATED_TIME as createdTime,
        cpi.RECEIVE_STATUS as receiveStatus,
        cpi.SWITCH_OFF as switchOff,
        coi.ORDER_NO as orderNo,
        coi.ACCIDENT_TYPE as accidentType,
        coi.COMPENSATE_TYPE as compensateType,
        SUM( cpi.CURRENT_AMOUNT ) as currentAmount
        FROM
        COMPENSATION_ORDER_INFO coi
        LEFT JOIN COMPENSATION_PAX_INFO cpi ON coi.ID = cpi.ORDER_ID
        <where>
            AND cpi.TKT_NO IS NOT NULL AND coi.STATUS != '0'
            <if test="dto.flightNo != null and dto.flightNo != ''">
                AND coi.FLIGHT_NO = #{dto.flightNo}
            </if>
            <if test="dto.flightDate != null and dto.flightDate != ''">
                AND coi.FLIGHT_DATE = #{dto.flightDate}
            </if>
            <if test="dto.ticketNumber != null and dto.ticketNumber != ''">
                AND cpi.TKT_NO = #{dto.ticketNumber}
            </if>
        </where>
        GROUP BY
        coi.STATUS,
        coi.CREATED_BY,
        coi.CREATED_TIME,
        cpi.RECEIVE_STATUS,
        cpi.SWITCH_OFF,
        coi.ORDER_NO,
        cpi.ID_NO,
        coi.ACCIDENT_TYPE,
        coi.COMPENSATE_TYPE
        ORDER BY coi.CREATED_TIME DESC
    </select>

    <select id="selectRelationshipOfCompensationList"
            resultType="com.swcares.aps.compensation.model.complaint.vo.AccidentConcatCompensationInfoVo">
        SELECT
        (select COUNT(1) FROM COMPENSATION_PAX_INFO cpi WHERE cpi.ORDER_ID = coi.ID GROUP BY cpi.ORDER_ID) as
        planCarryOutNum,
        (select COUNT(1) FROM COMPENSATION_PAX_INFO cpi WHERE cpi.ORDER_ID = coi.ID and cpi.RECEIVE_STATUS = '1' GROUP
        BY cpi.ORDER_ID) as actualCarryOutNum,
        coi.SUM_MONEY as planCurrentAmount,
        (select sum(cpi.CURRENT_AMOUNT) FROM COMPENSATION_PAX_INFO cpi WHERE cpi.ORDER_ID = coi.ID and
        cpi.RECEIVE_STATUS = '1' GROUP BY cpi.ORDER_ID) as trueCurrentAmount,
        coi.COMPENSATE_TYPE AS compensateType ,
        coi.ORDER_NO as orderNo,
        coi.STATUS as status,
        coi.ID as id,
        coi.SERVICE_CITY as serviceCity,
        coi.CREATED_BY as createdUser,
        coi.CREATED_TIME as createdTime
        FROM
        COMPENSATION_ORDER_INFO coi
        WHERE
        coi.ACCIDENT_ID = #{id}
    </select>

    <select id="checkInfo"
            resultType="com.swcares.aps.compensation.model.complaint.vo.ComplaintCompensationCheckInfoVo">
        SELECT
        coi.id AS id,
        coi.ACCIDENT_ID as accidentId,
        coi.ACCIDENT_TYPE as accidentType,
        coi.ORDER_NO AS orderId,
        coi.STATUS AS orderStatus,
        coi.CHOICE_SEGMENT AS segment,
        SUM( cpi.CURRENT_AMOUNT ) AS totalAmount,
        COUNT( cpi.ORDER_ID ) AS totalNumber,
        coi.CREATED_BY AS createdUser
        FROM
        COMPENSATION_ORDER_INFO coi
        LEFT JOIN COMPENSATION_PAX_INFO cpi ON cpi.ORDER_ID = coi.ID
        WHERE
        1=1
        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto)">
            and coi.ID IN (SELECT DISTINCT ORDER_ID FROM COMPENSATION_PAX_INFO WHERE TKT_NO IN
            <foreach item="item" collection="dto" open="(" separator="," close=")">
                #{item.ticketNumber}
            </foreach>
            )
        </if>
        <if test="id != null and id != ''">
            AND coi.ID != #{id}
        </if>
        GROUP BY
        coi.ACCIDENT_ID,
        coi.ACCIDENT_TYPE,
        coi.id,
        coi.ORDER_NO,
        coi.STATUS,
        coi.CHOICE_SEGMENT,
        coi.CREATED_BY
    </select>

    <select id="frequency" resultType="com.swcares.aps.compensation.model.complaint.vo.FrequencyVo">
        SELECT
        cpi.PAX_NAME as paxName,
        cpi.TKT_NO AS ticketNumber,
        COUNT( cpi.ID_NO ) AS frequency
        FROM
        COMPENSATION_ORDER_INFO coi
        LEFT JOIN COMPENSATION_PAX_INFO cpi ON coi.ID = cpi.ORDER_ID
        <where>
            AND cpi.TKT_NO IS NOT NULL
            <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto)">
                AND cpi.TKT_NO in
                <foreach item="item" collection="dto" open="(" separator="," close=")">
                    #{item.ticketNo}
                </foreach>
            </if>
        </where>
        GROUP BY
        cpi.PAX_NAME,
        cpi.TKT_NO,
        cpi.ID_NO
    </select>

    <select id="getCheckAccidentInfo" resultType="com.swcares.aps.compensation.model.complaint.entity.ComplaintAccidentInfoEntity">
        SELECT
        cai.ID,
        cai.ACCIDENT_STATUS,
        cai.ACCIDENT_SUB_TYPE,
        cai.ACCIDENT_ID,
        cai.CREATED_USER,
        cai.SEGMENT
        FROM
        COMPLAINT_ACCIDENT_INFO cai
        LEFT JOIN PASSENGER_ACCIDENT_INFO pai ON pai.ACCIDENT_PRIMARY_ID = cai.ID
        WHERE
        cai.ACCIDENT_STATUS != '4'
        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto)">
            AND  pai.TKT_NO in
            <foreach item="item" collection="dto" open="(" separator="," close=")">
                #{item.ticketNumber}
            </foreach>
        </if>
        <if test="id != null">
            AND cai.id != #{id}
        </if>
        GROUP BY
        cai.ID,
        cai.ACCIDENT_STATUS,
        cai.ACCIDENT_SUB_TYPE,
        cai.ACCIDENT_ID,
        cai.CREATED_USER,
        cai.SEGMENT
    </select>

</mapper>