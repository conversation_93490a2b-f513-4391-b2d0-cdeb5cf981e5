package com.swcares.aps.compensation.model.dataconfig.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * ClassName：com.swcares.aps.compensation.model.replace.dto.ReplaceRejectReasonDTO <br>
 * Description： 代人领取审核拒绝原由数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-12 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="ReplaceRejectReasonDTO对象", description="代人领取审核拒绝原由数据传输对象")
public class ReplaceRejectReasonDTO implements BaseDTO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "配置主类型，这里为固定值AUDIT_REASON")
    private String type;

    @ApiModelProperty(value = "配置子类型，这里为固定值REJECT_REASON")
    private String subType;

    @ApiModelProperty(value = "配置值，这里为具体的拒绝原因",required = true)
    @NotNull
    private String value;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "创建时间")
//    @JsonFormat(pattern="yyyy-MM-dd HH:mm")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "修改时间")
//    @JsonFormat(pattern="yyyy-MM-dd HH:mm")
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "机场或航司编码")
    private String airCode;

    @ApiModelProperty(value = "编码中文")
    private String codeCn;
}
