package com.swcares.aps.compensation.model.irregularflight.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * ClassName：com.swcares.aps.compensating.mode.irregularflight.vo <br>
 * Description：补偿标准 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 10月29日 9:42 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="CompensationStandardVO对象", description="赔偿单补偿标准")
public class CompensationStandardVO {

    @ApiModelProperty(value = "舱位类型(1-经济舱，2-公务舱)")
    private String classType;

    @ApiModelProperty(value = "成人比例")
    private String adultStd;

    @ApiModelProperty(value = "儿童比例")
    private String childStd;

    @ApiModelProperty(value = "婴儿赔偿标准")
    private String babyStd;

    @ApiModelProperty(value = "补偿金额 - 自定义金额时 取 此字段.不是自定义时此字段为舱位额度")
    private String cpsNum;

    @ApiModelProperty(value = "是否自定义金额（0否 1是）")
    private String isCustom;

    @ApiModelProperty(value = "成人赔偿标准是百分比还是实际金额,1是百分比2是实际金额")
    private Integer isPercentageAdult;

    @ApiModelProperty(value = "婴儿赔偿标准是百分比还是实际金额,1是百分比2是实际金额")
    private Integer isPercentageBaby;

    @ApiModelProperty(value = "儿童赔偿标准是百分比还是实际金额,1是百分比2是实际金额")
    private Integer isPercentageChild;

}
