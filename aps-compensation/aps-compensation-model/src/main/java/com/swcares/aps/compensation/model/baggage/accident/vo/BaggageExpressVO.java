package com.swcares.aps.compensation.model.baggage.accident.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: BaggageExpressVo
 * @projectName aps
 * @description: 异常行李事故单下快递信息VO
 * @date 2022/3/7 10:42
 */
@Data
@ApiModel(value="异常行李事故单下快递信息VO", description="异常行李事故单下快递信息VO")
public class BaggageExpressVO {
    @ApiModelProperty(value = "收件人")
    private String addressee;

    @ApiModelProperty(value = "收件地址")
    private String address;

    @ApiModelProperty(value = "联系电话")
    private String phone;

    @ApiModelProperty(value = "快递类型(手动添加，补偿单线下发放等)")
    private String expressType;

    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    @ApiModelProperty(value = "快递公司")
    private String expressCompany;

    @ApiModelProperty(value = "邮寄物品")
    private String expressGoods;

    @ApiModelProperty(value = "补偿单号")
    private String orderNo;

    @ApiModelProperty(value = "添加人")
    private String createdBy;

    @ApiModelProperty(value = "添加时间")
    private String createdTime;

}
