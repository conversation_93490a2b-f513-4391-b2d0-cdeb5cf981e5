package com.swcares.aps.staff.bff.service;

import com.swcares.aps.compensation.model.baggage.accident.dto.CompensationExpressInfoDTO;
import com.swcares.aps.compensation.model.compensation.dto.*;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditOperationVO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationBaseInfoVO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationMaterialDetailFinalVO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationMaterialQueriesListVO;
import com.swcares.aps.compensation.model.irregularflight.dto.FreezeOrderPaxDTO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationFromDetailsVO;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;

import java.util.List;

/**
 * @ClassName：CompensationOrderService
 * @Description：补偿单相关聚合服务
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/16 9:44
 * @version： v1.0
 */
public interface CompensationOrderService {


    BaseResult<String> addMaterialCompensation(CompensationMaterialAddCommandDTO request);

    BaseResult<String> editMaterialCompensation(CompensationMaterialEditCommandDTO request);

    BaseResult<String> addCashCompensation(CompensationCashAddCommandDTO request);

    BaseResult<String> editCashCompensation(CompensationCashEditCommandDTO request);

    BaseResult<CompensationAuditOperationVO> submit(String orderId);

    BaseResult<String> delete(String orderId);

    PagedResult<List<CompensationBaseInfoVO>> compensationBaseInfoPage(CompensationBaseInfoRequestDTO request);

    BaseResult<Object> compensationDetailSearch(String orderId);

    BaseResult<String> auditPassToTakeEffect(String orderId);

    BaseResult<String> closeCompensation(String orderId);

    PagedResult<List<Object>> findCashCompensationList(CompensationBaseInfoRequestDTO request);

    BaseResult<Object> freezeOrderPax(FreezeOrderPaxDTO freezeOrderPaxDTO);

    PagedResult<List<CompensationMaterialQueriesListVO>> findCompensationLuggageList(CompensationMaterialListDTO dto);

    BaseResult<CompensationMaterialDetailFinalVO> findCompensationLuggageDetailInfo(CompensationMaterialDetailDTO dto);

    BaseResult<Object> mailGrant(CompensationExpressInfoDTO compensationExpressInfoDTO);

    BaseResult<Object> offlineGrant(OfflineGrantDTO offlineGrantDTO);
    CompensationFromDetailsVO getCompensationDetailsVo(Long orderId);
}
