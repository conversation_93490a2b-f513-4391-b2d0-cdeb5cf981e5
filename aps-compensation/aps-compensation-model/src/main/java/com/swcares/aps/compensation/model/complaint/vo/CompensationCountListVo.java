package com.swcares.aps.compensation.model.complaint.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel(value = "CompensationCountListVo对象", description = "旅客投诉补偿总数列表")
public class CompensationCountListVo {


    @ApiModelProperty(value = "补偿总现金价值")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "补偿单列表")
    private List<CompensationCountVo> compensationCountVos;


}
