package com.swcares.aps.workflow.flowable.service;

import com.swcares.aps.workflow.dto.CurrentTaskActivityDTO;
import com.swcares.aps.workflow.dto.HistoryTaskAuditActivityDTO;
import org.flowable.engine.history.HistoricProcessInstance;

import java.util.List;

/**
 * ClassName：FlowableActiveService <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/2/10 <br>
 * @version v1.0 <br>
 */
public interface FlowableActivityService {

    /**
     *
     * @param historicProcessInstance
     * @return
     */
    List<CurrentTaskActivityDTO> getCurrentTaskActivity(HistoricProcessInstance historicProcessInstance);

    /**
     *
     * @param historicProcessInstance
     * @return
     */
    List<HistoryTaskAuditActivityDTO> getHistoryTaskAuditActivity(HistoricProcessInstance historicProcessInstance);

    /**
     *
     * @param historicProcessInstance
     * @return
     */
    HistoryTaskAuditActivityDTO getPreHistoryTaskAuditActivity(HistoricProcessInstance historicProcessInstance);

    /**
     * Title：getNextTask <br>
     * Description： 根据当前节点taskId,查下一个【用户节点】<br>
     * author：傅欣荣 <br>
     * date：2022/2/8 13:59 <br>
     * @param
     * @return
     */
    CurrentTaskActivityDTO getNextTask(String taskId);
}
