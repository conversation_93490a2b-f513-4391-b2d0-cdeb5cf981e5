package com.swcares.aps.compensation.model.compensation.dto;


import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CompensationMaterialDetailDTO
 * @Description：箱包补偿单详情DTO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 唐康
 * @Date： 2022/4/13 15:36
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationMaterialDetailDTO对象", description="箱包补偿单详情DTO")
public class CompensationMaterialDetailDTO  {
    @ApiModelProperty(value = "实物补偿单id")
    private String id;
}
