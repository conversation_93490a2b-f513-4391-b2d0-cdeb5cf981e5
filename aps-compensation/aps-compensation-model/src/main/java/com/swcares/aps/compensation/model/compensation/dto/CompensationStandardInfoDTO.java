package com.swcares.aps.compensation.model.compensation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CompensationStandardInfoDTO
 * @Description：现金补偿单详情下的赔偿标准信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 唐康
 * @Date： 2022/4/21 15:36
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationStandardInfoDTO", description="现金补偿单详情下补偿标准")
public class CompensationStandardInfoDTO {

    @ApiModelProperty(value = "舱位类型(1-经济舱，2-公务舱)")
    private String classType;

    @ApiModelProperty(value = "经济舱和商务舱额度")
    private String cpsNum;

    @ApiModelProperty(value = "成人比例")
    private String adultStd;

    @ApiModelProperty(value = "儿童比例")
    private String childStd;

    @ApiModelProperty(value = "携带儿童比例")
    private Double childPercent;

}
