/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：CustomerMessageApi <br>
 * Package：com.swcares.reaptv.usercenter.impl.com.swcares.reaptv.usercenter.api.innermail <br> 
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.usercenter.remote.api.innermail;

import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.components.msg.dto.CustomerMessageDepositoryDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * ClassName：com.swcares.reaptv.usercenter.impl.com.swcares.reaptv.usercenter.api.innermail.CustomerMessageApi <br>
 * Description：TODO：(这里用一句话描述这个类的作用) <br>
 * <AUTHOR> <br>
 * date 2022/8/26 10:06 <br>
 * @version v1.0.0 <br>
 */
@FeignClient(name = "aps-user-center", path = "/api/aps-user-center", contextId = "customerMessageApi")
public interface CustomerMessageApi {
    @PostMapping({"/msg/customer/send_now"})
    @ApiOperation("给旅客发送信息接口") BaseResult<Object> sendCustomerMobile(@RequestBody
            List<CustomerMessageDepositoryDTO> dto);
}
