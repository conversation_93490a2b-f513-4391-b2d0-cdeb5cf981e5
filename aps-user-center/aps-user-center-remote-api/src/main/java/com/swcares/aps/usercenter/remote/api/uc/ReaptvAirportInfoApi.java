/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：ReaptvAirportInfoController <br>
 * Package：com.swcares.reaptv.usercenter.impl.com.swcares.reaptv.usercenter.api.uc <br> 
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.usercenter.remote.api.uc;

import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.components.bd.entity.AirportInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * InterfaceName：com.swcares.reaptv.usercenter.impl.com.swcares.reaptv.usercenter.api.uc.ReaptvAirportInfoController <br>
 * Description：ReaptvAirportInfo远程调用接口 <br>
 * <AUTHOR> <br>
 * date 2022/10/17 22:56 <br>
 * @version v1.0.0 <br>
 */
@FeignClient(name = "aps-user-center", path = "/api/aps-user-center", contextId = "ReaptvAirportInfoApi")
public interface ReaptvAirportInfoApi {
    @GetMapping("/reaptv/airport/get_all")
    public BaseResult<List<AirportInfo>> getAll();
}
