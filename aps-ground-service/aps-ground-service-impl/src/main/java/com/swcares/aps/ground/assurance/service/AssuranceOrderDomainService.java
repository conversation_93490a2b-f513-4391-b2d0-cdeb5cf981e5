package com.swcares.aps.ground.assurance.service;

import com.swcares.aps.ground.enums.AssuranceOrderState;
import com.swcares.aps.ground.models.assurance.domain.AssuranceOrderDomain;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderChangeStatusCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderVerifyCommand;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderInfoDO;

/**
 * @ClassName：AssuranceDomainService
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/21 16:23
 * @version： v1.0
 */
public interface AssuranceOrderDomainService {

    /**
     * @title create
     * @description 创建
     * <AUTHOR>
     * @date 2024/8/22 10:25
     * @param orderDomain
     * @return
     */
    void create(AssuranceOrderDomain orderDomain);

    /**
     * @title update
     * @description 修改
     * <AUTHOR>
     * @date 2024/8/22 10:25
     * @param orderDomain
     * @return
     */
    void update(AssuranceOrderDomain orderDomain);

    /**
     * @title delete
     * @description 删除
     * <AUTHOR>
     * @date 2024/8/22 10:30
     * @param domainId
     * @return
     */
    void delete(String domainId);

    /**
     * @title checkAssuranceOrderChangeState
     * @description 一些简单的校验
     * <AUTHOR>
     * @date 2024/11/21 09:54
     * @param orderInfoDO
     * @param targetOrderState
     * @return
     */
    void checkAssuranceOrderChangeState(AssuranceOrderInfoDO orderInfoDO, AssuranceOrderState targetOrderState);

    /**
     * 修改状态
     * @param command
     */
    void changeAssuranceOrderStatus(AssuranceOrderChangeStatusCommand command);

    /**
     * @title submit
     * @description 提交
     * <AUTHOR>
     * @date 2024/8/22 10:34
     * @param domainId
     * @return
     */
    void submit(String domainId);

    /**
     * @title auditBack
     * @description 审核退回到提交节点
     * <AUTHOR>
     * @date 2024/8/22 10:35
     * @param domainId
     * @return
     */
    void auditBack(String domainId);

    /**
     * @title auditReject
     * @description 审核不通过
     * <AUTHOR>
     * @date 2024/8/22 10:35
     * @param domainId
     * @return
     */
    void auditReject(String domainId);

    /**
     * @title auditSuccess
     * @description 审核通过
     * <AUTHOR>
     * @date 2024/8/22 10:35
     * @param domainId
     * @return
     */
    void auditSuccess(String domainId);


    /**
     * @title verifyAssuranceOrder
     * @description 核销数据
     * <AUTHOR>
     * @date 2024/9/29 14:03
     * @param command
     * @return
     */
    void verifyAssuranceOrder(AssuranceOrderVerifyCommand command);

    /**
     * @title close
     * @description 关闭
     * <AUTHOR>
     * @date 2024/8/22 10:35
     * @param domainId
     * @return
     */
    void close(String domainId);

    /**
     * @title overTime
     * @description 逾期
     * <AUTHOR>
     * @date 2024/8/22 10:35
     * @param domainId
     * @return
     */
    void overTime(String domainId);

    /**
     * @title finish
     * @description 完成
     * <AUTHOR>
     * @date 2024/9/25 11:33
     * @return
     */
    void finish(String domainId);
}
