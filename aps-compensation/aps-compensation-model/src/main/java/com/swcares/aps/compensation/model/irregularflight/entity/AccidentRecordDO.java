package com.swcares.aps.compensation.model.irregularflight.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.entity.AccidentRecord <br>
 * Description：事故单将操作记录表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="AccidentRecord对象", description="事故单将操作记录表")
@TableName(value = "accident_record")
public class AccidentRecordDO{

    @ApiModelProperty(value = "事故单ID")
    private Long id;

    private Long tenantId;

    @ApiModelProperty(value = "事故单ID")
    private Long accidentId;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "操作人ID")
    private String operationId;

    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operationTime;

}
