package com.swcares.aps.compensation.model.irregularflight.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * ClassName：com.swcares.aps.compensating.mode.irregularflight.vo <br>
 * Description：赔偿单审核记录Vo <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 10月28日 17:22 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="CompensationAuditRecordVO对象", description="赔偿单审核记录")
public class CompensationAuditRecordVO {

    @ApiModelProperty(value = "审核人id")
    private String reviewerId;
    @ApiModelProperty(value = "节点名称（发起、同意、不同意、驳回）")
    private String nodeName;
    @ApiModelProperty(value = "节点状态（ready:待操作,complete:操作完成,auditing:审核中）")
    private String status;
    @ApiModelProperty(value = "操作人（姓名+工号+联系方式）")
    private String reviewerInfo;
    @ApiModelProperty(value = "等到操作人列表，当status为ready有值时,该字段有值")
    private List<CompensationAuditReviewerVO> waiteAuditReviewers;
    @ApiModelProperty(value = "操作时间")
    private LocalDateTime doneDate;
    @ApiModelProperty(value = "操作人所在机构名称")
    private String objName;
    @ApiModelProperty(value = "备注")
    private String remarks;

}
