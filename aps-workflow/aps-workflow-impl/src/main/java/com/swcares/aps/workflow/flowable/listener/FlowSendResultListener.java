package com.swcares.aps.workflow.flowable.listener;

import com.alibaba.fastjson.JSONObject;
import com.swcares.aps.workflow.common.constants.ProcessParamsConstants;
import com.swcares.aps.workflow.common.util.ProcessParamsUtil;
import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.dto.CompleteProcessParamsDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityVO;
import com.swcares.aps.workflow.dto.SyncWorkflowInfoDTO;
import com.swcares.aps.workflow.flowable.entity.SyncWorkflowTaskDO;
import com.swcares.aps.workflow.flowable.mapper.SyncWorkflowTaskMapper;
import com.swcares.aps.workflow.flowable.service.FlowableWorkflowService;
import com.swcares.aps.workflow.flowable.service.handler.SyncWorkflowTaskHandlerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.task.service.delegate.DelegateTask;
import org.flowable.task.service.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @ClassName：FlowTaskListener
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/6/12 14:12
 * @version： v1.0
 */
@Slf4j
@Component
public class FlowSendResultListener implements TaskListener {


    @Resource(name = "sendResultTaskHandlerServiceImpl")
    private SyncWorkflowTaskHandlerService syncWorkflowTaskHandlerService;

    @Autowired
    private FlowableWorkflowService flowableWorkflowService;

    @Autowired
    private SyncWorkflowTaskMapper syncWorkflowTaskMapper;
    @Override
    public void notify(DelegateTask delegateTask) {

        log.info("NeedResult任务监听器:{}", delegateTask);
        log.info("NeedResult任务监听器 ProcessDefinitionId:{}, getProcessInstanceId:{},taskID:{}", delegateTask.getProcessDefinitionId(),delegateTask.getProcessInstanceId(),delegateTask.getId());

        BaseQueryParamDTO paramDTO = BaseQueryParamDTO.builder().processInstanceId(delegateTask.getProcessInstanceId()).build();
        CurrentTaskActivityVO currentTaskActivityVO = flowableWorkflowService.currentUserTask(paramDTO);

        SyncWorkflowTaskDO taskDO=new SyncWorkflowTaskDO();
        taskDO.setOwnProcessId(delegateTask.getProcessInstanceId());
        taskDO.setOwnBusinessKey(currentTaskActivityVO.getBusiKey());
        taskDO.setLastOptionCode(currentTaskActivityVO.getPreOptionCode());
        taskDO.setLastComment(currentTaskActivityVO.getPreComment());
        taskDO.setExecStatus(SyncWorkflowTaskDO.STATUS_ING);
        taskDO.setExecTimes(0);
        taskDO.setCreatedTime(new Date());
        taskDO.setUpdatedTime(new Date());
        taskDO.setTaskType(SyncWorkflowTaskDO.TASK_TYPE_SEND_RESULT);
        syncWorkflowTaskMapper.insert(taskDO);

        syncWorkflowTaskHandlerService.syncSchedule(taskDO);

    }
}
