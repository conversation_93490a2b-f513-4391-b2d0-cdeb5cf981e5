package com.swcares.aps.basic.data.businessimpl.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：FlightSegmentVo
 * @Description：航班航段信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/14 13:11
 * @version： v1.0
 */
@Data
@ApiModel(value="FlightSegmentVo", description="航班航段信息")
public class FlightSegmentVO {

    @ApiModelProperty(value = "出发航站中文拼接")
    private String departPortCH;

    @ApiModelProperty(value = "到达航站中文拼接")
    private String arrivalPortCH;

    @ApiModelProperty(value = "出发航站")
    private String departPort;

    @ApiModelProperty(value = "到达航站")
    private String arrivalPort;
}
