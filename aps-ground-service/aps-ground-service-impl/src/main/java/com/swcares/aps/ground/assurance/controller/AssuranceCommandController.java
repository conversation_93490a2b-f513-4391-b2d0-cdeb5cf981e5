package com.swcares.aps.ground.assurance.controller;

import com.alibaba.fastjson.JSONObject;
import com.swcares.aps.ground.assurance.service.AssuranceOrderDomainService;
import com.swcares.aps.ground.assurance.service.AssuranceOrderManageService;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderChangeStatusCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderReviewerSaveCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderSaveCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderVerifyCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceWorkflowAuditCommand;
import com.swcares.aps.ground.models.assurance.vo.AssuranceWorkflowAuditResultVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @ClassName：AssuranceQueryController
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/21 16:30
 * @version： v1.0
 */
@RestController
@RequestMapping("/assurance/busi/command")
@Api(tags = {"保障单业务操作接口"})
@ApiVersion({"保障单相关Api"})
@Slf4j
public class AssuranceCommandController extends BaseController {

    @Autowired
    private AssuranceOrderManageService assuranceOrderManageService;

    @Autowired
    private AssuranceOrderDomainService assuranceOrderDomainService;

    @PostMapping("/save")
    @ApiOperation(value = "保存保障单信息")
    public BaseResult<Object> saveOrUpdateAssuranceOrder(@RequestBody @Valid AssuranceOrderSaveCommand command){
        log.info("保存保障单信息,{}", JSONObject.toJSONString(command));
        String orderId = assuranceOrderManageService.saveOrUpdateAssuranceOrder(command);
        return ok(orderId);
    }

    @PostMapping("/changeStatus")
    @ApiOperation(value = "修改保障单状态")
    public BaseResult<Object> changeAssuranceOrderStatus(@RequestBody @Valid AssuranceOrderChangeStatusCommand command){
        log.info("修改保障单状态,{}", JSONObject.toJSONString(command));
        Object result = assuranceOrderManageService.changeAssuranceOrderStatus(command);
        return ok(result);
    }

    @PostMapping("/workflow/process")
    @ApiOperation(value = "审核保障单")
    public BaseResult<AssuranceWorkflowAuditResultVO> processAssuranceOrderWorkflow(@RequestBody @Valid AssuranceWorkflowAuditCommand command){
        log.info("审核保障单,{}", JSONObject.toJSONString(command));
        AssuranceWorkflowAuditResultVO assuranceWorkflowAuditResultVO = assuranceOrderManageService.processAssuranceOrderWorkflow(command);
        return ok(assuranceWorkflowAuditResultVO);
    }


    @PostMapping("/workflow/saveAuditReviewer")
    @ApiOperation(value = "审核人确认")
    public BaseResult<Object> saveAuditReviewer(@RequestBody @Validated AssuranceOrderReviewerSaveCommand command) {
        log.info("审核人确认,{}", JSONObject.toJSONString(command));
        assuranceOrderManageService.saveAuditReviewer(command);
        return ok();
    }

    @GetMapping("/delete/{id}")
    @ApiOperation(value = "删除保障单信息")
    public BaseResult<Object> deleteAssuranceOrder(@PathVariable @ApiParam(value = "主键id", required = true) String id){
        log.info("删除保障单信息,{}", id);
        assuranceOrderDomainService.delete(id);
        return ok(id);
    }

    @PostMapping("/verify")
    @ApiOperation(value = "核销保障单")
    public BaseResult<Object> verifyAssuranceOrder(@RequestBody @Valid AssuranceOrderVerifyCommand command){
        log.info("核销保障单,{}", JSONObject.toJSONString(command));
        assuranceOrderDomainService.verifyAssuranceOrder(command);
        return ok();
    }

}
