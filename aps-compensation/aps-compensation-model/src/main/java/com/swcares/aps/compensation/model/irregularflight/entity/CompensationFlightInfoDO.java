package com.swcares.aps.compensation.model.irregularflight.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.entity.CompensationFlightInfo <br>
 * Description：赔偿航班信息 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="CompensationFlightInfo对象", description="赔偿航班信息")
@TableName(value = "compensation_flight_info")
public class CompensationFlightInfoDO{

    private static final long serialVersionUID = 1L;

    private Long tenantId;
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "创建者")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新者")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;
    @ApiModelProperty(value = "航班ID")
    @NotNull
    private String flightId;

    @ApiModelProperty(value = "航班号")
    @NotNull
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    @NotNull
    private String flightDate;

    @ApiModelProperty(value = "航段")
    @NotNull
    private String segment;

    @ApiModelProperty(value = "航段中文")
    private String segmentCh;

    @ApiModelProperty(value = "飞机号")
    private String planeCode;

    @ApiModelProperty(value = "机型")
    private String acType;

    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    @ApiModelProperty(value = "计划到达时间")
    private String sta;

    @ApiModelProperty(value = "预计起飞时间")
    private String etd;

    @ApiModelProperty(value = "预计到达时间")
    private String eta;

    @ApiModelProperty(value = "延误时长")
    private String delayTime;

    @ApiModelProperty(value = "航班延误原因")
    private String lateReason;

    @ApiModelProperty(value = "赔付单ID")
    private Long orderId;

}
