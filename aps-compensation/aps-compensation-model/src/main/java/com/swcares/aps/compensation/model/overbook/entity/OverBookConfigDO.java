package com.swcares.aps.compensation.model.overbook.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：OverBookConfigDO
 * @Description：超售规则配置
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/30 10:40
 * @version： v1.0
 */
@Data
@TableName("OVER_BOOK_CONFIG")
@ApiModel(value="超售规则配置DO", description="超售规则配置DO")
public class OverBookConfigDO extends BaseEntity {
    @ApiModelProperty(value = "类型：1改签，2退票")
    private String type;
    @ApiModelProperty(value = "左条件值（整数，延误小时 表达式<）")
    private String leftConditionValue;
    @ApiModelProperty(value = "右条件值（整数，延误小时 表达式<=）")
    private String rightConditionValue;
    @ApiModelProperty(value = "赔偿固定金额（类型为1退票时，此字段必须有值）")
    private String fixedMoney;
    @ApiModelProperty(value = "舱位全价（%）")
    private String fullPrice;
}
