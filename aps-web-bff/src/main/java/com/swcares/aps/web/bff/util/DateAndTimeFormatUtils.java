/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：DateFormatUtils <br>
 * Package：com.swcares.reaptv.msg.bff.util <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.web.bff.util;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * ClassName：com.swcares.reaptv.msg.bff.util.DateFormatUtils <br>
 * Description：TODO：(这里用一句话描述这个类的作用) <br>
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/13 9:54 <br>
 * @version:
 */
public class DateAndTimeFormatUtils {
    private static DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm");
    private static DateTimeFormatter datetimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static String formatDate(LocalDate date){
        if (date == null){
            return null;
        }

        return dateFormatter.format(date);
    }

    public static String formatDate(LocalDateTime date){
        if (date == null){
            return null;
        }

        return dateFormatter.format(date);
    }


    public static String formatDateTime(LocalDateTime time) {
        if (time == null){
            return null;
        }
        
        return datetimeFormatter.format(time);
    }

    public static String formatTime(LocalDateTime time) {
        if(time == null){
            return null;
        }
        
        return timeFormatter.format(time);
    }
}
