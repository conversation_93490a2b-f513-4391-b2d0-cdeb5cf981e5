package com.swcares.aps.compensation.model.overbook.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @ClassName：OverBookAccidentSaveDTO
 * @Description：H5超售-事故单信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/28 13:37
 * @version： v1.0
 */
@Data
public class OverBookAccidentSaveDTO {

    @ApiModelProperty(value = "【前端不用传】事故单状态（0草稿,1待处理,2处理中,3已结案,4作废）")
    private String accidentStatus;

    @ApiModelProperty(value = "事故单ID【草稿编辑必传】")
    private Long accidentId;

    @ApiModelProperty(value = "事故单号【草稿编辑必传】")
    private String accidentNo;

    @ApiModelProperty(value = "事故类型：1改签，2退票")
    private String type;

    @ApiModelProperty(value = "事故说明")
    @Size(max = 1000)
    private String accidentReason;


    @ApiModelProperty(value = "原航班ID")
    @NotBlank(message = "原航班ID不能为空")
    private String flightId;

    @ApiModelProperty(value = "原航班-航班号")
    private String flightNo;

    @ApiModelProperty(value = "原航班-航班日期")
    private String flightDate;

    @ApiModelProperty(value = "原航班-航段-中文格式")
    private String segmentCh;

    @ApiModelProperty(value = "原航班-计划起飞")
    private String std;


    @ApiModelProperty(value = "改签航班号")
    private String overBookFlightNo;

    @ApiModelProperty(value = "改签航班日期")
    private String overBookFlightDate;

    @ApiModelProperty(value = "改签航班计划起飞")
    private String overBookStd;

    @ApiModelProperty(value = "经济舱全价")
    private String fullEconomyFare;

    @ApiModelProperty(value = "附件图片")
    @Size(max = 10)
    private List<String> imgUrl;
}
