package com.swcares.aps.compensation.model.irregularflight.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * ClassName：com.swcares.aps.compensating.mode.irregularflight.dto <br>
 * Description：赔偿单旅客冻结筛选列表条件 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 11月08日 15:43 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="CompensationPaxFrozenDTO分页对象", description="赔偿单-已选旅客冻结列表筛选条件对象")
public class CompensationPaxFrozenDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "赔偿单id")
    private String orderId;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "证件号")
    private String idNo;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "领取状态(0未领取,1已领取,2处理中3已逾期) -多选")
    private List<String> receiveStatus;

    @ApiModelProperty(value = "旅客申领资格开关(默认0有资格，1取消领取资格=冻结)")
    private String switchOff;

    @ApiModelProperty(value = "已选航站 -多选")
    private List<String> choiceSegment;

    @ApiModelProperty(value = "开始购票时间")
    private String startTktIssueDate;

    @ApiModelProperty(value = "结束购票时间")
    private String endTktIssueDate;

    @ApiModelProperty(value = "值机状态 - 单选")
    private String paxStatus;

    @ApiModelProperty(value = "包含N舱 0不包含 1包含")
    private String isCabinN;

    @ApiModelProperty(value = "包含已取消旅客 0不包含 1包含")
    private String isCancel;

    @ApiModelProperty(value = "取消开始时间开始")
    private String cancelDateStart;

    @ApiModelProperty(value = "取消开始时间结束")
    private String cancelDateEnd;
}
