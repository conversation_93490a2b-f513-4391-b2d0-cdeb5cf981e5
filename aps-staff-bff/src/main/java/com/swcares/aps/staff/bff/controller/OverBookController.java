package com.swcares.aps.staff.bff.controller;

import com.swcares.aps.compensation.model.overbook.dto.*;
import com.swcares.aps.compensation.model.overbook.entity.OverBookConfigDO;
import com.swcares.aps.compensation.model.overbook.vo.OverBookConfigVO;
import com.swcares.aps.compensation.model.overbook.vo.OverBookH5DetailsVO;
import com.swcares.aps.compensation.model.overbook.vo.OverBookH5ListVO;
import com.swcares.aps.compensation.model.overbook.vo.OverBookPaxVO;
import com.swcares.aps.compensation.remote.api.irregularflight.OverBookAccidentApi;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @description 超售事故单
 * <AUTHOR>
 * @date 2024/6/4 10:10
 * @return
 */
@RestController
@RequestMapping("/h5/overbook")
@Api(tags = "超售事故单接口")
@ApiVersion(value = "超售事故单接口v1.0.1")
@Slf4j
public class OverBookController {

    @Autowired
    OverBookAccidentApi overBookAccidentApi;


    @PostMapping("/getH5List")
    @ApiOperation(value = "超售事故单信息列表查询")
    public PagedResult<List<OverBookH5ListVO>> getH5List(@RequestBody OverBookH5SearchDTO dto){
        return overBookAccidentApi.getH5List(dto);
    }

    @GetMapping("/overBookDetailInfo")
    @ApiOperation(value = "事故单详情信息")
    public BaseResult<OverBookH5DetailsVO> overBookDetailInfo(@RequestParam Long accidentId)  {
        return overBookAccidentApi.overBookDetailInfo(accidentId);
    }


    @PostMapping("/fltVerify")
    @ApiOperation(value = "航班验证")
    public BaseResult<List<OverBookPaxVO>> findSelectedPax(@RequestBody @Valid OverBookVerifyDTO dto) {
        return overBookAccidentApi.findSelectedPax(dto);
    }
    @PostMapping("/verifyAlikeOrder")
    @ApiOperation(value = "校验是否存在相同类型订单")
    public BaseResult verifyAlikeOrder(@RequestBody VerifyAlikeOrderDTO dto) {
        return overBookAccidentApi.verifyAlikeOrder(dto);
    }


    @PostMapping("/saveOverBook")
    @ApiOperation(value = "旅客超售-草稿||提交")
    public BaseResult<Map<String,Long>> saveOverBook(@RequestBody OverBookSaveDTO overBookSaveDTO) {
        return overBookAccidentApi.saveOverBook(overBookSaveDTO);
    }
    @PostMapping("/saveOverBookCompensation")
    @ApiOperation(value = "旅客超售补偿单-编辑保存")
    public BaseResult<Long> saveOverBookCompensation(@RequestBody OverBookCompensationSaveDTO compensationSaveDTO){
        return overBookAccidentApi.saveOverBookCompensation(compensationSaveDTO);
    }


    @PostMapping("/delOverBook")
    @ApiOperation(value = "旅客超售-删除草稿事故单")
    public BaseResult delOverBook(@RequestBody OverBookChangeDTO dto) {
        return overBookAccidentApi.delOverBook(dto);
    }


    @PostMapping("/toVoidOverBook")
    @ApiOperation(value = "旅客超售-作废待处理事故单")
    public BaseResult toVoidOverBook(@RequestBody OverBookChangeDTO dto) {
        return overBookAccidentApi.toVoidOverBook(dto);
    }

    @GetMapping("/getConfigInfo")
    @ApiOperation(value = "旅客超售-规则查询")
    public BaseResult<List<OverBookConfigVO>> getConfigInfo(@RequestParam(required = false)String time, @RequestParam(required = false)String type) {
        return overBookAccidentApi.getConfigInfo(time,type);
    }

    @PostMapping("/saveConfigInfo")
    @ApiOperation(value = "旅客超售-规则保存")
    public BaseResult saveConfigInfo(@RequestBody List<OverBookConfigDO> doList){
        return overBookAccidentApi.saveConfigInfo(doList);
    }

}
