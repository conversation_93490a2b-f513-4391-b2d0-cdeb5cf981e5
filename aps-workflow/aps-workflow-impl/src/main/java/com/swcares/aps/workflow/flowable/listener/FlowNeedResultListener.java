package com.swcares.aps.workflow.flowable.listener;


import com.swcares.aps.workflow.common.constants.ProcessParamsConstants;
import com.swcares.aps.workflow.common.util.ProcessParamsUtil;
import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.dto.CompleteProcessParamsDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityVO;
import com.swcares.aps.workflow.dto.SyncWorkflowInfoDTO;
import com.swcares.aps.workflow.flowable.entity.SyncWorkflowTaskDO;
import com.swcares.aps.workflow.flowable.mapper.SyncWorkflowTaskMapper;
import com.swcares.aps.workflow.flowable.service.FlowableWorkflowService;
import com.swcares.aps.workflow.flowable.service.SyncWorkflowLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.task.service.delegate.DelegateTask;
import org.flowable.task.service.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @ClassName：FlowTaskListener
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/6/12 14:12
 * @version： v1.0
 */
@Slf4j
@Component
public class FlowNeedResultListener implements TaskListener {

    @Autowired
    private FlowableWorkflowService flowableWorkflowService;

    @Autowired
    private SyncWorkflowTaskMapper syncWorkflowTaskMapper;

    @Autowired
    private SyncWorkflowLogService syncWorkflowLogService;

    @Override
    public void notify(DelegateTask delegateTask) {

        log.info("NeedResult任务监听器:{}", delegateTask);
        log.info("NeedResult任务监听器 ProcessDefinitionId:{}, getProcessInstanceId:{},taskID:{}", delegateTask.getProcessDefinitionId(),delegateTask.getProcessInstanceId(),delegateTask.getId());


        BaseQueryParamDTO paramDTO = BaseQueryParamDTO.builder().processInstanceId(delegateTask.getProcessInstanceId()).build();
        String syncWorkflowInfoJson = (String)flowableWorkflowService.getProcessVariable(paramDTO, ProcessParamsConstants.SYNC_WORKFLOW_INFO);
        if(StringUtils.isNotBlank(syncWorkflowInfoJson)){

            SyncWorkflowInfoDTO syncWorkflowInfoDTO = ProcessParamsUtil.parseSyncWorkflowInfo(syncWorkflowInfoJson);
            if(syncWorkflowInfoDTO!=null && StringUtils.equalsIgnoreCase(SyncWorkflowTaskDO.END_YES,syncWorkflowInfoDTO.getRequestEndFlag())){
                log.info("任务监听器 syncWorkflowInfo:{},同步任务已经结束，直接将结果告诉给当前流程即可", syncWorkflowInfoJson);
                CompleteProcessParamsDTO completeProcessParamsDTO=new CompleteProcessParamsDTO();
                completeProcessParamsDTO.setOptionCode(syncWorkflowInfoDTO.getRequestLastOptionCode());
                completeProcessParamsDTO.setTaskId(delegateTask.getId());
                if(StringUtils.isNotBlank(syncWorkflowInfoDTO.getRequestLastComment())){
                    completeProcessParamsDTO.setComment(syncWorkflowInfoDTO.getRequestLastComment());
                }
                completeProcessParamsDTO.setCanDoSyncProcessTask(CompleteProcessParamsDTO.CAN_DO_SYNC_PROCESS_TASK_SIGN);
                flowableWorkflowService.completeTask(completeProcessParamsDTO);
                log.info("任务监听器执行自动审核处理当前节点完成");
                syncWorkflowLogService.saveLog("NeedResultListener直接结束",syncWorkflowInfoJson);
                return;
            }
        }

        CurrentTaskActivityVO currentTaskActivityVO = flowableWorkflowService.currentUserTask(paramDTO);

        SyncWorkflowTaskDO taskDO=new SyncWorkflowTaskDO();
        taskDO.setOwnProcessId(delegateTask.getProcessInstanceId());
        taskDO.setOwnTaskId(delegateTask.getId());
        taskDO.setOwnBusinessKey(currentTaskActivityVO.getBusiKey());
        taskDO.setExecStatus(SyncWorkflowTaskDO.STATUS_WAITE);
        taskDO.setExecTimes(0);
        taskDO.setCreatedTime(new Date());
        taskDO.setUpdatedTime(new Date());
        taskDO.setTaskType(SyncWorkflowTaskDO.TASK_TYPE_NEED_RESULT);
        syncWorkflowTaskMapper.insert(taskDO);

    }
}
