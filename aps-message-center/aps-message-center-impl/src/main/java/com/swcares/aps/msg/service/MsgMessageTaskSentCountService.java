package com.swcares.aps.msg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.msg.model.dto.SmsSentResultBaseDTO;
import com.swcares.aps.msg.model.entity.MsgMessageTaskSentCount;
import com.swcares.aps.msg.model.entity.SmsTaskDetailInfo;

import java.util.List;

/**
 * @ClassName：MsgMessageTaskSentCountService
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/11/30 10:56
 * @version： v1.0
 */
public interface MsgMessageTaskSentCountService extends IService<MsgMessageTaskSentCount> {
    /***
     * @title smsSendResultStatistics
     * @description 对发送结果进行统计，发送维度有所不同
     * <AUTHOR>
     * @date 2022/11/28 9:25

     * @return void
     */
    public void smsSendResultStatistics(SmsSentResultBaseDTO baseDTO);

    /**
     * Title：updateMsgTaskSentCount <br>
     * Description：单独事务更新短信失败的为发送中 <br>
     * author：王磊 <br>
     * date：2022/12/7 15:11 <br>
     *
     * @param smsTaskDetailInfos
     * @param taskId             <br>
     * @return <br>
     */
    public void updateMsgTaskSentCount(List<SmsTaskDetailInfo> smsTaskDetailInfos, String taskId);
}
