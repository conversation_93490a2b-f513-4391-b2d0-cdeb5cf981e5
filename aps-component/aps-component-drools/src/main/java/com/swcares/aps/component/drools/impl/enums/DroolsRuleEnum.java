package com.swcares.aps.component.drools.impl.enums;

/**
 * ClassName：com.swcares.component.drools.impl.enums.DroolsRuleEnum <br>
 * Description：规则引擎枚举类 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021/10/19 16:18 <br>
 * @version v1.0 <br>
 */
public enum DroolsRuleEnum {

    IRREGULAR_FLIGHT("IRREGULAR_FLIGHT"),
    ABNORMAL_LUGGAGE( "ABNORMAL_LUGGAGE");

    private DroolsRuleEnum( String value) {
        this.value = value;
    }

    private String value;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

}
