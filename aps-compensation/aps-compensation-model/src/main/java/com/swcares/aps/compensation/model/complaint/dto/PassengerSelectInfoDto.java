package com.swcares.aps.compensation.model.complaint.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "PassengerSelectInfoDto对象", description = "选择旅客查询条件")
public class PassengerSelectInfoDto {

    @ApiModelProperty(value = "事故单类型",required = true)
    @NotBlank(message = "事故单类型不能为空")
    private String accidentType;

    @ApiModelProperty(value = "航班号",example = "CA8888",required = true)
    @NotBlank(message = "航班号不能为空")
    private String flightNo;

    @ApiModelProperty(value = "航班日期",example = "2024-01-01",required = true)
    @NotBlank(message = "航班日期不能为空")
    private String flightDate;

    @ApiModelProperty(value = "航段",example = "CTU-PEK,PEK-CTU",required = true)
    @NotBlank(message = "航段不能为空")
    private String segment;

    @ApiModelProperty(value = "航段中文",example = "成都CTU-上海PEK",required = true)
    private String segmentCh;

    @ApiModelProperty(value = "票号")
    private String ticketNumber;

    @ApiModelProperty(value = "用于旅客筛选")
    private String predicate;

    @ApiModelProperty(value = "用于旅客筛选-加密",hidden = true)
    private String encryptPredicate;

    private String compensateType;
}
