package com.swcares.aps.compensation.model.irregularflight.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.dto.CompensationOrderInfoPagedDTO <br>
 * Description：赔偿单信息 分页数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="CompensationOrderInfoPagedDTO分页对象", description="赔偿单信息")
public class CompensationOrderInfoPagedDTO extends PagedDTO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "补偿来源")
    private String source;

    @ApiModelProperty(value = "归属航司")
    private List<String> belongAirline;

    @ApiModelProperty(value = "航班起始日期")
    private String flightStartDate;

    @ApiModelProperty(value = "航班结束日期")
    private String flightEndDate;

    @ApiModelProperty(value = "起始航站")
    private String orgCity;

    @ApiModelProperty(value = "到达航站")
    private String dstCity;

    @ApiModelProperty(value = "赔偿单单号")
    private String orderNo;

    @ApiModelProperty(value = "服务航站，补偿航站")
    private String serviceCity;

    @ApiModelProperty(value = "补偿类型-1不正常航班，2异常行李，3超售")
    private String accidentType;

    @ApiModelProperty(value = "补偿子类型-1延误，2取消，3备降，4返航，5补班")
    private String fcType;

    @ApiModelProperty(value = "补偿方式 1现金，2虚拟，3实物")
    private String compensateType;

    @ApiModelProperty(value = "1草稿,2审核中,3审核不通过,4驳回,5审核通过,6生效,7关闭,8逾期")
    private List<String> status;

    @ApiModelProperty(value = "提交人ID")
    private String createdBy;

    @ApiModelProperty(value = "当前操作人ID")
    private String userId;

    //用于运行时行权限的实现，字段可能会有多个值，需要拼接到对应查询的sql中
    @ApiModelProperty(value = "事故单类型")
    private List<String> accidentTypes;

    @ApiModelProperty(value = "工作航站")
    private List<String> workStations;

    //用于支持前端多选条件查询列表,后台自己处理，前端不传
    @ApiModelProperty(value = "补偿子类型-1延误，2取消，3备降，4返航，5补班",hidden = true)
    private List<String> fcTypeList;

    @ApiModelProperty(value = "服务航站",hidden = true)
    private List<String> serviceCityList;

}
