/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：EmployeeSmsController <br>
 * Package：com.swcares.reaptv.msg.controller <br> 
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.usercenter.remote.api.uc.ReaptvAirportInfoApi;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.components.bd.entity.AirportInfo;
import com.swcares.aps.msg.model.dto.*;
import com.swcares.aps.msg.model.enums.SendStatusEnum;
import com.swcares.aps.msg.model.entity.EmployeeSmsRecord;
import com.swcares.aps.msg.model.util.ModelConverter;
import com.swcares.aps.msg.model.vo.EmployeeSmsRecordVO;
import com.swcares.aps.msg.model.vo.MessageStatusStaticsVO;
import com.swcares.aps.msg.model.vo.SmsSendResultVO;
import com.swcares.aps.msg.service.AssistantSmsService;
import com.swcares.aps.msg.service.EmployeeSmsRecordService;
import com.swcares.aps.msg.service.impl.SearchCriteriaUtils;
import com.swcares.aps.msg.util.AirportInfoUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.reaptv.msg.controller.EmployeeSmsController <br>
 * Description：员工短信发送记录接口。
 * <AUTHOR> <br>
 * date 2022/9/18 19:18 <br>
 * @version v1.0.0 <br>
 */
@RestController
@RequestMapping({"/msg/sms/employee"})
@Api(tags = {"员工短信发送记录接口"})
@ApiVersion({"Message Center Api v1.0"})
public class EmployeeSmsController {
    @Autowired
    private EmployeeSmsRecordService employeeSmsRecordService;

    @Autowired
    private ReaptvAirportInfoApi airportInfoApi;

    @Autowired
    private SearchCriteriaUtils searchCrteriaUtils;
    
    @Autowired
    private AssistantSmsService assistantSmsService;
    
    //发送短信
    @PostMapping("/send")
    @ApiOperation("给员工发送短信")
    public BaseResult<SmsSendResultVO> sendSms(@RequestBody EmployeeSmsSendDTO dto){
        EmployeeSmsRecord entity = employeeSmsRecordService.sendSms(dto);

        SmsSendResultVO result = new SmsSendResultVO();
        result.setStatus(entity.getStatus().getKey());
        result.setReason(entity.getReason());
        result.setId(entity.getId());
        return BaseResult.ok(result);
    }


    //发送短信
    @PostMapping("/send_list")
    @ApiOperation("给多个员工发送短信")
    public BaseResult<List<SmsSendResultVO>> sendMultiSms(@RequestBody List<EmployeeSmsSendDTO> dto){
        List<EmployeeSmsRecord> entities = employeeSmsRecordService.sendSms(dto);
        List<SmsSendResultVO> result = new ArrayList<>();
        
        for(EmployeeSmsRecord record : entities){
            SmsSendResultVO signleResult = new SmsSendResultVO();
            signleResult.setStatus(record.getStatus().getKey());
            signleResult.setReason(record.getReason());
            signleResult.setId(record.getId());
            result.add(signleResult);
        }
        
        return BaseResult.ok(result);

    }

    //分页查询短信列表
    @PostMapping("/list")
    @ApiOperation("分页获取员工短信发送记录")
    public PagedResult<List<EmployeeSmsRecordVO>> getEmployeeSmsRecord(@RequestBody EmployeeSmsSearchDTO searchDto){
        transferBusinessTypeSearchCriteria(searchDto);
        IPage<EmployeeSmsRecord> datas = employeeSmsRecordService.getEmployeeSmsRecordPage(searchDto);
        List<EmployeeSmsRecordVO> result = ModelConverter.convertToEmployeeSmsRecordVO(datas.getRecords());
        List<AirportInfo> airportInfos = airportInfoApi.getAll().getData();
        Map<String, AirportInfo> airportInfoMap = AirportInfoUtils.convertToMap(airportInfos);
        AirportInfoUtils.populateAirportInfo(result, airportInfoMap);
        return PagedResult.ok(result, datas.getTotal(), datas.getPages(), datas.getSize(), datas.getCurrent());
    }

    private void transferBusinessTypeSearchCriteria(EmployeeSmsSearchDTO dto){
        if (dto == null || dto.getBusinessTypes() == null){
            return;
        }

        List<String> transferedBusinessType = searchCrteriaUtils.getSmsBusinessTypeName(dto.getBusinessTypes());
        dto.setBusinessTypes(transferedBusinessType);
    }

    @RequestMapping("/statics")
    @ApiOperation("获取满足指定的条件的短信发送状态统计")
    public BaseResult<MessageStatusStaticsVO> getMessageStatusStatics(@RequestBody EmployeeSmsSearchDTO searchDto){
        transferBusinessTypeSearchCriteria(searchDto);
        List<MessageStatusStaticsDTO> staticsDTO = employeeSmsRecordService.getMessagesStatusStatics(searchDto);

        MessageStatusStaticsVO messageStatusStaticsVO = new MessageStatusStaticsVO();
        if (staticsDTO != null && !staticsDTO.isEmpty()){
            for(MessageStatusStaticsDTO dto : staticsDTO){
                messageStatusStaticsVO.addTotalCount(dto.getRecordCount());

                if (SendStatusEnum.SUCCESS.equals(dto.getStatus())){
                    messageStatusStaticsVO.addSuccessCount(dto.getRecordCount());
                }
            }
        }

        return BaseResult.ok(messageStatusStaticsVO);
    }

    //根据id获取短信详情
    @GetMapping("/detail")
    @ApiOperation("获取指定记录的详情")
    public BaseResult<EmployeeSmsRecordVO> getDetail(@RequestParam(name="id", required = true)Long id){
        EmployeeSmsRecord record = employeeSmsRecordService.getById(id);

        EmployeeSmsRecordVO vo = ModelConverter.convertToEmployeeSmsRecordVO(record);

        List<AirportInfo> airportInfos = airportInfoApi.getAll().getData();
        Map<String, AirportInfo> airportInfoMap = AirportInfoUtils.convertToMap(airportInfos);
        AirportInfoUtils.populateAirportInfo(vo, airportInfoMap);
        return BaseResult.ok(vo);
    }

    //逻辑删除短信发送记录
    @PostMapping("/delete")
    @ApiOperation("逻辑删除指定的记录")
    public BaseResult<Boolean> delete(@RequestBody DeleteRecordDTO dto){
        employeeSmsRecordService.batchDelete(dto.getIds());
        return BaseResult.ok(Boolean.TRUE);
    }



    //按照查询条件下载
    @PostMapping("/download")
    @ApiOperation("根据查询条件下载指定的员工短信发送记录")
    public void download(@RequestBody EmployeeSmsSearchDTO searchDto, HttpServletRequest request, HttpServletResponse response){
        transferBusinessTypeSearchCriteria(searchDto);
        employeeSmsRecordService.download(searchDto, request, response);
    }

    //按照指定的id进行下载
    @PostMapping("/downloadById")
    @ApiOperation("下载通过id指定的员工短信发送记录")
    public void downloadById(@RequestBody List<Long> ids, HttpServletRequest request, HttpServletResponse response){
        employeeSmsRecordService.downLoad(ids, request,response);
    }

    @PostMapping("/send_assistant_sms")
    @ApiOperation("给员工发送辅助类短信")
    public BaseResult<Object> sendAssistantSms(@RequestBody AssistantSmsSendDTO dto){
        assistantSmsService.sendSmsViaInnerChannel(dto);
        return BaseResult.ok();
    }

}
