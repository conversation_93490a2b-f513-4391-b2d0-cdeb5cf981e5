package com.swcares.aps.staff.bff.service.impl;

import com.beust.jcommander.internal.Lists;
import com.swcares.aps.compensation.model.dataconfig.vo.CompensationConfigVO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.aps.staff.bff.service.CompensationDataConfigService;
import com.swcares.aps.staff.bff.vo.CompensationDataConfigVO;
import com.swcares.baseframe.common.base.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @ClassName：CompensationDataConfigServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/23 16:03
 * @version： v1.0
 */
@Service
@Slf4j
public class CompensationDataConfigServiceImpl implements CompensationDataConfigService {

    public static final String ACCIDENT_DAMAGE_TYPE = "ACCIDENT_DAMAGE";
    public static final String BAGGAGE_ACCIDENT_RULE_TYPE = "BAGGAGE_ACCIDENT_RULE";

    private CompensationInfoApi compensationInfoApi;

    @Autowired
    public CompensationDataConfigServiceImpl(CompensationInfoApi compensationInfoApi){
        this.compensationInfoApi=compensationInfoApi;
    }

    @Override
    public BaseResult<Map<String, CompensationDataConfigVO>> getBaggageAccidentConfigs() {
        BaseResult<List<CompensationConfigVO>> vos = compensationInfoApi
                .getCompensationDataConfigByTypes(Lists.newArrayList(ACCIDENT_DAMAGE_TYPE, BAGGAGE_ACCIDENT_RULE_TYPE));

        List<CompensationConfigVO> compensationConfigVOS = vos.getData();
        Map<String, CompensationDataConfigVO> result = CompensationDataConfigVO.of(compensationConfigVOS);

        return BaseResult.ok(result);
    }
}
