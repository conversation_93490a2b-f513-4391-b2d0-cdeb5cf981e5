package com.swcares.aps.web.bff.controller;

import com.swcares.aps.compensation.model.rools.dto.CompensationRuleFactoryDTO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationRuleConfigApi;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/compensation/rule")
@Api(tags = "补偿单规则配置接口")
@ApiVersion(value = "补偿单规则配置接口 v1.0.1")
public class CompensationRuleConfigController {

    @Autowired
    CompensationRuleConfigApi compensationRuleConfigApi;

    @GetMapping("/getAll")
    @ApiOperation(value = "补偿单规则查询")
    public BaseResult<List<Map<String, CompensationRuleFactoryDTO>>> getAll() {
        return compensationRuleConfigApi.getAll();
    }

    @PostMapping("/save")
    @ApiOperation(value = "补偿单规则保存")
    public BaseResult<CompensationRuleFactoryDTO> save(@RequestBody CompensationRuleFactoryDTO request) {
        return compensationRuleConfigApi.save(request);
    }
}
