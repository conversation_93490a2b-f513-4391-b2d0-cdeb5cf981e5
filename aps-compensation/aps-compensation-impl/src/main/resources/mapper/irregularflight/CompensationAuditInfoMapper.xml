<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationAuditInfoMapper">
    <select id="findReviewer" resultType="com.swcares.aps.compensation.model.irregularflight.vo.CompensationReviewerInfoVO" databaseId="mysql">
        SELECT distinct
                CONCAT(ue.name,ue.job_number) as reviewerNameNo,ue.job_number,ue.name,
                uu.id reviewerId ,IFNULL(ue.phone,"") reviewerPhone ,ue.belong_to_org orgId ,uo.name orgName,
                uu.name userName,
                ue.id employeeId
        FROM uc_employee ue
        LEFT JOIN uc_user uu on ue.id=uu.employee_id
        LEFT JOIN uc_organization uo on ue.belong_to_org = uo.id AND uo.deleted != 1
        LEFT JOIN uc_user_role_join uur on uur.user_id=uu.id
        LEFT JOIN uc_role ur on ur.id=uur.role_id and ur.deleted!=1 and ur.status =1
        where 1=1 and uu.id is not null
        and uu.status=1 and uu.deleted !=1
        <choose>
            <when test="(userIds != null and userIds.length != 0) or (deptIds != null and deptIds.length !=0 )
            or (roleIds != null and roleIds.length !=0 )" >
            and  ( 1=2
            </when>
        </choose>

        <if test="userIds != null and userIds.length != 0">
            or uu.id in
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="deptIds != null and deptIds.length !=0 ">
            or ue.belong_to_org in (
                WITH RECURSIVE children AS (
                SELECT d.* FROM uc_organization d WHERE d.id in
                <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                UNION
                SELECT d.* FROM children c, uc_organization d WHERE d.parent_id = c.id
                ) SELECT id FROM children
            )
        </if>
        <if test="roleIds != null and roleIds.length !=0 ">
            or ur.id in
            <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
                #{roleId}
            </foreach>

        </if>

        <choose>
            <when test="(userIds != null and userIds.length != 0) or (deptIds != null and deptIds.length !=0 )
            or (roleIds != null and roleIds.length !=0 )" >
               )
            </when>
        </choose>

        <if test=" orgId != null and orgId !='' ">
            and ue.belong_to_org = #{orgId}
        </if>
        <if test="userInfo != null and userInfo !='' ">
            and (ue.name like "%"#{userInfo}"%" or ue.job_number like "%"#{userInfo}"%" )
        </if>
        order by convert(left(ue.name,1) USING gbk) ,ue.job_number asc
    </select>

    <select id="findReviewer" resultType="com.swcares.aps.compensation.model.irregularflight.vo.CompensationReviewerInfoVO" databaseId="oracle">
        SELECT DISTINCT
        CONCAT(ue.name, ue.job_number) reviewerNameNo,
        ue.job_number,
        ue.name,
        uu.id reviewerId,
        NVL(ue.phone,'') reviewerPhone,
        ue.belong_to_org orgId,
        uo.name orgName,
        uu.name userName,
        ue.id employeeId
        FROM
        uc_employee ue
        LEFT JOIN uc_user uu ON ue.id = uu.employee_id
        LEFT JOIN uc_organization uo ON ue.belong_to_org = uo.id AND uo.deleted != 1
        LEFT JOIN uc_user_role_join uur ON uur.user_id = uu.id
        LEFT JOIN uc_role ur ON ur.id = uur.role_id AND ur.deleted != 1 AND ur.status = 1
        WHERE
        1 = 1
        AND uu.id IS NOT NULL
        AND uu.status = 1
        AND uu.deleted !=1
        <choose>
            <when test="(userIds != null and userIds.length != 0) or (deptIds != null and deptIds.length !=0 )
            or (roleIds != null and roleIds.length !=0 )" >
                and  ( 1=2
            </when>
        </choose>

        <if test="userIds != null and userIds.length != 0">
            or uu.id in
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="deptIds != null and deptIds.length !=0 ">
            or ue.belong_to_org in (
            WITH children(id, parent_id) AS (
            SELECT
            id,parent_id
            FROM
            uc_organization
            WHERE
            id IN
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            UNION ALl
            SELECT
            d.id, d.parent_id
            FROM
            uc_organization d, children
            WHERE
            d.parent_id = children.id
            ) SELECT id FROM children
            )
        </if>
        <if test="roleIds != null and roleIds.length !=0 ">
            or ur.id in
            <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
                #{roleId}
            </foreach>

        </if>

        <choose>
            <when test="(userIds != null and userIds.length != 0) or (deptIds != null and deptIds.length !=0 )
            or (roleIds != null and roleIds.length !=0 )" >
                )
            </when>
        </choose>

        <if test=" orgId != null and orgId !='' ">
            and ue.belong_to_org = #{orgId}
        </if>
        <if test="userInfo != null and userInfo !='' ">
            and (ue.name like concat(concat('%',#{userInfo}),'%') or ue.job_number like concat(concat('%',#{userInfo}),'%') )
        </if>
        ORDER BY
        convert(SUBSTR(ue.name,1,1),'UTF8'),
        ue.job_number ASC
    </select>

    <select id="findOrganization" resultType="java.lang.String">
        SELECT DISTINCT uo.name FROM uc_organization uo
        LEFT JOIN uc_employee ue on ue.belong_to_org = uo.id
        LEFT JOIN uc_user uu on uu.employee_id = ue.id
        where 1=1
        <if test="userId != null and userId != ''" >
            and uu.id = #{userId}
        </if>
        <if test="orgId != null and orgId != '' " >
            and uo.id = #{orgId}
        </if>
    </select>

    <select id="findReviewerByTaskId" resultType="com.swcares.aps.compensation.model.irregularflight.vo.CompensationReviewerInfoVO" databaseId="mysql">
        SELECT distinct
        (SELECT CONCAT(ue.name,ue.job_number) as reviewerNameNo,
        ue.name as reviewerName,
        ue.job_number as reviewerNo,
        uu.id reviewerId ,IFNULL(ue.phone,"") reviewerPhone ,ue.belong_to_org orgId ,uo.name orgName,
        uu.name userName, ue.id employeeId
        FROM uc_employee ue
        LEFT JOIN uc_user uu on ue.id=uu.employee_id
        LEFT JOIN uc_organization uo on ue.belong_to_org = uo.id
        LEFT JOIN compensation_audit_info cai on cai.auditor_id = uu.id
        where 1=1 and uu.id is not null and cai.task_id = #{taskId}
        order BY fn_getpy(ue.name,3),ue.JOB_NUMBER
    </select>

    <select id="findReviewerByTaskId" resultType="com.swcares.aps.compensation.model.irregularflight.vo.CompensationReviewerInfoVO" databaseId="oracle">
        SELECT distinct
        CONCAT(ue.name,ue.job_number) reviewerNameNo,
        ue.name reviewerName,
        ue.job_number reviewerNo,
        uu.id reviewerId,
        nvl(ue.phone,'') reviewerPhone,
        ue.belong_to_org orgId,
        uo.name orgName,
        uu.name userName,
        ue.id employeeId
        FROM uc_employee ue
        LEFT JOIN uc_user uu on ue.id=uu.employee_id
        LEFT JOIN uc_organization uo on ue.belong_to_org = uo.id
        LEFT JOIN compensation_audit_info cai on cai.auditor_id = uu.id
        where 1=1 and uu.id is not null and cai.task_id = #{taskId}
        order BY fn_getpy(ue.name,3),ue.JOB_NUMBER
    </select>

    <select id="getUserInfo"  resultType="java.util.Map">
        SELECT uc.ID  ,uc.name userName
        FROM UC_USER uc
        LEFT JOIN UC_EMPLOYEE ue ON ue.ID = uc.EMPLOYEE_ID
        WHERE INSTR(  #{createdBy} , ue.JOB_NUMBER )>1
        and rownum =1
    </select>

</mapper>
