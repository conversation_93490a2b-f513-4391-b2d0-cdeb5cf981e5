package com.swcares.aps.compensation.model.overbook.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CheckCompensationAmountDTO
 * @Description：超售校验补偿单金额dto
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/6/5 15:40
 * @version： v1.0
 */
@Data
public class CompensationAmountCheckDTO {
    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "赔偿单单号【前端不用传】",hidden = true)
    private String orderNo;

    @ApiModelProperty(value = "事故类型：1改签，2退票")
    private String type;

    @ApiModelProperty(value = "经济舱全价")
    private String fullEconomyFare;

    @ApiModelProperty(value = "补偿方式(补偿方式 1现金，2虚拟，3实物)")
    private String compensateType;

    @ApiModelProperty(value = "补偿总金额")
    private String sumMoney;

    @ApiModelProperty(value = "1标准补偿金额 2自定义")
    private String ruleType;

    @ApiModelProperty(value = "补偿选择类型 1百分比 2固定金额")
    private String selectType;

    @ApiModelProperty(value = "超售规则id")
    private String overBookRuleId;

    @ApiModelProperty(value = "原航班-计划起飞")
    private String std;

    @ApiModelProperty(value = "改签航班计划起飞")
    private String overBookStd;
}
