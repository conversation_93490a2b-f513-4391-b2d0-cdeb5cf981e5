<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationPaxInfoMapper">

    <select id="findSelectedPax" resultType="com.swcares.aps.compensation.model.irregularflight.vo.ChoosePaxInfoVO" databaseId="mysql">
        select
        P.pax_id,
        P.pax_name,
        P.id_no,
        P.id_type idType,
        P.sex sex,
        p.org_city_airp ,
        p.dst_city_airp,
        P.is_child,
        P.with_baby,
        P.segment,
        P.tkt_no,
        P.main_class,
        P.sub_class,
        P.pnr,
        P.pax_status checkStatus,
        P.is_cancel,
        P.tkt_issue_date tktDate,
        P.telephone,
        get_dp_pax_paycount(P.pax_id,O.flight_no,O.flight_date) payCount,
        ifnull(get_apply_count(P.ORDER_ID,P.id),0) applyCount,
        ap.pay_status payStatus,
        ap.err_code_des payFailRemark
        FROM (
        SELECT COUNT(*) num,id_no no,pax_name name FROM compensation_pax_info p
        where p.order_id = #{orderId} GROUP BY p.pax_name,p.id_type,p.id_no HAVING COUNT(*)>1
        ORDER BY convert(pax_name USING gbk) COLLATE gbk_chinese_ci asc
        ) p2
        RIGHT JOIN
        compensation_pax_info p on p.id_no = p2.no
        LEFT JOIN compensation_order_info O on P.ORDER_ID = O.ID
        LEFT JOIN (
        select p.order_id,p.pax_info_id,
        pd.pay_status,
        pd.err_code_des,
        row_number() over(partition by p.apply_id,p.order_id,p.id order by pd.created_time DESC)  rowNum
        from  compensation_apply_pax p
        LEFT JOIN compensation_pay_record pd
        on p.id = pd.apply_pax_id and  p.apply_id = pd.apply_id and p.order_id=pd.order_id
        ) ap on p.order_id = ap.order_id and p.id = ap.pax_info_id and rowNum=1
        WHERE 1=1
        AND P.ORDER_ID = #{orderId}
        ORDER BY p2.num desc,convert(p2.name USING gbk) ,p.id desc
    </select>

    <select id="findSelectedPax" resultType="com.swcares.aps.compensation.model.irregularflight.vo.ChoosePaxInfoVO" databaseId="oracle">
        select
        P.pax_id,
        P.pax_name,
        P.id_no,
        P.id_type idType,
        P.sex sex,
        p.org_city_airp ,
        p.dst_city_airp,
        P.is_child,
        P.with_baby,
        P.segment,
        P.tkt_no,
        P.main_class,
        P.sub_class,
        P.pnr,
        P.pax_status checkStatus,
        P.is_cancel,
        P.tkt_issue_date tktDate,
        P.telephone,
        get_dp_pax_paycount(P.pax_id,O.flight_no,O.flight_date,O.ACCIDENT_TYPE) payCount,
        nvl(get_apply_count(P.ORDER_ID,P.id),0) applyCount,
        ap.pay_status payStatus,
        ap.err_code_des payFailRemark
        FROM (
        SELECT COUNT(*) num,id_no no,pax_name name FROM compensation_pax_info p
        where p.order_id = #{orderId} GROUP BY p.pax_name,p.id_type,p.id_no HAVING COUNT(*)>1
        ORDER BY convert(upper(pax_name),'UTF8') asc
        ) p2
        RIGHT JOIN
        compensation_pax_info p on p.id_no = p2.no
        LEFT JOIN compensation_order_info O on P.ORDER_ID = O.ID
        LEFT JOIN (
        select p.order_id,p.pax_info_id,
        pd.pay_status,
        pd.err_code_des,
        row_number() over(partition by p.apply_id,p.order_id,p.id order by pd.created_time DESC)  n
        from  compensation_apply_pax p
        LEFT JOIN compensation_pay_record pd
        on p.id = pd.apply_pax_id and  p.apply_id = pd.apply_id and p.order_id=pd.order_id
        ) ap on p.order_id = ap.order_id and p.id = ap.pax_info_id and n = 1
        WHERE 1=1
        AND P.ORDER_ID = #{orderId}
        ORDER BY p2.num desc,convert(p2.name,'UTF8') ,p.id desc
    </select>


    <select id="findChoicePax" resultType="com.swcares.aps.compensation.model.irregularflight.vo.CompensationChoicePaxVO" databaseId="mysql">
        with datas as(
            select P.TENANT_ID,
            P.id ,P.pax_id	,P.pax_name	,P.id_no, P.telephone,P.segment	,
            P.segment_ch ,P.org_city_airp , P.dst_city_airp ,	P.pax_status, P.is_cancel ,P.main_class ,P.sub_class, P.tkt_no ,P.pkg_no ,
            P.pkg_weight ,P.pkg_over_weight ,	P.with_baby	, P.baby_pax_name ,P.tkt_issue_date , P.current_amount , P.receive_channel , P.receive_way,
            P.receive_time	, P.order_id,	P.is_child,	 P.pnr, P.switch_off,P.cancel_time,
            P.created_by created_by,
            P.created_time,
            P.updated_by updated_by,
            P.updated_time,
            P.id_type idType,
            P.sex sex,
            P.receive_status receiveStatus,
            ifnull(get_apply_count(P.ORDER_ID,P.id),0) applyCount,
            ifnull(get_dp_pax_paycount(P.pax_id,O.flight_no,O.flight_date),0) payCount,
            ap.pay_status payStatus,ap.err_code_des payFailRemark
            FROM compensation_pax_info P
            LEFT JOIN compensation_order_info o on P.ORDER_ID = O.ID
            LEFT JOIN (
            select p.order_id,
            p.pax_info_id,
            pd.pay_status ,
            pd.err_code_des,
            row_number() over(partition by p.pax_info_id,p.order_id order by pd.created_time DESC) as rowNum
            from  compensation_apply_pax p
            LEFT JOIN compensation_pay_record pd
            on p.pax_info_id = pd.apply_pax_id and  p.apply_id = pd.apply_id and p.order_id=pd.order_id
            ) ap on p.order_id = ap.order_id and p.id = ap.pax_info_id and rowNum=1
            WHERE 1=1
            AND P.ORDER_ID = #{dto.orderId}
        <if test="dto.paxName != null and dto.paxName != ''">
             AND p.pax_name like "%" #{dto.paxName} "%"
        </if>
        <if test="dto.idNo != null and dto.idNo != ''">
            AND p.id_no = #{dto.idNo}
        </if>
        <if test="dto.tktNo != null and dto.tktNo != ''">
            AND p.tkt_no = #{dto.tktNo}
        </if>
        <if test="dto.choiceSegment != null and dto.choiceSegment.size()>0">
            AND p.segment in
            <foreach collection="dto.choiceSegment" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.startTktIssueDate != null and dto.startTktIssueDate != ''">
            AND DATE_FORMAT(p.tkt_issue_date,'%Y-%m-%d') &gt;= #{dto.startTktIssueDate}
        </if>
        <if test="dto.endTktIssueDate != null and dto.endTktIssueDate != ''">
            AND DATE_FORMAT(p.tkt_issue_date,'%Y-%m-%d') &lt;= #{dto.endTktIssueDate}
        </if>
        <if test="dto.cancelDateStart != null and dto.cancelDateStart != ''">
            AND DATE_FORMAT(p.cancel_time,'%Y-%m-%d') &gt;= #{dto.cancelDateStart} and p.is_cancel = 'Y'
        </if>
        <if test="dto.cancelDateEnd != null and dto.cancelDateEnd != ''">
            AND DATE_FORMAT(p.cancel_time,'%Y-%m-%d') &lt;= #{dto.cancelDateEnd} and p.is_cancel = 'Y'
        </if>

        <if test="dto.paxStatus != null and dto.paxStatus != '' ">
            and p.pax_status = #{dto.paxStatus}
        </if>

        <if test="dto.isCabinN != null and dto.isCabinN != ''">
            <choose>
                <when test="dto.isCabinN==0">and p.sub_class != 'N' </when>
            </choose>
        </if>

        <if test="dto.isCancel != null and dto.isCancel != ''">
            <choose>
                <when test="dto.isCancel==0">and p.is_cancel != 'Y' </when>
            </choose>
        </if>

        <if test="dto.switchOff != null and dto.switchOff != ''">
            AND p.switch_off = #{dto.switchOff}
        </if>


        <if test="dto.receiveStatus != null and dto.receiveStatus.size()>0">
            AND p.receive_status in
            <foreach collection="dto.receiveStatus" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        ),
        <!--&#45;&#45; 一人多座，同证件号、同姓名-->
        redData as(
        select pax_name ,id_no ,count(pax_name) from datas
        GROUP BY pax_name ,id_no  HAVING count(pax_name)>1
        ORDER BY CONVERT(pax_name USING gbk),pax_name,id_no asc
        )
       <!-- &#45;&#45; sort
        &#45;&#45; 是标红置顶，同名同证件 按姓名拼音首字母先后排序，按票号小到大排序。-->
        (select datas.* ,'1' isMarkedRed from redData  LEFT JOIN datas on datas.pax_name = redData.pax_name and datas.id_no = redData.id_no
        ORDER BY CONVERT(LEFT(redData.pax_name,1) USING gbk),redData.pax_name,redData.id_no asc ,datas.tkt_no asc  limit 999999999 )
        union all
        <!--&#45;&#45; 按姓名拼音首字母排序，出票时间倒序，相同时，补偿总次数倒序。-->
        (select datas.* ,'0' isMarkedRed from datas LEFT JOIN redData on datas.pax_name = redData.pax_name and datas.id_no = redData.id_no
        where  redData.pax_name is null
        ORDER BY CONVERT(LEFT(datas.pax_name,1) USING gbk) asc,datas.tkt_Issue_Date,payCount desc  limit 999999999
        )

    </select>

    <select id="findChoicePax" resultType="com.swcares.aps.compensation.model.irregularflight.vo.CompensationChoicePaxVO" databaseId="oracle">
        with datas as(
        select P.TENANT_ID,
        P.id ,P.pax_id	,P.pax_name	,P.id_no, P.telephone,P.segment	,
        P.segment_ch ,P.org_city_airp , P.dst_city_airp ,	P.pax_status, P.is_cancel ,P.main_class ,P.sub_class, P.tkt_no ,P.pkg_no ,
        P.pkg_weight ,P.pkg_over_weight ,	P.with_baby	, P.baby_pax_name ,P.tkt_issue_date , P.current_amount , P.receive_channel , P.receive_way,
        P.receive_time	, P.order_id,	P.is_child,	 P.pnr, P.switch_off,P.cancel_time,
        P.created_by created_by,
        P.created_time,
        P.updated_by updated_by,
        P.updated_time,
        P.id_type idType,
        P.sex sex,
        P.receive_status receiveStatus,
        nvl(get_apply_count(P.ORDER_ID,P.id),0) applyCount,
        nvl(get_dp_pax_paycount(P.pax_id,O.flight_no,O.flight_date,O.ACCIDENT_TYPE),0) payCount,
        ap.pay_status payStatus,ap.err_code_des payFailRemark
        FROM compensation_pax_info P
        LEFT JOIN compensation_order_info o on P.ORDER_ID = O.ID
        LEFT JOIN (
        select p.order_id,
        p.pax_info_id,
        pd.pay_status ,
        pd.err_code_des,
        row_number() over(partition by p.pax_info_id,p.order_id order by pd.created_time DESC)  n
        from  compensation_apply_pax p
        LEFT JOIN compensation_pay_record pd
        on p.pax_info_id = pd.apply_pax_id and  p.apply_id = pd.apply_id and p.order_id=pd.order_id
        ) ap on p.order_id = ap.order_id and p.id = ap.pax_info_id and n=1
        WHERE 1=1
        AND P.ORDER_ID = #{dto.orderId}
        <if test="dto.paxName != null and dto.paxName != ''">
            AND p.pax_name like concat(concat('%',#{dto.paxName}),'%')
        </if>
        <if test="dto.idNo != null and dto.idNo != ''">
            AND p.id_no = #{dto.idNo}
        </if>
        <if test="dto.tktNo != null and dto.tktNo != ''">
            AND p.tkt_no = #{dto.tktNo}
        </if>
        <if test="dto.choiceSegment != null and dto.choiceSegment.size()>0">
            AND p.segment in
            <foreach collection="dto.choiceSegment" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.startTktIssueDate != null and dto.startTktIssueDate != ''">
            AND to_char(p.tkt_issue_date,'yyyy-MM-dd') &gt;= #{dto.startTktIssueDate}
        </if>
        <if test="dto.endTktIssueDate != null and dto.endTktIssueDate != ''">
            AND to_char(p.tkt_issue_date,'yyyy-MM-dd') &lt;= #{dto.endTktIssueDate}
        </if>
        <if test="dto.cancelDateStart != null and dto.cancelDateStart != ''">
            AND to_char(p.cancel_time,'yyyy-MM-dd') &gt;= #{dto.cancelDateStart} and p.is_cancel = 'Y'
        </if>
        <if test="dto.cancelDateEnd != null and dto.cancelDateEnd != ''">
            AND to_char(p.cancel_time,'yyyy-MM-dd') &lt;= #{dto.cancelDateEnd} and p.is_cancel = 'Y'
        </if>

        <if test="dto.paxStatus != null and dto.paxStatus != '' ">
            and p.pax_status = #{dto.paxStatus}
        </if>

        <if test="dto.isCabinN != null and dto.isCabinN != ''">
            <choose>
                <when test="dto.isCabinN==0">and p.sub_class != 'N' </when>
            </choose>
        </if>

        <if test="dto.isCancel != null and dto.isCancel != ''">
            <choose>
                <when test="dto.isCancel==0">and p.is_cancel != 'Y' </when>
            </choose>
        </if>

        <if test="dto.switchOff != null and dto.switchOff != ''">
            AND p.switch_off = #{dto.switchOff}
        </if>
        <if test="dto.receiveStatus != null and dto.receiveStatus.size()>0">
            AND p.receive_status in
            <foreach collection="dto.receiveStatus" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ),
        <!--&#45;&#45; 一人多座，同证件号、同姓名-->
        redData as(
        select pax_name ,id_no ,count(pax_name) from datas
        GROUP BY pax_name ,id_no  HAVING count(pax_name)>1
        ORDER BY fn_getpy(pax_name,3),pax_name,id_no asc
        ),
        <!-- &#45;&#45; sort
         &#45;&#45; 是标红置顶，同名同证件 按姓名拼音首字母先后排序，按票号小到大排序。-->
        with_as_a as (select datas.* ,'1' isMarkedRed from redData  LEFT JOIN datas on datas.pax_name = redData.pax_name and datas.id_no = redData.id_no
        WHERE rownum &lt;= 999999999
        ORDER BY fn_getpy(substr(redData.pax_name,1,1),3),redData.pax_name,redData.id_no asc ,datas.tkt_no asc
        ),
        <!--&#45;&#45; 按姓名拼音首字母排序，出票时间倒序，相同时，补偿总次数倒序。-->
        with_as_b as (select datas.* ,'0' isMarkedRed from datas LEFT JOIN redData on datas.pax_name = redData.pax_name and datas.id_no = redData.id_no
        where  redData.pax_name is NULL AND rownum &lt;= 999999999
        ORDER BY fn_getpy(substr(datas.pax_name,1,1),3) asc,datas.tkt_Issue_Date,payCount DESC
        )
        select * FROM with_as_a UNION ALL SELECT * FROM with_as_b
    </select>


    <select id="page" resultType="com.swcares.aps.compensation.model.irregularflight.vo.CompensationPaxInfoVO">
        select * from compensation_pax_info
    </select>

    <select id="getPaxOrderInfo" resultType="com.swcares.aps.compensation.model.irregularflight.vo.PaxCompensationCountVO">
        select
        O.order_no orderNo,
        o.compensate_type compensateType,
        o.accident_type accidentType,
        O.status,
        p.current_amount sumMoney,P.switch_off switchOff,
        P.receive_status receiveStatus,
        P.telephone tel,
        O.CREATED_BY createdBy,
        O.created_time createdTime
        from compensation_pax_info P
        LEFT JOIN compensation_order_info O ON P.ORDER_ID = O.ID
        LEFT JOIN flight_accident_info AO ON O.accident_id = AO.ID
        WHERE O.status != 0
        AND P.PAX_ID = #{dto.paxId}
        AND O.FLIGHT_NO = #{dto.flightNo}
        AND O.FLIGHT_DATE = #{dto.flightDate}
        AND (
        (#{dto.compensateType} = '4' AND o.ACCIDENT_TYPE IN ('1', '2', '3','4'))
        OR (#{dto.compensateType} != '4' AND o.ACCIDENT_TYPE = #{dto.compensateType})
        )
        order by O.created_time desc
    </select>

    <select id="findListByCompensationId" resultType="com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO">
        select * from compensation_pax_info
        where order_id = #{compensationId}
        order by fn_getpy(pax_name,3) asc
    </select>

    <update id="freezeOrderPax">
        update compensation_pax_info set switch_off = #{dto.status}
        where order_id =  #{dto.orderId}
        <if test="dto.paxIds != null and dto.paxIds.size()>0">
            AND pax_id in
            <foreach collection="dto.paxIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="updateReceiveStatus" >
        update compensation_pax_info
        set receive_status = #{key}
        where id = #{compensationPaxId}
    </update>

    <update id="updateReceiveStatusByOrderNo" >
        update compensation_pax_info
        set receive_status = #{dto.receiveStatus} , receive_time = #{dto.receiveTime}
        where pax_id = #{dto.paxId} and order_id =#{dto.orderId}
    </update>


</mapper>
