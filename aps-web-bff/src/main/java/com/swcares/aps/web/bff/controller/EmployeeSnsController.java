/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：EmployeeSnsController <br>
 * Package：com.swcares.reaptv.msg.bff.controller <br> 
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.web.bff.controller;

import com.swcares.aps.msg.api.EmployeeSnsRecordApi;
import com.swcares.aps.msg.model.dto.DeleteRecordDTO;
import com.swcares.aps.msg.model.dto.EmployeeSnsSearchDTO;
import com.swcares.aps.msg.model.vo.EmployeeSnsRecordVO;
import com.swcares.aps.msg.model.vo.MessageStatusStaticsVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import feign.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.IOUtils;
import org.apache.ibatis.logging.Log;
import org.apache.ibatis.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Collection;
import java.util.List;

/**
 * ClassName：com.swcares.reaptv.msg.bff.controller.EmployeeSnsController <br>
 * Description：给员工发送社交软件记录的前端接口
 * <AUTHOR> <br>
 * date 2022/10/10 9:06 <br>
 * @version v1.0.0 <br>
 */
@RestController
@RequestMapping({"/msg/sns/employee"})
@Api(tags = {"员工社交软件信息发送记录接口"})
@ApiVersion({"Sns Api v1.0"})
public class EmployeeSnsController {
    @Autowired
    EmployeeSnsRecordApi employeeSnsRecordApi;

    protected Log log = LogFactory.getLog(getClass());


    @PostMapping("/list")
    @ApiOperation("分页获取员工社交信息发送记录")
    public PagedResult<List<EmployeeSnsRecordVO>> getEmployeeSnsRecord(@RequestBody
                                                                       EmployeeSnsSearchDTO searchDto){
        return employeeSnsRecordApi.getEmployeeSnsRecord(searchDto);
    }

    @PostMapping("/statics")
    @ApiOperation("获取满足指定的条件的信息发送状态统计")
    public BaseResult<MessageStatusStaticsVO> getMessageStatusStatics(@RequestBody
            EmployeeSnsSearchDTO searchDto){
        return employeeSnsRecordApi.getMessageStatusStatics(searchDto);
    }

    @PostMapping("/detail")
    @ApiOperation("获取指定记录的详情")
    public BaseResult<EmployeeSnsRecordVO> getDetails(@RequestParam(name="id", required = true)Long id){
        return employeeSnsRecordApi.getDetails(id);
    }

    @PostMapping("/delete")
    @ApiOperation("逻辑删除指定的记录")
    public BaseResult<Boolean> delete(@RequestBody DeleteRecordDTO dto){
        return employeeSnsRecordApi.delete(dto);
    }

    @PostMapping("/download")
    @ApiOperation("根据查询条件下载指定的员工社交信息发送记录")
    public void download(@RequestBody EmployeeSnsSearchDTO searchDto, HttpServletRequest request, HttpServletResponse response){
        Response feignResponse = employeeSnsRecordApi.download(searchDto);
        downloadByFeign(response, feignResponse);
    }

    @PostMapping("/downloadById")
    @ApiOperation("下载通过id指定的员工社交信息发送记录")
    public void downloadById(@RequestBody List<Long> idList, HttpServletRequest request, HttpServletResponse response){
        Response feignResponse = employeeSnsRecordApi.downloadById(idList);
        downloadByFeign(response, feignResponse);;
    }


    private void downloadByFeign(HttpServletResponse response, Response feignResponse) {
        response.setContentType("application/x-download;charset=UTF8");
        Collection<String> contentDispositionHeader = feignResponse.headers().get("Content-Disposition");
        response.setHeader("Content-Disposition",
                contentDispositionHeader.isEmpty()?"":contentDispositionHeader.iterator().next());

        Response.Body feignBody = feignResponse.body();
        try (OutputStream outputStream = response.getOutputStream();
                InputStream inputStream = feignBody.asInputStream()) {
            IOUtils.copy(inputStream, outputStream);
            outputStream.flush();
        } catch (IOException e) {
            log.error("导出员工社交软件消息发送记录出现异常",  e);
        }
    }
}
