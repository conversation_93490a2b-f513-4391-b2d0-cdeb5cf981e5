package com.swcares.aps.web.bff.service;

import com.swcares.aps.msg.model.dto.SmsTaskSendDetailDTO;
import com.swcares.aps.msg.model.dto.SmsTaskSendDetailExportDTO;

import javax.servlet.http.HttpServletResponse;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/
 * ClassName：com.swcares.reaptv.msg.bff.service.SmsTaskService
 * Description：(用一句话描述这个类或者接口表示什么)
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2022-12-1 10:45
 */
public interface SmsTaskService {


    void getTemplate(String type,HttpServletResponse response);

    void downloadFail(String id, HttpServletResponse response);

    void getSmsTaskSendDetailExport(SmsTaskSendDetailDTO dto, HttpServletResponse response);

    void exportSelectedSmsTaskSendDetails(SmsTaskSendDetailExportDTO dto, HttpServletResponse response);
}

