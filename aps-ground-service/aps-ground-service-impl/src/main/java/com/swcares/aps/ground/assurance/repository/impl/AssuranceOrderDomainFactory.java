package com.swcares.aps.ground.assurance.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.aps.basic.data.remoteapi.model.dto.FlightInfoDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightUnitInfoVO;
import com.swcares.aps.cpe.coordinate.model.enums.CustomerCategoryEnum;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderInfoMapper;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderServiceChoiceSupplierMapper;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderServiceMapper;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderServiceVerifyMapper;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderUserMapper;
import com.swcares.aps.ground.assurance.service.AssuranceFltPaxQueryService;
import com.swcares.aps.component.com.platform.PlatformTool;
import com.swcares.aps.ground.constants.GroundServicePubConstants;
import com.swcares.aps.ground.enums.EntityUseFlag;
import com.swcares.aps.ground.enums.OrderAssignSupplierFlag;
import com.swcares.aps.ground.enums.ServiceLevel;
import com.swcares.aps.ground.enums.ServiceTarget;
import com.swcares.aps.ground.models.assurance.domain.AssuranceOrderDomain;
import com.swcares.aps.ground.models.assurance.domain.AssuranceOrderServiceDomain;
import com.swcares.aps.ground.models.assurance.domain.AssuranceOrderUserDomain;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderSaveCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderServiceSaveCommand;
import com.swcares.aps.ground.models.assurance.dto.PaxQueryDTO;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderInfoDO;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderServiceChoiceSupplierDO;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderServiceDO;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderServiceVerifyDO;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderUserDO;
import com.swcares.aps.ground.models.assurance.vo.AssurancePassengerVO;
import com.swcares.aps.ground.models.servitem.dto.ServiceQueryRequest;
import com.swcares.aps.ground.models.servitem.vo.ServiceVO;
import com.swcares.aps.ground.models.supplier.dto.SupplierServiceFullRequest;
import com.swcares.aps.ground.models.supplier.vo.SupplierServiceFullVO;
import com.swcares.aps.ground.servitem.service.ServiceService;
import com.swcares.aps.ground.supplier.service.SupplierManageService;
import com.swcares.aps.privilege.model.dto.AirlineBusinessPrivilegeDTO;
import com.swcares.aps.privilege.service.BusinessPrivilegeService;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.lang.DateUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName：AssuranceOrderDomainFactory
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/22 10:31
 * @version： v1.0
 */
@Component
public class AssuranceOrderDomainFactory {

    public static final String HIGH_PAX_TYPE_CODE="高端旅客";

    @Autowired
    private AssuranceOrderInfoMapper assuranceOrderInfoMapper;

    @Autowired
    private AssuranceOrderServiceMapper assuranceOrderServiceMapper;

    @Autowired
    private AssuranceOrderServiceChoiceSupplierMapper assuranceOrderServiceChoiceSupplierMapper;

    @Autowired
    private AssuranceOrderUserMapper assuranceOrderUserMapper;

    @Autowired
    private AssuranceOrderServiceVerifyMapper assuranceOrderServiceVerifyMapper;

    @Autowired
    private PlatformTool platformTool;

    @Autowired
    private ServiceService serviceService;

    @Autowired
    private SupplierManageService supplierManageService;

    @Autowired
    private AssuranceFltPaxQueryService assuranceFltPaxQueryService;

    @Autowired
    private BusinessPrivilegeService businessPrivilegeService;
    /**
     * @title
     * @description @TODO
     * <AUTHOR>
     * @date 2024/8/22 14:13
     * @param orderId
     * @return
     */
    public AssuranceOrderDomain createById(String orderId){
        AssuranceOrderInfoDO orderInfoDO = assuranceOrderInfoMapper.selectById(orderId);
        if(orderInfoDO==null ||orderInfoDO.getDeleted()== GroundServicePubConstants.DELETED_YES){return null;}
        List<AssuranceOrderServiceDomain> orderServiceDomains=createOrderServiceDomainByOrderId(orderId);
        List<AssuranceOrderUserDomain> orderUserDomains=createOrderUserDomainByOrderId(orderId);
        return AssuranceOrderDomain.builder()
                .orderInfoDO(orderInfoDO)
                .orderServiceItemDomains(orderServiceDomains)
                .orderUserDomains(orderUserDomains)
                .build();
    }

    public List<AssuranceOrderUserDomain> createOrderUserDomainByOrderId(String orderId) {
        LambdaQueryWrapper<AssuranceOrderUserDO> userDOLambdaQueryWrapper = Wrappers
                .lambdaQuery(AssuranceOrderUserDO.class)
                .eq(AssuranceOrderUserDO::getOrderId, orderId);
        List<AssuranceOrderUserDO> assuranceOrderUserDOS = assuranceOrderUserMapper.selectList(userDOLambdaQueryWrapper);

        LambdaQueryWrapper<AssuranceOrderServiceVerifyDO> orderServiceVerifyDOLambdaQueryWrapper = Wrappers
                .lambdaQuery(AssuranceOrderServiceVerifyDO.class)
                .eq(AssuranceOrderServiceVerifyDO::getOrderId, orderId);

        Map<String, List<AssuranceOrderServiceVerifyDO>> orderUserIdToMp = assuranceOrderServiceVerifyMapper
                .selectList(orderServiceVerifyDOLambdaQueryWrapper)
                .stream()
                .collect(Collectors
                        .groupingBy(AssuranceOrderServiceVerifyDO::getOrderUserId));

        return assuranceOrderUserDOS
                .stream()
                .map(t -> AssuranceOrderUserDomain.builder()
                        .orderUserDO(t)
                        .serviceCertificateDos(orderUserIdToMp.get(t.getId()))
                        .build()).collect(Collectors.toList());
    }

    public List<AssuranceOrderUserDomain> createOrderUserDomainByOrderServiceId(String orderServiceId) {
        LambdaQueryWrapper<AssuranceOrderServiceVerifyDO> orderServiceVerifyDOLambdaQueryWrapper = Wrappers
                .lambdaQuery(AssuranceOrderServiceVerifyDO.class)
                .eq(AssuranceOrderServiceVerifyDO::getOrderServiceId, orderServiceId);
        List<AssuranceOrderServiceVerifyDO> assuranceOrderServiceVerifyDOS = assuranceOrderServiceVerifyMapper
                .selectList(orderServiceVerifyDOLambdaQueryWrapper);

        List<String> orderUserIdList = assuranceOrderServiceVerifyDOS.stream()
                .map(AssuranceOrderServiceVerifyDO::getOrderUserId)
                .collect(Collectors.toList());

        Map<String, List<AssuranceOrderServiceVerifyDO>> orderUserIdToMp = assuranceOrderServiceVerifyDOS
                .stream()
                .collect(Collectors
                        .groupingBy(AssuranceOrderServiceVerifyDO::getOrderUserId));

        LambdaQueryWrapper<AssuranceOrderUserDO> userDOLambdaQueryWrapper = Wrappers
                .lambdaQuery(AssuranceOrderUserDO.class)
                .in(AssuranceOrderUserDO::getId, orderUserIdList);
        List<AssuranceOrderUserDO> assuranceOrderUserDOS = assuranceOrderUserMapper.selectList(userDOLambdaQueryWrapper);

        return assuranceOrderUserDOS
                .stream()
                .map(t -> AssuranceOrderUserDomain.builder()
                        .orderUserDO(t)
                        .serviceCertificateDos(orderUserIdToMp.get(t.getId()))
                        .build()).collect(Collectors.toList());
    }

    public List<AssuranceOrderServiceDomain> createOrderServiceDomainByOrderId(String orderId) {
        LambdaQueryWrapper<AssuranceOrderServiceDO> orderServiceDOLambdaQueryWrapper = Wrappers
                .lambdaQuery(AssuranceOrderServiceDO.class)
                .eq(AssuranceOrderServiceDO::getOrderId, orderId);
        List<AssuranceOrderServiceDO> assuranceOrderServiceDOS = assuranceOrderServiceMapper.selectList(orderServiceDOLambdaQueryWrapper);

        LambdaQueryWrapper<AssuranceOrderServiceChoiceSupplierDO> serviceChoiceSupplierDOLambdaQueryWrapper = Wrappers
                .lambdaQuery(AssuranceOrderServiceChoiceSupplierDO.class)
                .eq(AssuranceOrderServiceChoiceSupplierDO::getOrderId, orderId);
        Map<String, List<AssuranceOrderServiceChoiceSupplierDO>> orderServiceIdToMp = assuranceOrderServiceChoiceSupplierMapper
                .selectList(serviceChoiceSupplierDOLambdaQueryWrapper)
                .stream()
                .collect(Collectors.groupingBy(AssuranceOrderServiceChoiceSupplierDO::getOrderServiceId));

        return assuranceOrderServiceDOS
                .stream()
                .map(t -> AssuranceOrderServiceDomain.builder()
                        .orderServiceDO(t)
                        .choiceSupplierDOS(orderServiceIdToMp.get(t.getId()))
                        .build()).collect(Collectors.toList());
    }

    /**
     * 通过接口请求数据构造domain数据
     * @param command
     * @return
     */
    public AssuranceOrderDomain createByOrderSaveCommand(AssuranceOrderSaveCommand command) {
        if(platformTool.isAirportPlatform()){
            if(StringUtils.isBlank(command.getBelongAirlineCode())){
                throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"所属航司CODE不能为空");
            }
            command.setServiceAirport(platformTool.getCurrentTenantCode());
            boolean verifyBusinessPrivilege = verifyBusinessPrivilege(platformTool.getCurrentTenantCode(), command.getBelongAirlineCode());
            if(!verifyBusinessPrivilege){
                throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,command.getBelongAirlineCode()+"航司未对当前机场授权保障单业务,不能创建保障单");
            }
        }
        if(platformTool.isAirlinePlatform()){
            if(StringUtils.isBlank(command.getServiceAirport())){
                throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"服务场站不能为空");
            }
            command.setBelongAirlineCode(platformTool.getCurrentTenantCode());
        }
        AssuranceOrderInfoDO assuranceOrderInfoDO = command.createDO();
        List<AssuranceOrderServiceDomain> orderServiceDomains=createOrderServiceDomains(command);
        List<AssuranceOrderUserDO> orderUserDOS=createOrderUserDOs(command);

        List<AssuranceOrderUserDomain> userDomains=createUserDomainsByService(orderUserDOS,orderServiceDomains);
        orderServiceDomains = filterNotServiceAndWrapTotalUserAmount(userDomains,orderServiceDomains);

        AssuranceOrderDomain orderDomain = AssuranceOrderDomain.builder().orderUserDomains(userDomains)
                .orderServiceItemDomains(orderServiceDomains)
                .orderInfoDO(assuranceOrderInfoDO)
                .build();
        orderDomain.initCreateAndTenantInfo(platformTool.getCurrentUserTenantTypeInfo(),command.getBelongAirlineCode());
        return orderDomain;
    }

    private boolean verifyBusinessPrivilege(String airportCode, String airlineCode) {
        List<AirlineBusinessPrivilegeDTO> businessPrivilegeDTOS = businessPrivilegeService.getByTenantCode(airportCode);
        if(CollectionUtils.isEmpty(businessPrivilegeDTOS)){
            return false;
        }
        Optional<AirlineBusinessPrivilegeDTO> any = businessPrivilegeDTOS.stream()
                .filter(t -> t.isAssignBusinessType(AirlineBusinessPrivilegeDTO.BUSINESS_TYPE_SERVICE_SUPPORT))
                .filter(t -> {
                    String privilegeTenant = t.getPrivilegeTenantTypeByTenantCode(airportCode);
                    String privilegeTenantTypeByTenantCode = t.getPrivilegeTenantCodeByTenantCode(airportCode);
                    return CustomerCategoryEnum.AIRLINE.getCode().equals(privilegeTenant)
                            && airlineCode.equals(privilegeTenantTypeByTenantCode);
                }).findAny();
        return any.isPresent();
    }

    private List<AssuranceOrderServiceDomain> filterNotServiceAndWrapTotalUserAmount(List<AssuranceOrderUserDomain> userDomains, List<AssuranceOrderServiceDomain> orderServiceDomains) {
        Set<String> serviceIds = userDomains.stream().flatMap(t -> t.getServiceCertificateDos().stream()).map(AssuranceOrderServiceVerifyDO::getServiceId).collect(Collectors.toSet());
        List<AssuranceOrderServiceDomain> assuranceOrderServiceDomains = orderServiceDomains.stream().filter(t -> serviceIds.contains(t.getServiceId())).collect(Collectors.toList());
        Map<String, Long> serviceIdToCountMp = userDomains.stream().flatMap(t -> t.getServiceCertificateDos().stream()).collect(Collectors.groupingBy(AssuranceOrderServiceVerifyDO::getServiceId, Collectors.counting()));
        for(AssuranceOrderServiceDomain orderServiceDomain:assuranceOrderServiceDomains){
            String serviceId = orderServiceDomain.getServiceId();
            Long aLong = serviceIdToCountMp.get(serviceId);
            Integer servicePerAmount = orderServiceDomain.getOrderServiceDO().getServicePerAmount();

            orderServiceDomain.getOrderServiceDO().setTotalUserAmount(new BigDecimal(aLong).divide(new BigDecimal(servicePerAmount)).intValue());
        }

        return assuranceOrderServiceDomains;
    }

    private List<AssuranceOrderUserDomain> createUserDomainsByService(List<AssuranceOrderUserDO> orderUserDOS, List<AssuranceOrderServiceDomain> orderServiceDomains) {
        List<AssuranceOrderUserDomain> orderUserDomains=new ArrayList<>(orderUserDOS.size());
        for(AssuranceOrderUserDO userDO:orderUserDOS){
            AssuranceOrderUserDomain orderUserDomain = AssuranceOrderUserDomain.builder().orderUserDO(userDO).build();
            if(orderUserDomain.createServiceCertificateDos(orderServiceDomains)){
                orderUserDomains.add(orderUserDomain);
            }
        }
        if(CollectionUtils.isEmpty(orderServiceDomains)){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"旅客匹配服务项失败");
        }
        return orderUserDomains;
    }

    private List<AssuranceOrderServiceDomain> createOrderServiceDomains(AssuranceOrderSaveCommand command) {
        List<String> serviceIds = command.getOrderServiceSaveCommands().stream().map(AssuranceOrderServiceSaveCommand::getServiceId).collect(Collectors.toList());
        ServiceQueryRequest request=new ServiceQueryRequest();
        request.setPageSize(serviceIds.size()+100);
        request.setAirportCode(command.getServiceAirport());
        request.setIds(serviceIds);
        List<ServiceVO> records = serviceService.pageQuery(request).getRecords();
        if(CollectionUtils.isEmpty(records) || records.size()!=serviceIds.size()){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法服务项数据");
        }

        Optional<ServiceVO> any1 = records.stream().filter(t -> !command.getServiceTarget().equals(t.getServiceTarget())).findAny();
        if(any1.isPresent()){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"存在服务对象不合法服务项数据");
        }
        List<SupplierServiceFullVO> supplierServiceFulls;
        //校验供应商
        if(command.getAssignSupplier().equals(OrderAssignSupplierFlag.ASSIGN.getCode())){
            Optional<AssuranceOrderServiceSaveCommand> any = command.getOrderServiceSaveCommands().stream().filter(t -> CollectionUtils.isEmpty(t.getSupplierServiceIds())).findAny();
            if(any.isPresent()){ throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"指定供应商不能为空");}
            SupplierServiceFullRequest req=new SupplierServiceFullRequest();
            List<String> supplierServiceIds = command.getOrderServiceSaveCommands().stream().flatMap(t -> t.getSupplierServiceIds().stream()).collect(Collectors.toList());
            req.setSupplierServiceIds(supplierServiceIds);
            req.setAirportCode(command.getServiceAirport());
            req.setBeginDate(command.getFlightDate());
            req.setEndDate(command.getFlightDate());
            req.setUseAble(EntityUseFlag.USABLE.getCode());
            supplierServiceFulls = supplierManageService.getSupplierServiceFulls(req);
        }else{
            SupplierServiceFullRequest req=new SupplierServiceFullRequest();
            req.setServiceIds(serviceIds);
            req.setAirportCode(command.getServiceAirport());
            req.setBeginDate(command.getFlightDate());
            req.setEndDate(command.getFlightDate());
            req.setUseAble(EntityUseFlag.USABLE.getCode());
            supplierServiceFulls = supplierManageService.getSupplierServiceFulls(req);
        }

        if(CollectionUtils.isEmpty(supplierServiceFulls)){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"已无满足条件的服务项或供应商，请修改后提交");
        }
        List<String> collect = supplierServiceFulls.stream().map(SupplierServiceFullVO::getServiceId).collect(Collectors.toList());
        List<String> notServiceIds = serviceIds.stream().filter(t -> !collect.contains(t)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(notServiceIds)){
            String notServiceNames = records.stream().filter(t -> notServiceIds.contains(t.getId())).map(ServiceVO::getServiceName).collect(Collectors.joining(","));
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"没有供应商提供选择的["+notServiceNames+"]服务");
        }

        Map<String, List<AssuranceOrderServiceSaveCommand>> serviceIdToServiceSaveCommandMp = command.getOrderServiceSaveCommands().stream().collect(Collectors.groupingBy(AssuranceOrderServiceSaveCommand::getServiceId));
        Map<String, List<SupplierServiceFullVO>> serviceIdToSupplierServiceMp = supplierServiceFulls.stream().collect(Collectors.groupingBy(SupplierServiceFullVO::getServiceId));

        return records.stream()
                .map(t -> AssuranceOrderServiceDomain
                        .createServiceDomain(t,
                                command,
                                serviceIdToSupplierServiceMp.get(t.getId()),
                                serviceIdToServiceSaveCommandMp.get(t.getId()).get(0)))
                .collect(Collectors.toList());
    }

    private List<AssuranceOrderUserDO> createOrderUserDOs(AssuranceOrderSaveCommand command) {
        List<AssuranceOrderUserDO> orderUserDOS;
        String flightDate = DateUtils.formatDate(command.getFlightDate(), DateUtils.PTN_YMD);
        if(ServiceTarget.CREW.getCode().equals(command.getServiceTarget())){
           String flightSegments = command.getFlightSegments();
            List<FlightInfoDTO> flightInfoDTOS = Arrays.asList(flightSegments.split(",")).stream().map(flightSegment -> {
                String[] split = flightSegment.split("-");
                FlightInfoDTO infoDTO = new FlightInfoDTO();
                infoDTO.setFlightDate(flightDate);
                infoDTO.setFlightNo(command.getFlightNo());
                infoDTO.setOrg(split[0]);
                infoDTO.setDst(split[1]);
                infoDTO.setFlightUnitPersonnelCodes(command.getUserPrimaryKeys());
                return infoDTO;
            }).collect(Collectors.toList());
            List<FlightUnitInfoVO> flightUnitInfos = assuranceFltPaxQueryService.getFlightUnitInfo(flightInfoDTOS);

            orderUserDOS = flightUnitInfos.stream().map(t -> {
                AssuranceOrderUserDO userDO = new AssuranceOrderUserDO();
                userDO.setUserId(t.getFlightUnitPersonnelCode());
                userDO.setUserName(t.getFlightUnitPersonnelName());
                userDO.setCrewWorkNo(t.getFlightUnitPersonnelCode());
                userDO.setCrewWorkPosition(t.getFlightUnitPersonnelJob());
                return userDO;
            }).collect(Collectors.toList());

        }else{
            PaxQueryDTO paxQueryDTO=new PaxQueryDTO();
            paxQueryDTO.setFlightDate(flightDate);
            paxQueryDTO.setFlightNo(command.getFlightNo());
            paxQueryDTO.setPaxIds(command.getUserPrimaryKeys());
            List<AssurancePassengerVO> assurancePassengerVOS = assuranceFltPaxQueryService.queryPax(paxQueryDTO);
            orderUserDOS = assurancePassengerVOS.stream().map(t -> {
                String categoryPaxType = t.getCategoryPaxType();
                AssuranceOrderUserDO userDO = new AssuranceOrderUserDO();
                userDO.setUserId(t.getPaxId());
                userDO.setUserName(t.getPaxName());
                userDO.setPaxCarryCabin(t.getCarryClass());
                userDO.setPaxCategoryCode(t.getCategoryCode());
                userDO.setPaxTypeCode(categoryPaxType);
                userDO.setPaxIdNo(t.getIdNo());
                userDO.setPaxTicketNo(t.getTktNo());
                userDO.setPaxOrg(t.getOrgCityAirp());
                userDO.setPaxDst(t.getDstCityAirp());
                userDO.setPaxTktTime(t.getTktDate());
                userDO.setServiceLevel(ServiceLevel.COMMON.getCode());
                if(StringUtils.isNotBlank(userDO.getPaxTypeCode())){
                    Optional<String> first = Arrays.stream(userDO.getPaxTypeCode().split(","))
                            .filter(HIGH_PAX_TYPE_CODE::equals)
                            .findFirst();
                    if(first.isPresent()){
                        userDO.setServiceLevel(ServiceLevel.HIGH.getCode());
                    }
                }
                return userDO;
            }).collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(orderUserDOS)){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"没有找到旅客或则机组数据");

        }
        return orderUserDOS;
    }
}
