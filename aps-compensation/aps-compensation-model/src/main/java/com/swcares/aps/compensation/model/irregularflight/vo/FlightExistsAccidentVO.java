package com.swcares.aps.compensation.model.irregularflight.vo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.vo.FlightExistsAccidentVO <br>
 * Description：通过航班号和日期来获取是否存在事故单展示对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="FlightExistsAccidentVO对象", description="赔偿航班信息")
public class FlightExistsAccidentVO{

    @ApiModelProperty(value = "事故单ID")
    private Long id;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "事故单状态（0草稿、1待处理、2处理中、3已结案、4作废）")
    private String accidentStatus;

    @ApiModelProperty(value = "航变类型（1延误，2取消，3备降，4返航，5补班）")
    private String fcType;

    @ApiModelProperty(value = "航段-中文格式")
    private String choiceSegmentCh;

    @ApiModelProperty(value = "创建人")
    private String createdBy;
}
