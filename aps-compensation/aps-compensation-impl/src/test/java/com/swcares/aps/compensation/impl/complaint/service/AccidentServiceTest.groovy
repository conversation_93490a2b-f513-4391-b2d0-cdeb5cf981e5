package com.swcares.aps.compensation.impl.complaint.service

import com.alibaba.fastjson.JSON
import com.swcares.CompensationImplApplication
import com.swcares.aps.compensation.impl.complaint.service.impl.ComplaintAccidentService
import com.swcares.aps.compensation.impl.irregularflight.service.impl.FlightAccidentInfoServiceImpl
import com.swcares.aps.compensation.model.complaint.dto.*
import com.swcares.baseframe.common.security.LoginUserDetails
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootContextLoader
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.security.core.Authentication
import org.springframework.security.core.context.SecurityContext
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.provider.OAuth2Authentication
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification
import spock.lang.Unroll

@SpringBootTest(classes = CompensationImplApplication.class)
@ContextConfiguration(loader = SpringBootContextLoader.class)
class AccidentServiceTest extends Specification {

    @Autowired
    ComplaintAccidentService complaintAccidentService

    @Autowired
    FlightAccidentInfoServiceImpl flightAccidentInfoService;

    // set authentication to get login detail
    def setup() {
        def authentication = Mock(LoginUserDetails)
        authentication.getJobNumber() >> "778899"
        authentication.getUsername() >> "测试mock"
        authentication.getId() >> 1793089353696133120
        authentication.getTenantId() >> 123L
        def securityContext = Mock(SecurityContext)
        def mock = Mock(OAuth2Authentication)
        def authentication_mock = Mock(Authentication)
        authentication_mock.getPrincipal() >> authentication
        mock.getUserAuthentication() >> authentication_mock
        mock.getPrincipal() >> authentication
        securityContext.getAuthentication() >> mock
        SecurityContextHolder.setContext(securityContext)
    }

    def "create accident test"() {
        given: "mock input properties"
        def complaintAccidentCreateDto = new ComplaintAccidentCreateDto(
                flag: 1,
                passengerComplaintInfoList: [new PassengerComplaintDto(idNumber: "H03875630", passengerName: "罗爱群", ticketNumber: "0882151347491", idType: "PSPT",),
                                             new PassengerComplaintDto(idNumber: "H04150489", passengerName: "蔡丽香", ticketNumber: "0882151347489", idType: "PSPT",),
                                             new PassengerComplaintDto(idNumber: "H08786708", passengerName: "胡玉英", ticketNumber: "0882151347490", idType: "PSPT",),
                                             new PassengerComplaintDto(idNumber: "H03875630", passengerName: "LAW/OIKWAN", ticketNumber: "0882440953384", idType: "PSPT",),
                                             new PassengerComplaintDto(idNumber: "H04150489", passengerName: "TSOI/LAI HEUNG", ticketNumber: "0882440953387", idType: "PSPT",),

                ],
                passengerSelectInfo: new PassengerSelectInfoDto(accidentType: "4", flightDate: "2024-05-16", flightNo: "TV9917", segment: "LXA-MIG,MIG-SZX,LXA-SZX"),
                passengerCompensationInfoDto: new PassengerCompensationInfoDto(accidentSubType: "4", reasonType: "1", complaintDep: "22,1791313906147594240,1791314102466187264", complaintChannel: "1", accidentDes: "qhyutest", contactInfo: "18628218225", fileUrl: "1807591596782235648",),
        )

        when: "create accident"
        def result = complaintAccidentService.create(complaintAccidentCreateDto)

        then: "create accident"
        noExceptionThrown()
    }

    def "detail about accident info"(){
        given:" input id "
        def id = 1798595808944648192
        when:"get accident info"
        def result = complaintAccidentService.detail(id)

        then:"get accident info"
        print(result)
        noExceptionThrown()
    }

    def "draft about accident info"(){
        given:" input id "
        def id = 1807620850401443840
        when:"get accident info"
        def result = complaintAccidentService.draft(id)

        then:"get accident info"
        print(JSON.toJSONString(result))
        noExceptionThrown()
    }

    @Unroll
    def "operate accident"(Long id, String flag) {
        expect:
        false == complaintAccidentService.operate(id, flag)
        where:
        id                  | flag
        1796098772759420928 | "0"
        1796098772759420928 | "1"
    }

    def "check accident create before"(){
        given: "mock input properties"
        def complaintAccidentCreateDto = new ComplaintAccidentCreateDto(
                id: 1813812460301582336,
                flag: "1",
                passengerComplaintInfoList: [new PassengerComplaintDto(idNumber: "132326197812251319", passengerName: "张聪然", ticketNumber: "0883709701340", idType: "NI",),
                                             new PassengerComplaintDto(idNumber: "232131198101094212", passengerName: "杜继伟", ticketNumber: "0883709701361", idType: "NI",)],
                passengerSelectInfo: new PassengerSelectInfoDto(accidentType: "3", flightDate: "2024-05-16", flightNo: "TV9917", segment: "MIG-SZX"),
                passengerCompensationInfoDto: new PassengerCompensationInfoDto(accidentSubType: "2", reasonType: "1", complaintDep: "1792823512158404608,1792823610313895936", complaintChannel: "1", accidentDes: "地面服务差劲", contactInfo: "18628218225", fileUrl: "1797829583852322816"),
        )

        when: "check accident"
        def result = complaintAccidentService.check(complaintAccidentCreateDto)
        print(result)

        then: "create accident"
        noExceptionThrown()
    }

    @Unroll
    def "page query"(){
        given:
        //def dto = new ComplaintAccidentQueryDto(flightStartDate: "2024-05-16",flightEndDate: "2024-05-16",accidentSource: "2",pageNumber: 1,pageSize: 1)
        def dto = new ComplaintAccidentQueryDto(flightNo: "3U9917",flightStartDate: "2024-05-16",flightEndDate: "2024-05-16",accidentSubType: ["3","1"],accidentSource: "1",pageNumber: 1,pageSize: 10)
        when:
        def query = complaintAccidentService.pageQuery(dto)
        then:
        print(query)
        noExceptionThrown()
    }

    def "flight accident query"(){
        given:
        def id =1813102784962510848
        when:
        def query = flightAccidentInfoService.findById(id)
        then:
        print(query)
        noExceptionThrown()
    }
}