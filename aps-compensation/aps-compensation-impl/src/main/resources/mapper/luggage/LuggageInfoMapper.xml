<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.baggage.luggage.mapper.LuggageInfoMapper">
    <select id="findLuggageList" resultType="com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageInfoVO" databaseId="mysql">
        SELECT lm.id,lm.luggage_no,lm.brand,lm.luggage_name,lm.sizes as size,lm.segment,lm.price,lm.stock,
        lm.compensate,lm.created_time,
        (SELECT CONCAT(ue.name,ue.JOB_NUMBER) as created_by
        FROM uc_employee ue WHERE ue.id = lm.created_by) created_by,
        if((SELECT COUNT(*) from luggage_consumption_details lcd where
        lcd.luggage_id = lm.id )>0,'Y','N')has_consumption_details
        FROM luggage_management lm
        where (lm.delete_mark != 'Y' or lm.delete_mark is null)
        <if test="dto.segment != null and dto.segment != '' ">
            and lm.segment like  "%"#{dto.segment}"%"
        </if>
        <if test="dto.brand != null and dto.brand != '' ">
            and lm.brand like  "%"#{dto.brand}"%"
        </if>
        <if test="dto.size != null and dto.size != '' ">
            and lm.sizes = #{dto.size}
        </if>
        <if test="dto.lowPrice != null and dto.lowPrice != ''">
            and lm.price &gt;= #{dto.lowPrice}
        </if>
        <if test="dto.highPrice != null and dto.highPrice != ''">
            and lm.price &lt;= #{dto.highPrice}
        </if>
        <if test="dto.mustHasStock != null and dto.mustHasStock == true">
            and lm.stock &gt; 0
        </if>
        ORDER BY lm.CREATED_TIME desc
        <!--ORDER BY fn_getpy(lm.BRAND,3),lm.SIZES,lm.PRICE,lm.STOCK desc  -->
    </select>
    <select id="findLuggageList" resultType="com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageInfoVO" databaseId="oracle">
        SELECT lm.id,lm.luggage_no,lm.brand,lm.luggage_name,lm.sizes as "SIZE",lm.segment,lm.price,lm.stock,
        lm.compensate,lm.created_time,
        lm.created_by created_by,
        CASE
        WHEN (SELECT COUNT(*) FROM luggage_consumption_details lcd WHERE
        lcd.luggage_id = lm.id )>0 THEN 'Y'
        ELSE 'N'
        END has_consumption_details
        FROM luggage_management lm
        where (lm.delete_mark != 'Y' or lm.delete_mark is null)
        <if test="dto.id != null and dto.id != '' ">
            and lm.id = #{dto.id}
        </if>
        <if test="dto.segment != null and dto.segment != '' ">
            and lm.segment like  concat(concat('%',#{dto.segment}),'%')
        </if>
        <if test="dto.brand != null and dto.brand != '' ">
            and lm.brand like  concat(concat('%',#{dto.brand}),'%')
        </if>
        <if test="dto.size != null and dto.size != '' ">
            and lm.sizes = #{dto.size}
        </if>
        <if test="dto.lowPrice != null and dto.lowPrice != ''">
            and lm.price &gt;= #{dto.lowPrice}
        </if>
        <if test="dto.highPrice != null and dto.highPrice != ''">
            and lm.price &lt;= #{dto.highPrice}
        </if>
        <if test="dto.mustHasStock != null and dto.mustHasStock == true">
            and lm.stock &gt; 0
        </if>
     <!--   ORDER BY lm.CREATED_TIME desc-->
      ORDER BY fn_getpy(lm.BRAND,3),lm.SIZES,lm.PRICE ASC ,lm.STOCK desc
    </select>

    <update id="updateCompensate">
        UPDATE luggage_management lm set lm.compensate = lm.compensate - (#{num})
        where id = #{id}
    </update>

    <update id="removeById">
        update luggage_management lm set lm.delete_mark='Y' where lm.id = #{id}
    </update>

    <update id="updateLuggage" databaseId="mysql">
        update luggage_management lm
        set lm.brand = #{dto.brand},lm.luggage_name = #{dto.luggageName},lm.sizes = #{dto.size},lm.price = #{dto.price}
        where lm.id = #{dto.id}
    </update>
    <update id="updateLuggage" databaseId="oracle">
        update luggage_management lm
        set lm.brand = #{dto.brand},lm.luggage_name = #{dto.luggageName},lm.sizes = #{dto.size},lm.price = #{dto.price}
        where lm.id = #{dto.id}
    </update>

    <select id="findTodayAmount" resultType="java.lang.Integer">
        SELECT count(*) from luggage_management where luggage_no like concat(concat('%',#{today}),'%')
    </select>

    <select id="findCreatedBy" resultType="java.lang.String">
        SELECT
<!--        CONCAT(ue.name,'(',ue.employee_code,')')as created_by -->
        CONCAT(ue.name,ue.JOB_NUMBER)created_by
        FROM uc_employee ue WHERE ue.id = #{id}
    </select>


    <select id="hasExistLuggage" resultType="java.lang.Integer" databaseId="mysql">
        SELECT count(*) from luggage_management lm
        where segment = #{dto.segment} and lm.luggage_name = #{dto.luggageName}
          and lm.brand = #{dto.brand} and lm.sizes = #{dto.size} 
          and price = #{dto.price}
          <if test="dto.id != null and dto.id !='' ">
              and lm.id != #{dto.id}
          </if>
    </select>
    <select id="hasExistLuggage" resultType="java.lang.Integer" databaseId="oracle">
        SELECT count(*) from luggage_management lm
        where segment = #{dto.segment} and lm.luggage_name = #{dto.luggageName}
        and lm.brand = #{dto.brand} and lm.sizes = #{dto.size}
        and price = #{dto.price}
        <if test="dto.id != null and dto.id !='' ">
            and lm.id != #{dto.id}
        </if>
    </select>

    <!--<select id="findSegment" resultType="java.lang.String">
        SELECT CONCAT(cc.city_ch_name,cc.airport_3code) as segment FROM city_code cc
        where cc.airport_3code in
        <foreach collection="codes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>-->
    <select id="findSegment" resultType="java.lang.String">
        SELECT CONCAT(cc.airport_name,cc.airport_3code) as segment FROM bd_airport_info cc
        where cc.airport_3code in
        <foreach collection="codes" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getLuggageManagementList" resultType="com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageInfoVO" databaseId="mysql">
        select
        lm.id,
        lm.luggage_no,
        lm.brand,
        lm.luggage_name,
        lm.sizes as size,
        lm.segment,
        lm.price,
        lm.stock,
        lm.compensate
        from luggage_management lm
        <where>
            <if test="brand != null and brand != ''">
                lm.brand = #{brand}
            </if>
        </where>
        order by lm.brand,lm."SIZES"
    </select>
    <select id="getLuggageManagementList" resultType="com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageInfoVO" databaseId="oracle">
        select
        lm.id,
        lm.luggage_no,
        lm.brand,
        lm.luggage_name,
        lm.sizes as "SIZE",
        lm.segment,
        lm.price,
        lm.stock,
        lm.compensate
        from luggage_management lm
        <where>
            lm.DELETE_MARK != 'Y' OR lm.DELETE_MARK IS NULL
            <if test="brand != null and brand.length > 0 ">
                and lm.brand in
                <foreach collection="brand" item="brand"  open="(" separator="," close=")">
                    #{brand}
                </foreach>
            </if>
        </where>
        order by lm.brand,lm."SIZES"
    </select>


</mapper>
