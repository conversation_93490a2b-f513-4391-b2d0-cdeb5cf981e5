package com.swcares.aps.msg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.msg.model.entity.SmsTaskImportReciverInfo;

import java.util.List;

public interface SmsTaskImportReciverService extends IService<SmsTaskImportReciverInfo> {

    public List<SmsTaskImportReciverInfo> getSuccessImportReceiverInfosByImportId(String importId);

    public List<SmsTaskImportReciverInfo> getFailedImportReceiverInfosByImportId(String importId);

    public List<SmsTaskImportReciverInfo> getImportReceiverInfosByImportId(String importId);
}
