/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：PassengerWechatController <br>
 * Package：com.swcares.reaptv.msg.controller <br> 
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.msg.model.dto.*;
import com.swcares.aps.msg.model.entity.PassengerWechatRecord;
import com.swcares.aps.msg.model.enums.SendStatusEnum;
import com.swcares.aps.msg.model.util.ModelConverter;
import com.swcares.aps.msg.model.vo.MessageStatusStaticsVO;
import com.swcares.aps.msg.model.vo.PassengerWechatRecordVO;
import com.swcares.aps.msg.model.vo.SnsSendResultVO;
import com.swcares.aps.msg.service.PassengerWechatRecordService;
import com.swcares.aps.msg.service.impl.SearchCriteriaUtils;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * ClassName：com.swcares.reaptv.msg.controller.PassengerWechatController <br>
 * Description：旅客微信发送记录接口
 * <AUTHOR> <br>
 * date 2022/10/11 9:47 <br>
 * @version v1.0.0 <br>
 */
@RestController
@RequestMapping({"/msg/wechat/passenger"})
@Api(tags = {"旅客微信信息发送记录接口"})
@ApiVersion({"Sns Api v1.0"})
public class PassengerWechatController {
    private Logger logger = LoggerFactory.getLogger(PassengerWechatController.class);
    
    @Autowired
    private PassengerWechatRecordService passengerWechatRecordService;

    @Autowired
    private SearchCriteriaUtils searchCriteriaUtils;
    
    @PostMapping("/send_subscribe_wechat_message")
    @ApiOperation("对旅客发送小程序订阅消息")
    public BaseResult<SnsSendResultVO> sendSubscribeMessage(@RequestBody PassengerWechatSubscribeMsgSendDTO dto){
        logger.info("对旅客发送小程序订阅消息: openid={}, templateId={}", dto.getOpenid(), dto.getTemplateId());
        PassengerWechatRecord record = passengerWechatRecordService.sendWechatSubscribeMessage(dto);
        SnsSendResultVO result = new SnsSendResultVO();
        result.setReason(record.getReason());
        result.setStatus(record.getStatus().getKey());
        result.setId(record.getId());
        logger.info("对旅客发送小程序订阅消息:openid={}，templatId={}, 发送结果：{}", dto.getOpenid(), dto.getTemplateId(), result.getStatus());
        return BaseResult.ok(result);
    }

    @PostMapping("/send_template_wechat_message")
    @ApiOperation("对旅客发送公众号模板消息")
    public BaseResult<SnsSendResultVO> sendTemplateMessage(@RequestBody PassengerWechatTemplateMsgSendDTO dto){
        logger.info("对旅客{}发送公众号模板消息：{}", dto.getOpenid(), dto.getTemplateId());
        PassengerWechatRecord record = passengerWechatRecordService.sendWechatTemplateMessage(dto);
        SnsSendResultVO result = new SnsSendResultVO();
        result.setReason(record.getReason());
        result.setStatus(record.getStatus().getKey());
        result.setId(record.getId());
        logger.info("对旅客{}发送公众号模板消息：{}，发送结果：{}", dto.getOpenid(), dto.getTemplateId(), result.getStatus());
        return BaseResult.ok(result);
    }

    @PostMapping("/list")
    @ApiOperation("分页获取旅客微信消息发送记录")
    public PagedResult<List<PassengerWechatRecordVO>> getPassengerWechatRecord(@RequestBody
                                                                               PassengerWechatSearchDTO searchDTO){
        transferBusinessTypeSearchCriteria(searchDTO);
        IPage<PassengerWechatRecord> datas = passengerWechatRecordService.getPassengerWechatRecordPage(searchDTO);
        List<PassengerWechatRecordVO> result = ModelConverter.convertToPassengerWechatRecordVO(datas.getRecords());

        return PagedResult.ok(result, datas.getTotal(), datas.getPages(), datas.getSize(), datas.getCurrent());
    }

    @RequestMapping("/statics")
    @ApiOperation("获取满足指定的条件的短信发送状态统计")
    public BaseResult<MessageStatusStaticsVO> getMessageStatusStatics(@RequestBody
            PassengerWechatSearchDTO searchDto){
        transferBusinessTypeSearchCriteria(searchDto);
        List<MessageStatusStaticsDTO> staticsDTO = passengerWechatRecordService.getMessagesStatusStatics(searchDto);

        MessageStatusStaticsVO messageStatusStaticsVO = new MessageStatusStaticsVO();
        if (staticsDTO != null && !staticsDTO.isEmpty()){
            for(MessageStatusStaticsDTO dto : staticsDTO){
                messageStatusStaticsVO.addTotalCount(dto.getRecordCount());

                if (SendStatusEnum.SUCCESS.equals(dto.getStatus())){
                    messageStatusStaticsVO.addSuccessCount(dto.getRecordCount());
                }
            }
        }

        return BaseResult.ok(messageStatusStaticsVO);
    }

    @GetMapping("/detail")
    @ApiOperation("获取指定记录的详情")
    public BaseResult<PassengerWechatRecordVO> getDetails(@RequestParam(name="id", required = true)Long id){
        PassengerWechatRecord record = passengerWechatRecordService.getById(id);
        PassengerWechatRecordVO result = ModelConverter.convertToPassengerWechatRecordVO(record);

        return BaseResult.ok(result);
    }

    @PostMapping("/delete")
    @ApiOperation("逻辑删除指定的记录")
    public BaseResult<Boolean> delete(@RequestBody DeleteRecordDTO dto){
        Boolean result = passengerWechatRecordService.batchDelete(dto.getIds());
        return BaseResult.ok(result);
    }

    @PostMapping("/download")
    @ApiOperation("根据查询条件下载符合条件的旅客微信信息发送记录")
    public void download(@RequestBody PassengerWechatSearchDTO searchDTO, HttpServletRequest request, HttpServletResponse response){
        transferBusinessTypeSearchCriteria(searchDTO);
        passengerWechatRecordService.download(searchDTO, request, response);
    }

    @PostMapping("/downloadById")
    @ApiOperation("根据给定的id下载旅客微信信息发送记录")
    public void downloadById(@RequestBody List<Long> ids, HttpServletRequest request, HttpServletResponse response){
        passengerWechatRecordService.downloadById(ids, request, response);
    }

    private void transferBusinessTypeSearchCriteria(PassengerWechatSearchDTO dto){
        if (dto == null || dto.getBusinessTypes() == null){
            return;
        }

        List<String> transferedBusinessType = searchCriteriaUtils.getPassengerWechatBusinessTypeDictValue(dto.getBusinessTypes());
        dto.setBusinessTypes(transferedBusinessType);
    }
}
