package com.swcares.aps.compensation.model.compensation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：BaggageCompensationAmountRuleVO
 * @Description：异常行李-补偿规则
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/6/19 15:19
 * @version： v1.0
 */
@Data
@ApiModel(value="BaggageCompensationAmountRuleVO对象", description="异常行李-补偿规则")
public class BaggageCompensationAmountRuleVO {
    @ApiModelProperty(value = "异常行李类型（21破损,22少收,24内件缺少,25丢失）")
    private String type;
    @ApiModelProperty(value = "补偿标准")
    private String standardAmount;
}
