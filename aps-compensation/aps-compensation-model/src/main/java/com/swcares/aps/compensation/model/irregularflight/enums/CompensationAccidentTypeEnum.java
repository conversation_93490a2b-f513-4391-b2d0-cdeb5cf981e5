package com.swcares.aps.compensation.model.irregularflight.enums;

import com.swcares.baseframe.common.enums.JsonEnumValue;

/**
 * ClassName：com.swcares.aps.compensation.impl.irregularflight.enums <br>
 * Description：事故单类型 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 11月18日 11:22 <br>
 * @version v1.0 <br>
 */
public enum CompensationAccidentTypeEnum {
//    补偿类型-1不正常航班，2异常行李，3超售
    IRREGULAR_FLIGHT("1", "不正常航班"),
    ABNORMAL_BAGGAGE("2", "异常行李"),
    OVERBOOKING("3", "超售"),
    COMPLAINT("4", "旅客投诉");


    private CompensationAccidentTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    @JsonEnumValue
    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static CompensationAccidentTypeEnum build(String key) {
        return build(key, true);
    }

    public static CompensationAccidentTypeEnum build(String key, boolean throwEx) {
        CompensationAccidentTypeEnum typeEnum = null;
        for (CompensationAccidentTypeEnum element : CompensationAccidentTypeEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + CompensationAccidentTypeEnum.class.getSimpleName());
        }
        return typeEnum;
    }
}
