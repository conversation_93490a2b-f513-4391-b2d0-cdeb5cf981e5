<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.ground.supplier.mapper.SupplierServiceMapper">
    <update id="logicRemoveById">
        update GROUND_SUPPLIER_SERVICE set deleted = 1 where id =#{id}
    </update>

    <select id="getSupplierServiceFulls" resultType="com.swcares.aps.ground.models.supplier.vo.SupplierServiceFullVO">

        select  DISTINCT gss.*
        from GROUND_SUPPLIER_SERVICE gss
        JOIN GROUND_SUPPLIER_SERVICE_APT gsa on gss.id = gsa.SUPPLIER_SERVICE_ID
        where    1=1 and gss.DELETED = 0

        <if test="dto.supplierServiceIds != null and dto.supplierServiceIds.size() > 0">
            and gss.id in
            <foreach collection="dto.supplierServiceIds" item="supplierServiceId"  open="(" separator="," close=")">
                #{supplierServiceId}
            </foreach>
        </if>

        <if test="dto.serviceId != null and dto.serviceId != ''">
            and gss.SERVICE_ID=#{dto.serviceId}
        </if>

        <if test="dto.serviceTarget != null and dto.serviceTarget != ''">
            and gss.SERVICE_TARGET=#{dto.serviceTarget}
        </if>

        <if test="dto.serviceType != null and dto.serviceType != ''">
            and gss.SERVICE_TYPE=#{dto.serviceType}
        </if>

        <if test="dto.serviceLevel != null and dto.serviceLevel != ''">
            and gss.SERVICE_LEVEL=#{dto.serviceLevel}
        </if>

        <if test="dto.supplierServiceState != null and dto.supplierServiceState != ''">
            and  gss.SUPPLIER_SERVICE_STATE = #{dto.supplierServiceState}
        </if>

        <if test="dto.serviceState != null and dto.serviceState != ''">
            and  gss.SERVICE_STATE = #{dto.serviceState}
        </if>

        <if test="dto.belongAirlineCode != null and dto.belongAirlineCode != ''">
            and gss.BELONG_AIRLINE_CODE=#{dto.belongAirlineCode}
        </if>

        <if test="dto.serviceIds != null and dto.serviceIds.size()>0">
            and gss.SERVICE_ID in
            <foreach collection="dto.serviceIds" item="sId" open="(" close=")" separator=",">#{sId}</foreach>
        </if>

        <if test="dto.airportCode != null and dto.airportCode != ''">
            and gsa.AIRPORT_CODE = #{dto.airportCode}
        </if>
        <if test="dto.beginDate!=null">and gss.begin_Date &lt;= #{dto.beginDate}</if>
        <if test="dto.endDate!=null">and gss.end_Date &gt;=  #{dto.endDate}</if>
        <if test="dto.canUse!=null">and gss.CAN_USE  =  #{dto.canUse}</if>
        <if test="dto.useAble!=null">
            and gsa.AIRPORT_CODE = #{dto.airportCode}
            and gss.SERVICE_STATE = 1
            and gss.SUPPLIER_SERVICE_STATE = 1
            and gss.SUPPLIER_STATE = 1
            <if test="dto.beginDate!=null">and gss.begin_Date &lt;= #{dto.endDate}</if>
            <if test="dto.endDate!=null">and gss.end_Date &gt;=  #{dto.beginDate}</if>
            <if test="dto.flightDate!=null">and gss.end_Date &gt;=  #{dto.flightDate} and gss.begin_Date &lt;= #{dto.flightDate}</if>
        </if>

    </select>

    <select id="refreshCanUseList" resultType="com.swcares.aps.ground.models.supplier.entity.SupplierServiceDO">

        select *

        from GROUND_SUPPLIER_SERVICE
        where
        DELETED = 0
        and
        (SUPPLIER_CAN_USE = 1 and SERVICE_STATE = 1
        and TRUNC(SYSDATE)  BETWEEN TRUNC(BEGIN_DATE) AND TRUNC(END_DATE) and CAN_USE =0)
        OR
        (
        (SUPPLIER_CAN_USE = 0  or SERVICE_STATE = 0 or TRUNC(SYSDATE) &lt;  TRUNC(BEGIN_DATE) OR SYSDATE > TRUNC(END_DATE)) and  CAN_USE =1
        )
    </select>
</mapper>