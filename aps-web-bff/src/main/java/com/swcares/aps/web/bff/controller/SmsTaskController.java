package com.swcares.aps.web.bff.controller;

import com.alibaba.fastjson.JSONObject;
import com.swcares.aps.msg.api.SmsTaskApi;
import com.swcares.aps.msg.model.dto.*;
import com.swcares.aps.msg.model.vo.*;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping({"/msg/sms/task"})
@Api(tags = {"短信任务接口"})
@ApiVersion({"SmsTask Api v1.0"})
@Slf4j
public class SmsTaskController {

    @Autowired
    private SmsTaskApi smsTaskApi;

    @PostMapping("/list")
    @ApiOperation("短信任务列表")
    public PagedResult<List<SmsTaskVO>> getSmsTaskList(@RequestBody SmsTaskSearchDTO dto){
        return smsTaskApi.getSmsTaskList(dto);
    }

    @GetMapping("/getBusinessType")
    @ApiOperation("下拉框获取业务类型")
    public BaseResult<List<BusinessTypeVO>> getBusinessType(@RequestParam String[] value){
        return smsTaskApi.getBusinessType(value);
    }


    @GetMapping("/detail/task")
    @ApiOperation("短信任务查看-任务信息")
    public BaseResult<SmsTaskDetailVO> getSmsTaskDetail(@ApiParam("任务Id") @RequestParam("taskId") String taskId){
        return smsTaskApi.getSmsTaskDetail(taskId);
    }

    @PostMapping("/detail/send")
    @ApiOperation("短信任务查看-发送详情")
    public BaseResult<SmsTaskSendDetailStaticsVO> getSmsTaskSendDetail(@RequestBody SmsTaskSendDetailDTO dto){
        return smsTaskApi.getSmsTaskSendDetail(dto);
    }



    @ApiOperation("半自动短信审核航班信息及任务信息")
    @GetMapping("/audit/taskInfo")
    public BaseResult<SmsTaskAuditInfoVO> getAdultInfo(@ApiParam("任务Id")@RequestParam("taskId") String taskId){
        return smsTaskApi.getAdultInfo(taskId);
    }


    @ApiOperation("半自动短信审核旅客列表")
    @PostMapping("/audit/getPassgener")
    public BaseResult<SmsTaskAuditVO> getAdultPassenger(@RequestBody AuditPassgenerSearchDTO dto){
        return smsTaskApi.getAdultPassenger(dto);
    }


    @ApiOperation("半自动短信审核操作")
    @PostMapping("/audit")
    public BaseResult audit(@RequestBody AuditSmsDTO dto){
        log.info(JSONObject.toJSONString(dto));
        return smsTaskApi.audit(dto);
    }


    @ApiOperation("半自动短信批量审核操作")
    @PostMapping("/audit/batch")
    public BaseResult batchAudit(@RequestBody AuditBatchSmsDTO dto){
        return smsTaskApi.batchAudit(dto);
    }

    @ApiOperation("短信任务停用")
    @GetMapping("/disable")
    public BaseResult disable(@ApiParam("id")@RequestParam String id){
        return smsTaskApi.disable(id);
    }


    @ApiOperation("手动创建短信任务")
    @PostMapping("/createTaskByHand")
    public BaseResult createTaskByHand(@RequestBody SmsTaskInfoDTO dto){
        return smsTaskApi.createTaskByHand(dto);
    }

    @ApiOperation("选择接收人-pnr、手机号、票号导入")
    @PostMapping(value = "/import/reciver", headers = "content-type=multipart/form-data")
    public BaseResult<SmsTaskImportVO> importReciver(@ApiParam("4-pnr 3-票号 2-手机号") @RequestParam String type,@RequestPart(name = "file") MultipartFile file){
        return smsTaskApi.importReciver(type, file);
    }


    @ApiOperation("pnr、票号、手机号导入结果查看")
    @PostMapping("/import/detail")
    public PagedResult<List<SmsTaskReciverImportVO>> importDetail(@RequestBody SmsTaskReciverImportDTO dto){
        return smsTaskApi.importDetail(dto);
    }




    @PostMapping("/previewSmsContentForCreateTask")
    @ApiOperation("创建短信任务的时候预览短信内容")
    public BaseResult<String> previewSmsContentForCreateTask(@RequestBody SmsTaskInfoDTO dto){
        return smsTaskApi.previewSmsContentForCreateTask(dto);
    }

    @PostMapping("/previewSmsContentForAudit")
    @ApiOperation("审核短信任务的时候预览短信内容")
    public BaseResult<String> previewSmsContentForAuditTask(@RequestBody AuditSmsDTO dto){
        return smsTaskApi.previewSmsContentForAuditTask(dto);
    }

}
