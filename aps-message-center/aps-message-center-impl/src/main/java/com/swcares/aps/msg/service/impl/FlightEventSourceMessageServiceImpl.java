/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：FlightEventMessageServiceImpl <br>
 * Package：com.swcares.reaptv.msg.service.impl <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.msg.mapper.FlightEventSourceMessageMapper;
import com.swcares.aps.msg.model.entity.FlightEventSourceMessage;
import com.swcares.aps.msg.service.FlightEventSourceMessageService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/*
 * ClassName：com.swcares.reaptv.msg.service.impl.FlightEventMessageServiceImpl <br>
 * Description：TODO：(这里用一句话描述这个类的作用) <br>
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/1 12:08 <br>
 * @version:
 */
@Service
public class FlightEventSourceMessageServiceImpl extends ServiceImpl<FlightEventSourceMessageMapper, FlightEventSourceMessage> implements
        FlightEventSourceMessageService {

    /**   
     * Title：getMissingSourceMessage <br>
     * Description：获取漏处理的FlightEvent的队列。<br>
     *     判断条件： 处理标记为false，创建时间再5分钟之前
     * @Return java.util.List<com.swcares.reaptv.msg.model.entity.FlightEventSourceMessage>
     * @throws 
     * @author：王翼 <br>
     * date：2022-12-25 14:42 <br>
     */
    @Override public List<FlightEventSourceMessage> getMissingSourceMessage() {
        LambdaQueryWrapper<FlightEventSourceMessage> searchCriteria = new LambdaQueryWrapper<>();
        searchCriteria.eq(FlightEventSourceMessage::isProcessed, false );
        searchCriteria.lt(FlightEventSourceMessage::getCreatedTime, LocalDateTime.now().minusMinutes(5));
        searchCriteria.orderByAsc(FlightEventSourceMessage::getCreatedTime);
        return getBaseMapper().selectList(searchCriteria);
        
    }

    @Override public void markAsProcessed(FlightEventSourceMessage flightEventSourceMessage) {
        FlightEventSourceMessage updateCriteria = new FlightEventSourceMessage();
        updateCriteria.setId(flightEventSourceMessage.getId());
        updateCriteria.setProcessed(true);
        updateCriteria.setResult(flightEventSourceMessage.getResult());
        
        getBaseMapper().updateById(updateCriteria);
    }
}
