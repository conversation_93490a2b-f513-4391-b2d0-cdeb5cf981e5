/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：PassengerWechatController <br>
 * Package：com.swcares.reaptv.msg.controller <br> 
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.web.bff.controller;

import com.swcares.aps.msg.api.PassengerWechatRecordApi;
import com.swcares.aps.msg.model.dto.DeleteRecordDTO;
import com.swcares.aps.msg.model.dto.PassengerWechatSearchDTO;
import com.swcares.aps.msg.model.vo.MessageStatusStaticsVO;
import com.swcares.aps.msg.model.vo.PassengerWechatRecordVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import feign.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.IOUtils;
import org.apache.ibatis.logging.Log;
import org.apache.ibatis.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Collection;
import java.util.List;

/**
 * ClassName：com.swcares.reaptv.msg.controller.PassengerWechatController <br>
 * Description：给旅客发送的微信记录的前端接口
 * <AUTHOR> <br>
 * date 2022/10/11 9:47 <br>
 * @version v1.0.0 <br>
 */
@RestController
@RequestMapping({"/msg/wechat/passenger"})
@Api(tags = {"旅客微信信息发送记录接口"})
@ApiVersion({"Sns Api v1.0"})
public class PassengerWechatController {
    @Autowired PassengerWechatRecordApi passengerWechatRecordApi;

    protected Log log = LogFactory.getLog(getClass());

    @PostMapping("/list")
    @ApiOperation("分页获取旅客微信消息发送记录")
    public PagedResult<List<PassengerWechatRecordVO>> getPassengerWechatRecord(@RequestBody
            PassengerWechatSearchDTO searchDTO){
        return passengerWechatRecordApi.getPassengerWechatRecord(searchDTO);
    }

    @PostMapping("/statics")
    @ApiOperation("获取满足指定的条件的短信发送状态统计")
    public BaseResult<MessageStatusStaticsVO> getMessageStatusStatics(@RequestBody
            PassengerWechatSearchDTO searchDto){
        return passengerWechatRecordApi.getMessageStatusStatics(searchDto);
    }

    @GetMapping("/detail")
    @ApiOperation("获取指定记录的详情")
    public BaseResult<PassengerWechatRecordVO> getDetails(@RequestParam(name="id", required = true)Long id){
        return passengerWechatRecordApi.getDetails(id);
    }

    @PostMapping("/delete")
    @ApiOperation("逻辑删除指定的记录")
    public BaseResult<Boolean> delete(@RequestBody DeleteRecordDTO dto){
        return passengerWechatRecordApi.delete(dto);
    }

    @PostMapping("/download")
    @ApiOperation("根据查询条件下载符合条件的旅客微信信息发送记录")
    public void download(@RequestBody PassengerWechatSearchDTO searchDTO, HttpServletRequest request, HttpServletResponse response){
        Response feignResponse = passengerWechatRecordApi.download(searchDTO);
        downloadByFeign(response, feignResponse);
    }

    @PostMapping("/downloadById")
    @ApiOperation("根据给定的id下载旅客微信信息发送记录")
    public void downloadById(@RequestBody List<Long> ids, HttpServletRequest request, HttpServletResponse response){
        Response feignResponse = passengerWechatRecordApi.downloadById(ids);
        downloadByFeign(response, feignResponse);
    }

    private void downloadByFeign(HttpServletResponse response, Response feignResponse) {
        response.setContentType("application/x-download;charset=UTF8");
        Collection<String> contentDispositionHeader =
                feignResponse.headers().get("Content-Disposition");
        response.setHeader("Content-Disposition", contentDispositionHeader.isEmpty() ?
                "" :
                contentDispositionHeader.iterator().next());

        Response.Body feignBody = feignResponse.body();
        try (OutputStream outputStream = response.getOutputStream();
                InputStream inputStream = feignBody.asInputStream()) {
            IOUtils.copy(inputStream, outputStream);
            outputStream.flush();
        } catch (IOException e) {
            log.error("导出旅客短信发送记录出现异常", e);
        }
    }
}
