package com.swcares.aps.compensation.model.compensation.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName：CompensationLuggageInfoDO
 * @Description：赔偿实物信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 10:09
 * @version： v1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "compensation_material_info")
public class CompensationMaterialInfoDO extends BaseEntity {

    private Long tenantId;

    /**
     * 旅客ID
     */
    @ApiModelProperty(value = "旅客ID")
    private String paxId;

    /**
     * 实物id
     */
    @ApiModelProperty(value = "箱包id")
    private Long materialId;

    /**
     * 实物编号
     */
    @ApiModelProperty(value = "箱包编号")
    private String materialNo;

    /**
     * 实物类型 1：实物
     */
    @ApiModelProperty(value = "实物类型")
    private String materialType;

    /**
     * 实物品牌
     */
    @ApiModelProperty(value = "实物品牌")
    private String materialBrand;

    /**
     * 实物名称
     */
    @ApiModelProperty(value = "实物名称")
    private String materialName;

    /**
     * 实物尺寸
     */
    @ApiModelProperty(value = "实物尺寸")
    private String materialSize;

    /**
     * 实物单价
     */
    @ApiModelProperty(value = "实物单价")
    private String materialUnivalent;


    /***
     * 赔偿单ID
     */
    @ApiModelProperty(value = "赔偿单ID")
    private Long orderId;

    /**
     * 赔偿单编号
     */
    @ApiModelProperty(value = "赔偿单编号")
    private String orderNo;

    /***
     * 赔偿数量
     */
    @ApiModelProperty(value = "赔偿数量")
    private Long amount;

}
