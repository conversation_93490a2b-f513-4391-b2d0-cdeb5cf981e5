<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.usercenter.impl.uc.mapper.EmployeeWechatMapper">

    <!-- 查找指定员工在指定的client的openid -->
    <select id="getEmployeeWechatByClientAndEmployee" resultType="com.swcares.aps.usercenter.impl.uc.entity.EmployeeWechat">
        SELECT id, employee_id, client_id, openid, created_by,created_time, updated_by, updated_time FROM uc_employee_wechat
        <where>
            client_id = #{clientId}
            and
            employee_id = #{employeeId}
        </where>
    </select>

    <!-- 查找指定用户在指定的client的openid -->
    <select id="getEmployeeWechatByClientAndUser" resultType="com.swcares.aps.usercenter.impl.uc.entity.EmployeeWechat">
        SELECT uew.id, uew.employee_id, uew.client_id, uew.openid, uew.created_by,uew.created_time, uew.updated_by, uew.updated_time 
        FROM uc_employee_wechat uew, uc_user uu
        <where>
            uu.id = #{userId}
            and 
            uu.employee_id = uew.employee_id
            and
            uew.client_id = #{clientId}
        </where>
    </select>
    
    <select id="getEmployeeByClientAndOpenid" resultType="com.swcares.aps.usercenter.impl.uc.entity.EmployeeWechat">
        SELECT id, employee_id, client_id, openid, created_by,created_time, updated_by, updated_time FROM uc_employee_wechat
        <where>
            client_id = #{clientId}
            and
            openid = #{openid}
        </where>
    </select>
</mapper>
