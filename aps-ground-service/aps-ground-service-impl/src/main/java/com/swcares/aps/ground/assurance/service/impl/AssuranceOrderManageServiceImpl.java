package com.swcares.aps.ground.assurance.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.Cached;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.aps.component.com.platform.PlatformTool;
import com.swcares.aps.component.workflow.dto.AuditorInfoDTO;
import com.swcares.aps.component.workflow.enums.AuditStatusEnum;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderInfoMapper;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderServiceChoiceSupplierMapper;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderServiceMapper;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderServiceVerifyMapper;
import com.swcares.aps.ground.assurance.repository.impl.AssuranceOrderDomainFactory;
import com.swcares.aps.ground.assurance.service.AssuranceOrderDomainService;
import com.swcares.aps.ground.assurance.service.AssuranceOrderManageService;
import com.swcares.aps.ground.assurance.service.AssuranceWorkflowDomainService;
import com.swcares.aps.ground.common.TenantService;
import com.swcares.aps.ground.constants.GroundServicePubConstants;
import com.swcares.aps.ground.enums.AssuranceOrderState;
import com.swcares.aps.ground.models.assurance.domain.AssuranceOrderDomain;
import com.swcares.aps.ground.models.assurance.domain.AssuranceOrderServiceDomain;
import com.swcares.aps.ground.models.assurance.domain.AssuranceOrderUserDomain;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderChangeStatusCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderListRequest;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderReviewerSaveCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderSaveCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderServiceListRequest;
import com.swcares.aps.ground.models.assurance.dto.AssuranceVerifyQueryRequest;
import com.swcares.aps.ground.models.assurance.dto.AssuranceWorkflowAuditCommand;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderServiceChoiceSupplierDO;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderServiceDO;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderInfoDO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderListVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderServiceDetailsVo;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderServiceListVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderServiceVerifyUserDetailVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderServiceVerifyUserVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceWorkflowAuditResultVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceWorkflowAuditorOrderVO;
import com.swcares.aps.ground.sms.service.SendSmsService;
import com.swcares.aps.workflow.dto.CurrentTaskActivityVO;
import com.swcares.aps.workflow.remote.api.util.WorkflowUtils;
import com.swcares.baseframe.common.core.tenant.TenantContextHolder;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.lang.DateUtils;
import com.swcares.baseframe.utils.lang.PinyinUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.Collator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName：AssuranceManageServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/21 16:24
 * @version： v1.0
 */
@Service
@Slf4j
public class AssuranceOrderManageServiceImpl implements AssuranceOrderManageService {

    @Autowired
    private AssuranceOrderDomainService assuranceOrderDomainService;

    @Autowired
    private AssuranceWorkflowDomainService assuranceWorkflowDomainService;

    @Autowired
    private SendSmsService sendSmsService;

    @Autowired
    private AssuranceOrderDomainFactory assuranceOrderDomainFactory;

    @Autowired
    private AssuranceOrderInfoMapper assuranceOrderInfoMapper;

    @Autowired
    private AssuranceOrderServiceMapper assuranceOrderServiceMapper;

    @Autowired
    private AssuranceOrderServiceChoiceSupplierMapper assuranceOrderServiceChoiceSupplierMapper;

    @Autowired
    private AssuranceOrderServiceVerifyMapper assuranceOrderServiceVerifyMapper;

    @Autowired
    private TenantService tenantService;

    @Autowired
    private PlatformTool platformTool;

    @Override
    public IPage<AssuranceOrderListVO> pageQueryOrderList(AssuranceOrderListRequest request) {
        IPage<AssuranceOrderListVO> result = assuranceOrderInfoMapper
                .pageQueryOrderList(request, request.createPage());
        if(CollectionUtils.isNotEmpty(result.getRecords())){
            result.getRecords().forEach(AssuranceOrderListVO::initOrgDst);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveOrUpdateAssuranceOrder(AssuranceOrderSaveCommand command) {
        AssuranceOrderDomain orderDomain = assuranceOrderDomainFactory.createByOrderSaveCommand(command);

        if(StringUtils.isBlank(orderDomain.getDomainId())){
            assuranceOrderDomainService.create(orderDomain);
        }else{
            assuranceOrderDomainService.update(orderDomain);
        }
        return orderDomain.getDomainId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object changeAssuranceOrderStatus(AssuranceOrderChangeStatusCommand command) {
        AssuranceOrderState targetOrderState = AssuranceOrderState.fromCode(command.getTargetStatus());
        AssuranceOrderDomain orderDomain = assuranceOrderDomainFactory.createById(command.getOrderId());
        if(orderDomain==null){ throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法保障单ID");}

        assuranceOrderDomainService.checkAssuranceOrderChangeState(orderDomain.getOrderInfoDO(),targetOrderState);

        String oriOrderState=orderDomain.getDomainState();
        AssuranceWorkflowAuditResultVO resultVO=null;
        //第一次提交
        if(oriOrderState.equals(AssuranceOrderState.DRAFT.getCode())
                && command.getTargetStatus().equals(AssuranceOrderState.AUDIT_ING.getCode())){
            resultVO = assuranceWorkflowDomainService.start(orderDomain, null);
        }
        //重新提交
        if(oriOrderState.equals(AssuranceOrderState.AUDIT_BACK.getCode())
                && command.getTargetStatus().equals(AssuranceOrderState.AUDIT_ING.getCode())){

            resultVO = assuranceWorkflowDomainService.submit(orderDomain);
        }

        assuranceOrderDomainService.changeAssuranceOrderStatus(command);

        if(resultVO!=null){
            return processWorkflowResult(command.getOrderId(), resultVO);
        }
        return command.getOrderId();
    }

    protected AssuranceWorkflowAuditResultVO processWorkflowResult(String orderId, AssuranceWorkflowAuditResultVO resultVO) {
        CurrentTaskActivityVO process = resultVO.getCurrentTaskActivityVO();
        boolean endToState = activityEndToState(orderId, process);
        //审核结束
        if(endToState){
            AssuranceOrderDomain orderDomain = assuranceOrderDomainFactory.createById(orderId);
            List<AuditorInfoDTO> auditorList = assuranceWorkflowDomainService.findAuditorList(Collections.singletonList(orderDomain.getOrderInfoDO().getSubmitId()));
            sendSmsService.sendAssuranceInnerSMS(auditorList,orderDomain, resultVO.getTaskId());
            resultVO.setOrderAuditorList(null);
        }else {
            //审核还没有结束
            List<AuditorInfoDTO> orderAuditorList = resultVO.getOrderAuditorList();
            if(orderAuditorList!=null && CollectionUtils.size(orderAuditorList)==1){
                AssuranceOrderDomain orderDomain = assuranceOrderDomainFactory.createById(orderId);
                sendSmsService.sendAssuranceInnerSMS(orderAuditorList,orderDomain, resultVO.getTaskId());
                resultVO.setOrderAuditorList(null);
            }
        }
        resultVO.setCurrentTaskActivityVO(null);
        return resultVO;
    }

    protected boolean activityEndToState(String orderId, CurrentTaskActivityVO currentTaskActivityVO) {
        if(!currentTaskActivityVO.getCurrentTaskActivityDTOS().get(0).getIsEndActivity()){ return false; }
        String preOptionCode = currentTaskActivityVO.getPreOptionCode();
        AssuranceOrderChangeStatusCommand command=new AssuranceOrderChangeStatusCommand();
        command.setOrderId(orderId);
        if(StringUtils.equals(AuditStatusEnum.DISAGREE.getKey(),preOptionCode)){
            command.setTargetStatus(AssuranceOrderState.AUDIT_REJECT.getCode());
        }else{
            command.setTargetStatus(AssuranceOrderState.SERVICE_ING.getCode());
        }
        assuranceOrderDomainService.changeAssuranceOrderStatus(command);
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AssuranceWorkflowAuditResultVO processAssuranceOrderWorkflow(AssuranceWorkflowAuditCommand command) {
        String currentUserId = String.valueOf(UserContext.getUserId());
        List<AssuranceWorkflowAuditorOrderVO> currentUserAuthOrders = assuranceWorkflowDomainService.getOrderAuthUsers(Collections.singletonList(command.getOrderId()),currentUserId);
        if(CollectionUtils.isEmpty(currentUserAuthOrders)){
            log.error("报销单workflowProcess,{},权限不足", JSONObject.toJSONString(command));
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"权限不足或数据已被审核");
        }
        AssuranceOrderDomain orderDomain = assuranceOrderDomainFactory.createById(command.getOrderId());
        if(orderDomain==null){ throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法保障单ID");}

        AssuranceWorkflowAuditResultVO resultVO = assuranceWorkflowDomainService.process(orderDomain, command);
        CurrentTaskActivityVO process =resultVO.getCurrentTaskActivityVO();

        if(AuditStatusEnum.REJECT.getKey().equals(command.getAuditStatus()) &&
                WorkflowUtils.isSubmitterTask(process.getCurrentTaskActivityDTOS().get(0).getNodeKey())){
            AssuranceOrderChangeStatusCommand statusCommand=new AssuranceOrderChangeStatusCommand();
            statusCommand.setOrderId(command.getOrderId());
            statusCommand.setTargetStatus(AssuranceOrderState.AUDIT_BACK.getCode());
            assuranceOrderDomainService.changeAssuranceOrderStatus(statusCommand);
        }

        return processWorkflowResult(command.getOrderId(), resultVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAuditReviewer(AssuranceOrderReviewerSaveCommand command) {
        AssuranceOrderDomain orderDomain = assuranceOrderDomainFactory.createById(command.getOrderId());
        if(orderDomain==null){ throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法保障单ID");}
        List<AuditorInfoDTO> auditorInfoDTOS = assuranceWorkflowDomainService.saveReviewer(orderDomain, command);
        String taskId=command.getTaskId();
        if(StringUtils.isBlank(taskId)){
            List<AssuranceWorkflowAuditorOrderVO> orderAuthUsers = assuranceWorkflowDomainService.getOrderAuthUsers(Collections.singletonList(command.getOrderId()), null);
            if(CollectionUtils.isNotEmpty(orderAuthUsers)){
                taskId=orderAuthUsers.get(0).getTaskId();
            }
        }
        sendSmsService.sendAssuranceInnerSMS(auditorInfoDTOS,orderDomain,taskId);

    }

    @Override
    public IPage<AssuranceOrderServiceListVO> pageQueryOrderServiceList(AssuranceOrderServiceListRequest request) {
        return assuranceOrderServiceMapper.h5PageQueryOrderList(request,request.createPage());
    }

    @Override
    public AssuranceOrderServiceDetailsVo getOrderServiceDetail(String orderServiceId) {
        AssuranceOrderServiceDO orderServiceDO = assuranceOrderServiceMapper.selectById(orderServiceId);
        if(orderServiceDO==null || orderServiceDO.getDeleted()== GroundServicePubConstants.DELETED_YES){
            return null;
        }

        AssuranceOrderInfoDO orderInfoDO = assuranceOrderInfoMapper.selectById(orderServiceDO.getOrderId());
        //服务项下可选供应商
        LambdaQueryWrapper<AssuranceOrderServiceChoiceSupplierDO> queryWrapper = Wrappers.lambdaQuery(AssuranceOrderServiceChoiceSupplierDO.class);
        queryWrapper.eq(AssuranceOrderServiceChoiceSupplierDO::getOrderServiceId,orderServiceDO.getId());
        queryWrapper.eq(AssuranceOrderServiceChoiceSupplierDO::getDeleted, GroundServicePubConstants.DELETED_NO);
        queryWrapper.eq(AssuranceOrderServiceChoiceSupplierDO::getOrderId, orderServiceDO.getOrderId());
        List<AssuranceOrderServiceChoiceSupplierDO> choiceSupplierDOS = assuranceOrderServiceChoiceSupplierMapper.selectList(queryWrapper);
        //服务项下对应的核销用户
        List<AssuranceOrderUserDomain> orderUserDomain = assuranceOrderDomainFactory.createOrderUserDomainByOrderServiceId(orderServiceId);

        AssuranceOrderDomain orderDomain = AssuranceOrderDomain.builder()
                .orderInfoDO(orderInfoDO)
                .orderServiceItemDomains(Collections.singletonList(AssuranceOrderServiceDomain.builder()
                        .choiceSupplierDOS(choiceSupplierDOS)
                        .orderServiceDO(orderServiceDO).build()))
                .orderUserDomains(orderUserDomain)
                .build();
        List<AssuranceOrderServiceVerifyUserVO> verifyUserVOS = orderDomain.createServiceVerifyDivideVOS().get(0).getVerifyUserVOS();

        Collator collator = Collator.getInstance(Locale.CHINA);
        List<AssuranceOrderServiceVerifyUserVO> sortedList = verifyUserVOS.stream()
                .sorted(Comparator.comparingInt(AssuranceOrderServiceVerifyUserVO::getVerificationStateSort)
                        .thenComparing((vo1, vo2) -> collator.compare(
                                PinyinUtils.getFirstSpell(vo1.getUserName()),
                                PinyinUtils.getFirstSpell(vo2.getUserName()))))
                .collect(Collectors.toList());

        return AssuranceOrderServiceDetailsVo.builder()
                .serviceId(orderServiceDO.getServiceId())
                .serviceName(orderServiceDO.getServiceName())
                .serviceType(orderServiceDO.getServiceType())
                .orderServiceId(orderServiceDO.getId())
                .orderServiceBelongAirlineCode(orderServiceDO.getBelongAirlineCode())
                .orderBelongAirlineCode(orderInfoDO.getBelongAirlineCode())
                .orderInfoDO(orderInfoDO)
                .orderServiceChoiceDOS(choiceSupplierDOS)
                .orderServiceDO(orderServiceDO)
                .verifyUserVOS(sortedList).build();
    }

    @Override
    public void overTimeAssuranceOrder() {
        Date overTime = DateUtils.addHours(new Date(), -AssuranceOrderDomain.OVER_TIME_HOUR);
        String tenantType=platformTool.getTenantType();
        List<AssuranceOrderInfoDO> assuranceOrderInfoDOS = assuranceOrderInfoMapper.selectOverTimeAssuranceOrder(overTime,tenantType);
        if(CollectionUtils.isEmpty(assuranceOrderInfoDOS)){
            log.info("保障单逾期定时任务获取到单子,没有过期的单子");
            return;
        }
        log.info("保障单逾期定时任务获取到单子：{}",assuranceOrderInfoDOS.stream().map(AssuranceOrderInfoDO::getId).collect(Collectors.toList()));
        for(AssuranceOrderInfoDO infoDO:assuranceOrderInfoDOS){
            try{
                TenantContextHolder.setTenant(infoDO.getTenantId());
                assuranceOrderDomainService.overTime(infoDO.getId());
                log.info("保障单逾期定时任务逾期保障单执行成功,Tenant:{},orderId:{}",infoDO.getTenantId(),infoDO.getId());
            }catch (Exception e){
                log.error("保障单逾期定时任务逾期保障单执行失败,Tenant:{},orderId:{}", infoDO.getTenantId(), infoDO.getId(), e);
            }

        }
    }

    @Override
    public void finishAssuranceOrder() {
        String tenantType=platformTool.getTenantType();
        List<AssuranceOrderInfoDO> assuranceOrderInfoDOS = assuranceOrderInfoMapper.selectNeedFinishAssuranceOrder(tenantType);
        if(CollectionUtils.isEmpty(assuranceOrderInfoDOS)){
            log.info("保障单完成定时任务获取到单子,没有需要完成的单子");
            return;
        }
        log.info("保障单完成定时任务获取到单子：{}",assuranceOrderInfoDOS.stream().map(AssuranceOrderInfoDO::getId).collect(Collectors.toList()));
        for(AssuranceOrderInfoDO infoDO:assuranceOrderInfoDOS){
            try{
                TenantContextHolder.setTenant(infoDO.getTenantId());
                assuranceOrderDomainService.finish(infoDO.getId());
                log.info("保障单完成定时任务逾期保障单执行成功,Tenant:{},orderId:{}",infoDO.getTenantId(),infoDO.getId());
            }catch (Exception e){
                log.error("保障单完成定时任务逾期保障单执行失败,Tenant:{},orderId:{}", infoDO.getTenantId(), infoDO.getId(), e);
            }

        }
    }

    @Override
    public IPage<AssuranceOrderServiceVerifyUserDetailVO> getOrderServiceVerifyDetailList(AssuranceVerifyQueryRequest request) {
        return assuranceOrderServiceVerifyMapper.getOrderServiceVerifyDetailList(request, request.createPage());
    }

    @Override
    public List<String> getDistinctServiceName() {
        return getDistinctServiceName(String.valueOf(TenantHolder.getTenant()));
    }

    @Cached(name = "supplier.serviceNames", expire = 300, key = "#tenant")
    protected List<String> getDistinctServiceName(String tenant){
        return assuranceOrderServiceMapper.selectDistinctServiceName();
    }

}
