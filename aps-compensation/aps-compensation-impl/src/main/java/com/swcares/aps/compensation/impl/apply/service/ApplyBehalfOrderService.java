package com.swcares.aps.compensation.impl.apply.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.apply.dto.ApplyBehalfOrderDTO;
import com.swcares.aps.compensation.model.apply.entity.ApplyOrderDO;

/**
 * ClassName：com.swcares.compensation.service.ApplyOrderService <br>
 * Description：航延补偿申领单信息表 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-11-25 <br>
 * @version v1.0 <br>
 */
public interface ApplyBehalfOrderService extends IService<ApplyOrderDO> {

    /**
     * @title saveBehalfApplyOrder
     * @description 保存带人领取的申领单
     * <AUTHOR>
     * @date 2021/11/29 11:00
     * @param dto
     * @return boolean
     */
    boolean saveBehalfApplyOrder(ApplyBehalfOrderDTO dto);

    /**
     * @title saveAssistApply
     * @description 保存协助领取申领单
     * <AUTHOR>
     * @date 2022/2/16 17:25
     * @param dto
     * @return boolean
     */
    boolean saveAssistApply(ApplyBehalfOrderDTO dto);

}
