package com.swcares.aps.compensation.impl.complaint.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.aps.compensation.model.complaint.dto.CompensationPassengerAmountDto;
import com.swcares.aps.compensation.model.complaint.dto.PassengerComplaintDto;
import com.swcares.aps.compensation.model.complaint.dto.PassengerSelectInfoDto;
import com.swcares.aps.compensation.model.complaint.entity.ComplaintAccidentInfoEntity;
import com.swcares.aps.compensation.model.complaint.vo.*;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;

/**
 * ClassName：com.swcares.aps.compensation.impl.complaint.mapper.ComplaintCompensationInfoMapper <br>
 * Description：用于查询旅客投诉事故单业务中的赔偿信息查询 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2024/5/27 11:17 <br>
 * @version v1.0 <br>
 */
public interface ComplaintCompensationInfoMapper extends BaseMapper<CompensationOrderInfoDO> {

    /**
     * Title: 通过旅客信息查询赔偿次数的查询
     * <AUTHOR>
     * @date  2024/5/27 11:25
     * @since  2024/5/27
     * @param passengerSelectInfoDto PassengerSelectInfoDto
     * @return List<CompensationCountByPassengerInfoVo>
     */
    List<CompensationCountByPassengerInfoVo> selectCompensationCountByPassengerInfo(@Param("dto") PassengerSelectInfoDto passengerSelectInfoDto);


    /**
     * Title: 查询旅客的补偿次数列表
     * <AUTHOR>
     * @date  2024/5/28 09:18
     * @since  2024/5/28
     * @param passengerSelectInfoDto PassengerSelectInfoDto
     * @return List<CompensationCountVo>
     */
    List<CompensationCountVo> selectCompensationList(@Param("dto") PassengerSelectInfoDto passengerSelectInfoDto);

    /**
     * Title: 查询旅客的关联补偿单信息
     * <AUTHOR>
     * @date  2024/5/28 09:18
     * @since  2024/5/28
     * @param id String
     * @return List<AccidentConcatCompensationInfoVo>
     */
    List<AccidentConcatCompensationInfoVo> selectRelationshipOfCompensationList(@Param("id") Long id);

    List<ComplaintCompensationCheckInfoVo> checkInfo(@Param("dto")List<PassengerComplaintDto> passengerComplaintDtoList,@Param("id") Long orderId);

    List<FrequencyVo> frequency(@Param("dto") List<CompensationPassengerAmountDto> compensationPassengerAmountDto);

    List<ComplaintAccidentInfoEntity> getCheckAccidentInfo(@Param("dto") List<PassengerComplaintDto> passengerComplaintInfoList, @Param("id") Long id);
}
