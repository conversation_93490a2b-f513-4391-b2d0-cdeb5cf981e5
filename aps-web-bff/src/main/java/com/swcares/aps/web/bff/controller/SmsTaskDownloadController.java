package com.swcares.aps.web.bff.controller;

import com.swcares.aps.msg.model.dto.SmsTaskSendDetailDTO;
import com.swcares.aps.msg.model.dto.SmsTaskSendDetailExportDTO;
import com.swcares.aps.web.bff.service.SmsTaskService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;

/**
 * @ClassName SmsTaskDownloadController
 * @Description TODO
 * <AUTHOR>
 * @Date 2023-03-30
 * @Version since
 **/

@Controller
@RequestMapping({"/msg/sms/task"})
@Api(tags = {"短信任务下载接口"})
@ApiVersion({"SmsTask Api v1.0"})
@Slf4j
public class SmsTaskDownloadController {
    @Autowired
    private SmsTaskService smsTaskService;
    @PostMapping("/detail/send/export")
    @ApiOperation("短信任务查看-按照查询条件导出发送详情")
    public void getSmsTaskSendDetailExport(@RequestBody SmsTaskSendDetailDTO dto, HttpServletResponse response){
        smsTaskService.getSmsTaskSendDetailExport(dto,response);
    }

    @PostMapping("/detail/send/exportSelected")
    @ApiOperation("短信任务查看-导出选中的发送详情")
    public void exportSelectedSmsTaskSendDetails(@RequestBody SmsTaskSendDetailExportDTO dto, HttpServletResponse response) {
        smsTaskService.exportSelectedSmsTaskSendDetails(dto, response);
    }

    @ApiOperation("pnr、票号、手机号下载导入失败的数据")
    @PostMapping("/download/fail")
    public void downloadFail(@ApiParam("导入后返回的id")@RequestParam String id, HttpServletResponse response){
        smsTaskService.downloadFail(id,response);
    }

    @ApiOperation("pnr、票号、手机号导入模板下载")
    @PostMapping("/download/template")
    public void downloadTemplate(@ApiParam("0-pnr 1-票号 2-手机号")@RequestParam String type,HttpServletResponse response){
        smsTaskService.getTemplate(type,response);
    }
}
