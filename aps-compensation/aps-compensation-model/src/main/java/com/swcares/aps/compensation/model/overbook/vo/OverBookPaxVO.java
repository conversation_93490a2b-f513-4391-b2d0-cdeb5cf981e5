package com.swcares.aps.compensation.model.overbook.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * @ClassName：OverBookPaxVO
 * @Description：超售旅客信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/28 10:11
 * @version： v1.0
 */
@Data
public class OverBookPaxVO {

    @ApiModelProperty(value = "原航班ID")
    @NotNull
    private String flightId;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "航段")
    private String segment;

    @ApiModelProperty(value = "航段-中文格式")
    private String segmentCh;

    @ApiModelProperty(value = "计划起飞")
    private String std;

    @ApiModelProperty(value = "计划到达")
    private String sta;

    @ApiModelProperty(value = "旅客id")
    private String paxId;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "证件类型")
    private String idType;

    @ApiModelProperty(value = "证件号")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "手机号码")
    private String telephone;

    @ApiModelProperty(value = "起始航站三字码")
    private String orgCityAirp;

    @ApiModelProperty(value = "到达航站三字码")
    private String dstCityAirp;

    @ApiModelProperty(value = "经济舱全价")
    private String fullEconomyFare;

    @ApiModelProperty(value = "是否取消0否1是")
    private String isCancel;

    @ApiModelProperty(value = "pnr")
    private String pnr;

}
