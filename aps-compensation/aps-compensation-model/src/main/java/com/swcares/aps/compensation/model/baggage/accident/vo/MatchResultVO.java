package com.swcares.aps.compensation.model.baggage.accident.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: MatchResultVO
 * @projectName aps
 * @description: 匹配多/少收的事故单结果
 * @date 2022/3/8 14:30
 */
@Data
@ApiModel(value="匹配多/少收的事故单结果", description="匹配多/少收的事故单结果")
public class MatchResultVO {

    @ApiModelProperty(value = "事故单id")
    private String accidentId;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "行李号")
    private String baggageNo;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "服务航站")
    private String serveSegment;

    @ApiModelProperty(value = "事故单状态(0草稿,1待处理,2处理中,3已结案,4作废)")
    private String accidentStatus;

    @ApiModelProperty(value = "异常行李类型(21破损,22少收,23多收,24内件缺少,25丢失)")
    private String type;

    @ApiModelProperty(value = "是否匹配")
    private String whetherMatch;
}
