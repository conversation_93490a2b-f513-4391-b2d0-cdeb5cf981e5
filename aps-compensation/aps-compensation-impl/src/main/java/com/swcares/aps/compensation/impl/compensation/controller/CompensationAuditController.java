package com.swcares.aps.compensation.impl.compensation.controller;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.compensation.impl.compensation.service.CompensationAuditService;
import com.swcares.aps.compensation.impl.compensation.service.CompensationOrderCommandsService;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationException;
import com.swcares.aps.compensation.model.compensation.dto.CompensationCashEditCommandDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialEditCommandDTO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditOperationVO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditReviewerVO;
import com.swcares.aps.compensation.model.irregularflight.dto.AuditProcessorDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationAuditInfoDTO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationAuditRecordVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * @ClassName：CompensationAuditController
 * @Description：补偿单审核统一接口
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/5/9 19:20
 * @version： v1.0
 */
@RestController
@RequestMapping("/compensation/audit")
@Api(tags = "补偿单审核统一接口")
@ApiVersion(value = "补偿单审核统一接口 v1.0.1")
@Slf4j
public class CompensationAuditController extends BaseController {

    @Autowired
    private CompensationAuditService compensationAuditService;

    @Autowired
    private CompensationOrderCommandsService compensationOrderCommandsService;

    /**
     * @title reSubmitCashCompensation
     * @description 重新提交现金补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单ID
     */
    @PostMapping("cash/reSubmit")
    @ApiOperation(value = "重新提交现金补偿单接口")
    public BaseResult<CompensationAuditOperationVO> reSubmitCashCompensation(@RequestBody @Validated CompensationCashEditCommandDTO request){
        return ok(compensationOrderCommandsService.reSubmitCashCompensation(request));
    }

    /**
     * @title reSubmitMaterialCompensation
     * @description 重新提交实物补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单ID
     */
    @PostMapping("material/reSubmit")
    @ApiOperation(value = "重新提交实物补偿单接口")
    public BaseResult<CompensationAuditOperationVO> reSubmitMaterialCompensation(@RequestBody @Validated CompensationMaterialEditCommandDTO request){
        return ok(compensationOrderCommandsService.reSubmitMaterialCompensation(request));
    }

    @GetMapping("/findAuditRecord")
    @ApiOperation(value = "查看审核记录")
    public BaseResult<List<CompensationAuditRecordVO>> findAuditRecord(@ApiParam(value = "赔偿单id",required=true)Long orderId,@ApiParam(value = "赔偿单号",required=true)String orderNo) {
        try {
            List<CompensationAuditRecordVO> list = compensationAuditService.findAuditRecord(orderId,orderNo);
            Collections.reverse(list);
            return ok(list);
        } catch (Exception e) {
            log.error("查看审核记录，请求参数为orderId【{}】、orderNo【{}】，异常信息为【{}】", orderId, orderNo, e);
            throw new BusinessException(CompensationException.AUDIT_ERROR);
        }
    }

    @GetMapping("/findAuditReviewer")
    @ApiOperation(value = "条件查询可选审核人")
    public BaseResult<CompensationAuditReviewerVO> findReviewer(@ApiParam(value = "部门id") Long orgId, @ApiParam(value = "节点id",required=true) String taskId, @ApiParam(value = "审核人信息：姓名/工号") String userInfo, @ApiParam(value = "赔偿单id")Long orderId) {
        return ok(compensationAuditService.findReviewer(orgId, userInfo,taskId,orderId));
    }

    @PostMapping("/saveAuditReviewer")
    @ApiOperation(value = "审核人确认")
    public BaseResult<Object> saveAuditReviewer(@RequestBody @Validated CompensationAuditInfoDTO dto) {
        compensationAuditService.saveReviewer(dto);
        return ok();
    }

    @PostMapping("/operation")
    @ApiOperation(value = "审核操作-同意、不同意、驳回")
    public BaseResult<CompensationAuditOperationVO> auditOperation(@RequestBody AuditProcessorDTO dto){
        try {
            CompensationAuditOperationVO compensationAuditOperationVO = compensationAuditService.auditOperation(dto);
            return ok(compensationAuditOperationVO);
        } catch (Exception e) {
            log.error("审核操作-同意、不同意、驳回异常，请求参数为【{}】，异常信息为【{}】", JSONUtil.toJsonStr(dto), e);
            if(e instanceof BusinessException){
                throw (BusinessException)e;
            }else{
                throw new BusinessException(CompensationException.AUDIT_ERROR);
            }
        }
    }

}
