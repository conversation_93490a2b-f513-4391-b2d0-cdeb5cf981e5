/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：FlightEventController <br>
 * Package：com.swcares.reaptv.msg.controller <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.msg.model.dto.FlightEventPassengerSearchDTO;
import com.swcares.aps.msg.model.dto.FlightEventSearchDTO;
import com.swcares.aps.msg.model.dto.GFPassengerSearchDTO;
import com.swcares.aps.msg.model.entity.FlightEvent;
import com.swcares.aps.msg.model.entity.FlightEventPassenger;
import com.swcares.aps.msg.model.entity.GFPassengerInfo;
import com.swcares.aps.msg.model.vo.Segment;
import com.swcares.aps.msg.service.FlightEventPassengerService;
import com.swcares.aps.msg.service.FlightEventService;
import com.swcares.aps.msg.service.GFFlightinfoService;
import com.swcares.aps.msg.service.GFPassengerInfoService;
import com.swcares.aps.usercenter.remote.api.uc.ReaptvAirportInfoApi;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.components.bd.entity.AirportInfo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.reaptv.msg.controller.FlightEventController <br>
 * Description：TODO：(这里用一句话描述这个类的作用) <br>
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/13 9:01 <br>
 * @version:
 */
@RestController
@Slf4j
@RequestMapping("/msg/flightevent")
public class FlightEventController {
    @Autowired
    private ReaptvAirportInfoApi airportInfoApi;
    
    @Autowired
    private FlightEventService flightEventService;
    
    @Autowired
    private GFFlightinfoService gfFlightinfoService;
    
    @Autowired
    private GFPassengerInfoService gfPassengerInfoService;
    
    @Autowired
    private FlightEventPassengerService flightEventPassengerService;
    
    @PostMapping("/list")
    @ApiOperation("查询航变事件列表")
    public PagedResult<List<FlightEvent>> searchFlightEvent(@RequestBody FlightEventSearchDTO searchCriteria){
        IPage<FlightEvent> flightEvents = flightEventService.getFlightEvents(searchCriteria);
        return PagedResult.ok(flightEvents.getRecords(), flightEvents.getTotal(), flightEvents.getPages(),
                flightEvents.getSize(), flightEvents.getCurrent());
    }

    @GetMapping("/segments")
    @ApiOperation("搜索广分数据中指定航班下的航段信息")
    public BaseResult<List<Segment>> getSegmentsOfFlightEventList(
            @RequestParam("flightNumber") String flightNumber, 
            @RequestParam("flightDate") String flightDate){
        List<Segment> segments = gfFlightinfoService.getSegments(flightNumber, flightDate);
        List<AirportInfo> airportInfos = airportInfoApi.getAll().getData();
        Map<String, AirportInfo> airportInfoMap = AirportInfoUtils.convertToMap(airportInfos);

        if (segments != null && !segments.isEmpty()){
            for (Segment segment : segments){
                AirportInfoUtils.populateAirportInfo(segment, airportInfoMap);
            }
        }
        return BaseResult.ok(segments);
    }

    @PostMapping("/passengers")
    @ApiOperation("查询广分数据中的旅客信息")
    public PagedResult<List<GFPassengerInfo>> searchFlightEventPassenger(@RequestBody
                                                                         GFPassengerSearchDTO criteria){
        IPage<GFPassengerInfo> gfPassengerInfos = gfPassengerInfoService.getGFPassengerInfoPage(criteria);
        return PagedResult.ok(gfPassengerInfos.getRecords(), gfPassengerInfos.getTotal(),
                gfPassengerInfos.getPages(), gfPassengerInfos.getSize(), gfPassengerInfos.getCurrent());
    }

    @GetMapping("/detail")
    @ApiOperation("获取指定航变事件的详情")
    public BaseResult<FlightEvent> getFlightEventDetail(Long id){
        FlightEvent result = flightEventService.getById(id);
        return BaseResult.ok(result);
    }

    @PostMapping("/passengersOfEvent")
    @ApiOperation("获取指定航变事件关联的旅客")
    public PagedResult<List<FlightEventPassenger>> getPassengersOfFlightEvent(@RequestBody
                                                                              FlightEventPassengerSearchDTO searchDTO){
        IPage<FlightEventPassenger> results = flightEventPassengerService.getPassengerInfoPage(searchDTO);
        return PagedResult.ok(results.getRecords(), 
                results.getTotal(), results.getPages(),
                results.getSize(), results.getCurrent());
    }
}
