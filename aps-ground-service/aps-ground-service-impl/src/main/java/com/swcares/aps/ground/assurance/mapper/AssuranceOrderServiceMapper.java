package com.swcares.aps.ground.assurance.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderServiceListRequest;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderServiceDO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderServiceListVO;
import com.swcares.baseframe.common.mybatis.base.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @ClassName：AssuranceOrderServiceItemMapper
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/21 15:58
 * @version： v1.0
 */
@Mapper
public interface AssuranceOrderServiceMapper extends BaseMapper<AssuranceOrderServiceDO> {

    IPage<AssuranceOrderServiceListVO> h5PageQueryOrderList(@Param("dto") AssuranceOrderServiceListRequest request, Page<Object> page);

    List<String> selectDistinctServiceName();

    void refreshServiceVerifyAmount(@Param("ids") Set<String> ids);
}
