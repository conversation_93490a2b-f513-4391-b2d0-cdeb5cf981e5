package com.swcares.aps.compensation.model.dataconfig.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * ClassName：com.swcares.aps.compensation.model.replace.dto.ReplaceRuleDTO <br>
 * Description： 代人领取规则支付等待期RULE配置 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-11 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="ReplacePayPeriodRuleDTO对象", description="代人领取规则支付等待期RULE配置")
public class ReplacePayPeriodRuleDTO implements BaseDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "代人领取支付等待期,文本框默认值为 5分钟，最大允许设置120分钟",example = "20",required = true)
    @NotNull
    @Min(value = 1)
    @Max(value = 120)
    private Integer paymentWaitingPeriod;

    @ApiModelProperty(value = "创建人")
    private String createdBy;
}
