package com.swcares.aps.usercenter.remote.api.uc;

import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.common.uc.dto.OrganizationTreeDTO;
import com.swcares.common.uc.vo.OrganizationTreeVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * @ClassName：ReaptvOrgApi
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2023/3/31 11:43
 * @version： v1.0
 */
@FeignClient(name = "aps-user-center", path = "/api/aps-user-center", contextId = "ReaptvOrgApi")
public interface ReaptvOrgApi {

    @GetMapping("/common/org/get_org_tree")
    BaseResult<List<OrganizationTreeVO>> getOrgTree(@SpringQueryMap OrganizationTreeDTO dto);
}
