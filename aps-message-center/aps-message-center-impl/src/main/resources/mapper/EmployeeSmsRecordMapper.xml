<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.msg.mapper.EmployeeSmsRecordMapper">
    <sql id="employeeSmsRecordDetail">
        SELECT
            ID,
            FLIGHT_NUMBER,
            FLIGHT_DATE,
            CONTENT,
            BUSINESS_TYPE,
            RECEIVER_INFO,
            RECEIVER_ACCOUNT,
            PHONE_NUMBER,
            ORG_AIRPORT,
            DEST_AIRPORT,
            SENDER_INFO,
            SENDER_ACCOUNT,
            SENT_TIME,
            STATUS,
            TRIGGER_TYPE,
            REASON,
            DELETED,
            CREATED_TIME,
            CREATED_BY,
            UPDATED_TIME,
            UPDATED_BY
        FROM
            MSG_EMPLOYEE_SMS_RECORD MESR
    </sql>
    <sql id="searchCriteriaOracle">
        WHERE
            MESR.DELETED = 0
        <if test="dto.receiverAccount != null and dto.receiverAccount !='' ">
            AND ( MESR.RECEIVER_ACCOUNT = #{dto.receiverAccount} OR MESR.RECEIVER_INFO LIKE '%'||#{dto.receiverAccount}||'%')
        </if>
        <if test="dto.phoneNumber != null and dto.phoneNumber != '' ">
            AND MESR.PHONE_NUMBER = #{dto.phoneNumber}
        </if>
        <if test="dto.status != null">
            AND MESR.STATUS = #{dto.status}
        </if>
        <if test="dto.businessTypes != null and dto.businessTypes.size() >0">
            AND MESR.BUSINESS_TYPE IN
            <foreach collection="dto.businessTypes" index="index" item="businessType" separator="," open="(" close=")">
                #{businessType}
            </foreach>
        </if>
        <if test="dto.flightNumber != null and dto.flightNumber != '' ">
            AND MESR.FLIGHT_NUMBER = #{dto.flightNumber}
        </if>
        <if test="dto.flightDateStart != null and dto.flightDateStart != '' ">
            AND MESR.FLIGHT_DATE <![CDATA[ >= ]]> to_date(#{dto.flightDateStart}, 'yyyy-MM-dd')
        </if>
        <if test="dto.flightDateEnd != null and dto.flightDateEnd != '' ">
            AND MESR.FLIGHT_DATE <![CDATA[ < ]]> (to_date(#{dto.flightDateEnd}, 'yyyy-MM-dd')+1)
        </if>
        <if test="dto.sentDateStart != null and dto.sentDateStart != '' ">
            AND MESR.SENT_TIME <![CDATA[ >= ]]> to_date(#{dto.sentDateStart}, 'yyyy-MM-dd')
        </if>
        <if test="dto.sentDateEnd != null and dto.sentDateEnd != '' ">
            AND MESR.SENT_TIME <![CDATA[ < ]]> (to_date(#{dto.sentDateEnd}, 'yyyy-MM-dd')+1)
        </if>
        <if test = "dto.senderAccount != null and dto.senderAccount != ''">
            AND (MESR.SENDER_ACCOUNT = #{dto.senderAccount} OR MESR.SENDER_INFO LIKE '%'||#{dto.senderAccount}||'%')
        </if>
        <if test = "dto.triggerType != null">
            AND MESR.TRIGGER_TYPE = #{dto.triggerType}
        </if>
    </sql>
    <select id="getEmployeeSmsReccordPage" resultType="com.swcares.aps.msg.model.entity.EmployeeSmsRecord" databaseId="oracle">
        <include refid="employeeSmsRecordDetail"></include>
        <include refid="searchCriteriaOracle"></include>
        ORDER BY SENT_TIME DESC
    </select>

    <select id="getByIds" resultType="com.swcares.aps.msg.model.entity.EmployeeSmsRecord">
        <include refid="employeeSmsRecordDetail"></include>
        WHERE
            MESR.ID IN
            <foreach collection="idList" index="index" item="id" separator="," open="(" close=")">
                    #{id}
            </foreach>
    </select>

    <select id="messageStatusStatics" resultType="com.swcares.aps.msg.model.dto.MessageStatusStaticsDTO">
        select status, count(id) recordCount
        from MSG_EMPLOYEE_SMS_RECORD MESR
        <include refid="searchCriteriaOracle"></include>
        group by status
    </select>
</mapper>
