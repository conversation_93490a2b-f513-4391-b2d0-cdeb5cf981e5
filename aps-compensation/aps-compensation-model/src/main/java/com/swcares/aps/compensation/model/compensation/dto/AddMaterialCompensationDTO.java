package com.swcares.aps.compensation.model.compensation.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * @ClassName：AddMaterialCompensationDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 16:07
 * @version： v1.0
 */
@Data
public class AddMaterialCompensationDTO {

    /***
     * 实物ID
     */
    @ApiModelProperty(value = "实物ID")
    private String materialId;

    /***
     * 实物数量
     */
    @ApiModelProperty(value = "实物类型 11：自动查询箱包，12：手动添加箱包")
    @NotNull
    @Pattern(regexp="^[11|12]$")
    private String materialType;

    /***
     * 实物数量
     */
    @ApiModelProperty(value = "实物数量")
    private Long amount;

    @ApiModelProperty(value = "实物品牌")
    private String materialBrand;

    @ApiModelProperty(value = "实物名称")
    private String materialName;

    @ApiModelProperty(value = "实物尺寸")
    private String materialSize;

    @ApiModelProperty(value = "实物单价")
    private String materialUnivalent;



}
