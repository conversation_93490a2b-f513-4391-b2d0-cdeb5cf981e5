package com.swcares.aps.workflow.flowable.service;


import org.flowable.bpmn.model.UserTask;

/**
 * ClassName：FlowableFlowElementService <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/2/16 <br>
 * @version v1.0 <br>
 */
public interface FlowableFlowElementService {

    /**
     *
     * @param taskId
     * @return
     */
    UserTask nextFlowNode(String taskId);
}
