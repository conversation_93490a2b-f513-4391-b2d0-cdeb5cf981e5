package com.swcares.aps.staff.bff.service;

import com.swcares.aps.basic.data.businessimpl.model.dto.CompensationPaxSearchDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.CheckPaxBagTagDTO;
import com.swcares.aps.staff.bff.vo.FlightAndPassengerVO;
import com.swcares.baseframe.common.base.BaseResult;


/**
 * ClassName：BaggageAccidentService <br>
 * Description： <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/7 <br>
 * @version v1.0 <br>
 */

public interface BaggageAccidentService {

    /**
     * @title getFlightAndPassenger
     * @description 获取航班+旅客信息
     * <AUTHOR>
     * @date 2022/3/25 9:20
     * @param dto
     * @return
     */
	FlightAndPassengerVO getFlightAndPassenger(CompensationPaxSearchDTO dto);

	/**
	 * @title checkIsNotOtherPaxBagTag
	 * @description 判断是否不是其他率行李，如果是就抛出异常
	 * <AUTHOR>
	 * @date 2022/3/25 9:20
	 * @param dto
	 * @return
	 */
	BaseResult<Object> checkIsNotOtherPaxBagTag(CheckPaxBagTagDTO dto);
}
