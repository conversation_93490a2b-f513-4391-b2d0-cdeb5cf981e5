/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：GFPassengerInfoService <br>
 * Package：com.swcares.reaptv.msg.service <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.msg.model.dto.GFPassengerSearchDTO;
import com.swcares.aps.msg.model.entity.GFPassengerInfo;

/**
 * InterfaceName：com.swcares.reaptv.msg.service.GFPassengerInfoService <br>
 * Description：TODO：(这里用一句话描述这个接口的作用) <br>
 * <AUTHOR> <br>
 * date 2022/12/6 9:44 <br>
 * @version v1.0.0 <br>
 */
public interface GFPassengerInfoService extends IService<GFPassengerInfo> {
    public void mergePassengerInfo(GFPassengerInfo passengerInfo);

    IPage<GFPassengerInfo> getGFPassengerInfoPage(GFPassengerSearchDTO criteria);
}
