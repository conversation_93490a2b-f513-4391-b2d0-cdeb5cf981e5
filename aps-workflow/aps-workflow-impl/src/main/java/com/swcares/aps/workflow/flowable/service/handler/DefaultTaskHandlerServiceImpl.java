package com.swcares.aps.workflow.flowable.service.handler;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.cpe.coordinate.model.enums.DataCategoryEnum;
import com.swcares.aps.cpe.coordinate.model.receiver.dto.CoordinateUploadRequestDTO;
import com.swcares.aps.cpe.coordinate.model.receiver.dto.CoordinateUploadResponseDTO;
import com.swcares.aps.cpe.coordinate.model.receiver.dto.WorkflowDataCoordinateDTO;
import com.swcares.aps.cpe.coordinate.util.CoordinateApiUtil;
import com.swcares.aps.workflow.common.config.CoordinateConfig;
import com.swcares.aps.workflow.common.constants.ProcessParamsConstants;
import com.swcares.aps.workflow.common.util.ProcessParamsUtil;
import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.dto.CustomerDTO;
import com.swcares.aps.workflow.dto.SyncWorkflowInfoDTO;
import com.swcares.aps.workflow.flowable.entity.SyncWorkflowTaskDO;
import com.swcares.aps.workflow.flowable.mapper.SyncWorkflowTaskMapper;
import com.swcares.aps.workflow.flowable.service.FlowableWorkflowService;
import com.swcares.aps.workflow.flowable.service.SyncWorkflowLogService;
import com.swcares.aps.workflow.remote.api.WorkflowApi;
import com.swcares.baseframe.utils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @ClassName：DefaultTaskHandlerServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2025/4/21 10:23
 * @version： v1.0
 */
@Service
@Slf4j
public class DefaultTaskHandlerServiceImpl implements SyncWorkflowTaskHandlerService {

    @Autowired
    private SyncWorkflowTaskMapper syncWorkflowTaskMapper;

    @Autowired
    private FlowableWorkflowService flowableWorkflowService;

    @Autowired
    private SyncWorkflowLogService syncWorkflowLogService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private WorkflowApi workflowApi;

    @Autowired
    private CoordinateConfig coordinateConfig;
    @Override
    public boolean support(String taskType) {
        return StringUtils.isBlank(taskType);
    }

    @Override
    public void syncSchedule(SyncWorkflowTaskDO taskDO) {
        BaseQueryParamDTO queryParamDTO = BaseQueryParamDTO.builder().processInstanceId(taskDO.getOwnProcessId()).build();
        String processVariable = (String)flowableWorkflowService.getProcessVariable(queryParamDTO, ProcessParamsConstants.SYNC_WORKFLOW_INFO);
        String customerInfo = (String)flowableWorkflowService.getProcessVariable(queryParamDTO, ProcessParamsConstants.CUSTOMER_INFO);

        log.info("获取当前实例的关联流程信息:{},当前租户信息:{}",processVariable,customerInfo);

        SyncWorkflowInfoDTO syncWorkflowInfoDTO = ProcessParamsUtil.parseSyncWorkflowInfo(processVariable);
        if(syncWorkflowInfoDTO==null
                || StringUtils.isEmpty(syncWorkflowInfoDTO.getRequestProcessInstanceId())
                || StringUtils.isEmpty(syncWorkflowInfoDTO.getRequestCustomer())
                || StringUtils.isEmpty(syncWorkflowInfoDTO.getRequestCustomerCategory())){
            taskDO.setExecStatus(SyncWorkflowTaskDO.STATUS_WAITE);
            taskDO.setUpdatedTime(new Date());
            taskDO.setExecResult("同步流程信息为空,先暂停任务执行");
            syncWorkflowTaskMapper.updateById(taskDO);
            return;
        }
        if(StringUtils.equalsIgnoreCase("1",syncWorkflowInfoDTO.getRequestEndFlag())){
            taskDO.setExecStatus(SyncWorkflowTaskDO.STATUS_SUCCESS);
            taskDO.setUpdatedTime(new Date());
            taskDO.setExecResult("同步流程信息已经到结束节点,不通知本次操作结果");
            syncWorkflowTaskMapper.updateById(taskDO);
            return;
        }

        CustomerDTO customerDTO = ProcessParamsUtil.parseCustomerInfo(customerInfo);
        SyncWorkflowInfoDTO request=new SyncWorkflowInfoDTO();
        if(customerDTO==null
                || StringUtils.isBlank(customerDTO.getCustomer())
                || StringUtils.isBlank(customerDTO.getCustomerCategory())){
            taskDO.setExecStatus(SyncWorkflowTaskDO.STATUS_ERROR);
            taskDO.setUpdatedTime(new Date());
            taskDO.setExecResult("同步流程信息出错,当前实例没有租户信息,先暂停任务执行");
            syncWorkflowTaskMapper.updateById(taskDO);
            return;
        }

        try{
            request.setRequestBusinessName(customerDTO.getBusinessName());
            request.setRequestCustomer(customerDTO.getCustomer());
            request.setRequestCustomerCategory(customerDTO.getCustomerCategory());
            request.setRequestProcessInstanceId(taskDO.getOwnProcessId());
            request.setRequestLastOptionCode(taskDO.getLastOptionCode());
            request.setRequestLastComment(taskDO.getLastComment());
            request.setRequestTaskId(taskDO.getOwnTaskId());
            request.setRequestEndFlag(taskDO.getEndFlag());


            request.setProcessInstanceId(syncWorkflowInfoDTO.getRequestProcessInstanceId());
            request.setTaskId(syncWorkflowInfoDTO.getRequestTaskId());


            String workflowData = JSONUtil.toJsonStr(request);

            WorkflowDataCoordinateDTO coordinateDTO = new WorkflowDataCoordinateDTO();
            //发送方的租户的代码
            coordinateDTO.setSenderCustomer(customerDTO.getCustomer());
            //发送方的租户的类型
            coordinateDTO.setSenderCustomerCategory(customerDTO.getCustomerCategory());
            //接收方的租户的代码
            coordinateDTO.setReceiverCustomer(syncWorkflowInfoDTO.getRequestCustomer());
            //接收方租户的类型
            coordinateDTO.setReceiverCustomerCategory(syncWorkflowInfoDTO.getRequestCustomerCategory());
            //实际的业务数据，发送方的应用端和接收的应用端要事先约定好格式
            coordinateDTO.setData(workflowData);

            //每次发送需要使用不同的requestID， requestID在每一个应用端中应该保证唯一性
            String requestID = taskDO.getOwnProcessId()
                    +taskDO.getOwnTaskId()+
                    DateUtils.formatDate(taskDO.getUpdatedTime(),DateUtils.DEF_PTN_YMD_HMS);

            CoordinateUploadRequestDTO requestDTO = CoordinateApiUtil.getCoordinateUploadRequestDTO(requestID,
                    //本次请求的数据分类，目前支持的数据分类都在DataCategoryEnum中定义
                    DataCategoryEnum.WORKFLOW_DATA.getCode(),
                    coordinateConfig.getAppClientId(),coordinateConfig.getAppSecretKeyVersion(),
                    coordinateConfig.getAppSecretKey(), JSONUtil.toJsonStr(coordinateDTO)
            );

            CoordinateUploadResponseDTO response = CoordinateApiUtil
                    .uploadCoordinateData(coordinateConfig.getCoordinateCenterUrl(), requestDTO);

            taskDO.setSyncProcessId(syncWorkflowInfoDTO.getProcessInstanceId());
            taskDO.setSyncTaskId(syncWorkflowInfoDTO.getTaskId());
            taskDO.setExecStatus(SyncWorkflowTaskDO.STATUS_SUCCESS);
            taskDO.setExecResult(CoordinateApiUtil.getDecryptedData(response, coordinateConfig.getAppSecretKey()));
            taskDO.setUpdatedTime(new Date());
            syncWorkflowTaskMapper.updateById(taskDO);
            syncWorkflowLogService.saveLog("推送DefaultTask任务[reqid:"+requestID+"]",workflowData);
        }catch (Exception e){
            log.error("同步流程信息出错",e);
            taskDO.setExecStatus(SyncWorkflowTaskDO.STATUS_ERROR);
            taskDO.setUpdatedTime(new Date());
            String message="同步流程信息出错"+e.getMessage();
            if(message.length()>1000){ message=message.substring(0,1000); }
            taskDO.setExecResult(message);
            syncWorkflowTaskMapper.updateById(taskDO);
        }
    }
}
