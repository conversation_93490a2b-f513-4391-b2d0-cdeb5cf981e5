/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：AirportInfoUtils <br>
 * Package：com.swcares.reaptv.msg.controller <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.controller;

import com.swcares.components.bd.entity.AirportInfo;
import com.swcares.aps.msg.model.vo.AirportRelated;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.reaptv.msg.controller.AirportInfoUtils <br>
 * Description：TODO：(这里用一句话描述这个类的作用) <br>
 * @author: 王翼 <br>
 * @CreatedAt: 2022/10/17 23:28 <br>
 * @version:
 */
public class AirportInfoUtils {
    public static Map<String, AirportInfo> convertToMap(List<AirportInfo> airportInfos){
        if (airportInfos == null){
            return null;
        }

        Map<String, AirportInfo> result = new HashMap<>(airportInfos.size());
        for(AirportInfo info : airportInfos){
            result.put(info.getAirport3code(), info);
        }

        return result;
    }

    public static void populateAirportInfo(AirportRelated vo, Map<String, AirportInfo>airportInfos){
        if (vo == null){
            return;
        }

        if (vo.getOrgAirport() != null){
            AirportInfo info = airportInfos.get(vo.getOrgAirport());
            if (info != null) {
                vo.setOrgAirportCN(info.getAirportName());
            }
        }

        if (vo.getDestAirport() != null){
            AirportInfo info = airportInfos.get(vo.getDestAirport());
            if (info != null) {
                vo.setDestAirportCN(info.getAirportName());
            }
        }
    }

    public static <T extends AirportRelated> void populateAirportInfo(List<T> voList, Map<String, AirportInfo>airportInfos){
        if (voList == null || voList.isEmpty()){
            return;
        }

        for(AirportRelated vo : voList){
            populateAirportInfo(vo, airportInfos);
        }
    }
}
