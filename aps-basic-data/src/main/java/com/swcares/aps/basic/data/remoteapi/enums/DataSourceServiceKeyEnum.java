package com.swcares.aps.basic.data.remoteapi.enums;

/**
 * @ClassName：DataSourceKeyEnum
 * @Description：数据源自定义key
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2025/3/18 14:04
 * @version： v1.0
 */
public enum DataSourceServiceKeyEnum {
    DATA_SOURCE_DEFAULT("DATA_SOURCE_DEFAULT","航班旅客数据默认从DB获取"),
    DATA_SOURCE_SC("DATA_SOURCE_SC","山航航班或旅客数据，从java接口获取"),
    ;
    private String key;
    private String value;

    public String getKey() {
        return key;
    }

    DataSourceServiceKeyEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getValue() {
        return value;
    }
    public static DataSourceServiceKeyEnum fromKey(String key) {
        for (DataSourceServiceKeyEnum dataSourceKey : values()) {
            if (dataSourceKey.getKey().equals(key)) {
                return dataSourceKey;
            }
        }
        throw new IllegalArgumentException("未找到对应的数据源key: " + key);
    }

}
