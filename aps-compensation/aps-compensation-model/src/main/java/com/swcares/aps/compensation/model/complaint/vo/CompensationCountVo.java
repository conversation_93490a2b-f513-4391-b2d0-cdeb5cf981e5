package com.swcares.aps.compensation.model.complaint.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "CompensationCountVo对象", description = "旅客投诉补偿详情列表")
public class CompensationCountVo {
    @ApiModelProperty(value = "补偿单类型")
    private String accidentType;

    @ApiModelProperty(value = "补偿方式")
    private String compensateType;

    @ApiModelProperty(value = "补偿单号")
    private String orderNo;

    @ApiModelProperty(value = "补偿状态")
    private String status;

    @ApiModelProperty(value = "本次补偿现金价值（元）")
    private String currentAmount;

    @ApiModelProperty(value = "是否冻结")
    private String switchOff;

    @ApiModelProperty(value = "领取状态")
    private String receiveStatus;

    @ApiModelProperty(value = "创建人")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

}
