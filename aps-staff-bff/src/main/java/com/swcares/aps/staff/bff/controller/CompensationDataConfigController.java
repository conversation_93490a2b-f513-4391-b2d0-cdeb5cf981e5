package com.swcares.aps.staff.bff.controller;

import com.swcares.aps.staff.bff.service.CompensationDataConfigService;
import com.swcares.aps.staff.bff.vo.CompensationDataConfigVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * ClassName：CompensationDataConfigController <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/3 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/h5")
@Api(tags = "基础信息，配置相关接口")
@ApiVersion(value = "工作人员端v1.0")
public class CompensationDataConfigController extends BaseController {


	private CompensationDataConfigService compensationDataConfigService;

	@Autowired
	public CompensationDataConfigController(CompensationDataConfigService compensationDataConfigService){
		this.compensationDataConfigService=compensationDataConfigService;
	}


	@GetMapping("/baggage/config")
	@ApiOperation(value = "获取异常行李事故单业务配置")
	public BaseResult<Map<String, CompensationDataConfigVO>> getCompensationConfigByType(){
		return compensationDataConfigService.getBaggageAccidentConfigs();
	}

}
