package com.swcares.aps.compensation.model.irregularflight.dto;

import lombok.Builder;
import lombok.Data;

/**
 * ClassName：com.swcares.aps.compensation.model.irregularflight.dto <br>
 * Description：审核流程发起参数 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 12月02日 9:46 <br>
 * @version v1.0 <br>
 */
@Data
@Builder
public class AuditStartParamDTO {

    private String accidentType;
    private String fcType;
    private String serviceCity;
    private String compensateType;
    private String isCustom;
    private String delayInterval;
    private String sumMoney;
    private String optionCode;
    private String adultAmount;
    // 补偿来源
    private String accidentSource;
    /**
     *
     *航变类型原因归属-字典枚举值fc_type_owner
     */
    private String  fcTypeOwner;

    /**
     *补偿事故单类型- 1.不正常航班 2异常行李 3超售
     */
    private String compensationAccidentType;

    //补偿总金额
    private String compensationAmount;
    // 补偿来源
    private String source;
    //归属航司
    private String belongAirline;
    //事故来源机场code
    private String accidentSourceAirportCode;
}
