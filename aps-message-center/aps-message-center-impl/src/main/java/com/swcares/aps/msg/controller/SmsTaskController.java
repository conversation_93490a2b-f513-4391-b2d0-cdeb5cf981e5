package com.swcares.aps.msg.controller;

import com.swcares.aps.msg.model.dto.*;
import com.swcares.aps.msg.model.entity.SmsTaskImportReciverInfo;
import com.swcares.aps.msg.model.vo.*;
import com.swcares.aps.msg.service.SmsTaskService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.text.ParseException;
import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/
 * ClassName：com.swcares.reaptv.msg.controller.SmsTaskController
 * Description：(用一句话描述这个类或者接口表示什么)
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2022-11-30 14:56
 */
@RestController
@RequestMapping({"/msg/sms/task"})
@Api(tags = {"短信任务接口"})
@ApiVersion({"SmsTask Api v1.0"})
@Validated
public class SmsTaskController extends BaseController {

    @Autowired
    private SmsTaskService smsTaskService;

    @GetMapping("/getBusinessType")
    @ApiOperation("下拉框获取业务类型")
    public BaseResult<List<BusinessTypeVO>> getBusinessType(@NotNull @RequestParam String[] value){
        return ok(smsTaskService.getBusinessType(value));
    }

    @ApiOperation("选择接收人-pnr、手机号、票号导入")
    @PostMapping(value = "/import/reciver", headers = "content-type=multipart/form-data")
    public BaseResult<SmsTaskImportVO> importReciver(@NotNull @ApiParam("4-pnr 3-票号 2-手机号") @RequestParam String type, @RequestPart(name = "file") MultipartFile file){
        return ok(smsTaskService.importReciver(type,file));
    }

    @ApiOperation("pnr、票号、手机号导入结果查看")
    @PostMapping("/import/detail")
    public PagedResult<List<SmsTaskReciverImportVO>> importDetail(@RequestBody SmsTaskReciverImportDTO dto){
        return ok(smsTaskService.importDetail(dto));
    }

    @ApiOperation("pnr、票号、手机号下载导入失败的数据")
    @PostMapping("/download/fail")
    public BaseResult<List<SmsTaskImportReciverInfo>> downloadFail(@NotNull @ApiParam("导入后返回的id")@RequestParam String id){
        return ok(smsTaskService.downloadFail(id));
    }

    @ApiOperation("短信任务停用")
    @GetMapping("/disable")
    public BaseResult disable(@NotNull @ApiParam("id")@RequestParam String id){
        smsTaskService.disable(id);
        return ok();
    }

    @ApiOperation("半自动短信审核操作")
    @PostMapping("/audit")
    public BaseResult audit(@RequestBody AuditSmsDTO dto){
        smsTaskService.audit(dto);
        return ok();
    }

    @ApiOperation("半自动短信批量审核操作")
    @PostMapping("/audit/batch")
    public BaseResult batchAudit(@RequestBody AuditBatchSmsDTO dto) throws ParseException {
        smsTaskService.batchAudit(dto);
        return ok();
    }

    @ApiOperation("手动创建短信任务")
    @PostMapping("/createTaskByHand")
    public BaseResult createTaskByHand(@RequestBody SmsTaskInfoDTO dto){
        smsTaskService.createTaskByHand(dto);
        return ok();
    }

    @ApiOperation("半自动短信审核航班信息及任务信息")
    @GetMapping("/audit/taskInfo")
    public BaseResult<SmsTaskAuditInfoVO> getAuditInfo(@NotNull @ApiParam("任务Id")@RequestParam("taskId") String taskId){
        return ok(smsTaskService.getAuditInfo(taskId));
    }

    @PostMapping("/detail/send")
    @ApiOperation("短信任务查看-发送详情")
    public BaseResult<SmsTaskSendDetailStaticsVO> getSmsTaskSendDetail(@RequestBody SmsTaskSendDetailDTO dto){
        return ok(smsTaskService.getSmsTaskSendDetail(dto));
    }

    @PostMapping("/detail/send/export")
    @ApiOperation(value = "根据查询条件查询短信任务发送详情", notes = "一次性返回所有符合条件的发送详情，需要谨慎使用")
    public BaseResult<List<SmsTaskSendDetailVO>> getSmsTaskSendDetails(@RequestBody SmsTaskSendDetailDTO dto){
        return ok(smsTaskService.getSmsTaskSendDetails(dto));
    }

    @PostMapping("/detail/send/exportSelected")
    @ApiOperation(value="获取指定的短信任务中指定的发送详情")
    public BaseResult<List<SmsTaskSendDetailVO>> getSelectedSmsTaskSendDetails(@RequestBody SmsTaskSendDetailExportDTO dto){
        return ok(smsTaskService.getSelectedSmsTaskSendDetails(dto));
    }
    @GetMapping("/detail/task")
    @ApiOperation("短信任务查看-任务信息")
    public BaseResult<SmsTaskDetailVO> getSmsTaskDetail(@NotNull @RequestParam("taskId") String taskId){
        return ok(smsTaskService.getSmsTaskDetail(taskId));
    }

    @ApiOperation("半自动短信审核旅客列表")
    @PostMapping("/audit/getPassgener")
    public BaseResult<SmsTaskAuditVO> getAuditPassenger(@RequestBody AuditPassgenerSearchDTO dto){
        return ok(smsTaskService.getAuditPassenger(dto));
    }

    @PostMapping("/list")
    @ApiOperation("短信任务列表")
    public PagedResult<List<SmsTaskVO>> getSmsTaskList(@RequestBody SmsTaskSearchDTO dto){
        return ok(smsTaskService.getSmsTaskList(dto));
    }


    @PostMapping("/previewSmsContentForCreateTask")
    @ApiOperation("创建短信任务的时候预览短信内容")
    public BaseResult<String> previewSmsContentForCreateTask(@RequestBody SmsTaskInfoDTO dto){
        return ok(smsTaskService.getPreviewSmsContent(dto));
    }

    @PostMapping("/previewSmsContentForAudit")
    @ApiOperation("审核短信任务的时候预览短信内容")
    public BaseResult<String> previewSmsContentForAuditTask(@RequestBody AuditSmsDTO dto){
        return ok(smsTaskService.getPreviewSmsContent(dto));
    }
}

