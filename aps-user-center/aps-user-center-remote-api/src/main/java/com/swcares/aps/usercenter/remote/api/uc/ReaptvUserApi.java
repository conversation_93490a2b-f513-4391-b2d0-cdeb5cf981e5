package com.swcares.aps.usercenter.remote.api.uc;

import com.swcares.aps.usercenter.model.uc.dto.ReaptvUserAddDTO;
import com.swcares.aps.usercenter.model.uc.dto.ReaptvUserUpdateDTO;
import com.swcares.aps.usercenter.model.uc.vo.ReaptvUserAllVO;
import com.swcares.aps.usercenter.model.uc.vo.ReaptvUserDetailVO;
import com.swcares.aps.usercenter.model.uc.vo.ReaptvUserEmployeeVO;
import com.swcares.aps.usercenter.model.uc.vo.ReaptvUserVO;
import com.swcares.aps.usercenter.vo.UserBriefInfoVO;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.common.uc.dto.*;
import com.swcares.common.uc.vo.UserEnterpriseAdminVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ClassName：com.swcares.reaptv.usercenter.impl.uc.controller.UserController <br>;
 * Description：藏航 用户中心 前端控制器 <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/5/26 17:50 <br>;
 * @version v1.0 <br>;
 */
@FeignClient(name = "aps-user-center", path = "/api/aps-user-center", contextId = "ReaptvUserApi")
public interface ReaptvUserApi {

    @PostMapping("/uc/reaptv_user/save")
    BaseResult<String> save(@RequestBody ReaptvUserAddDTO dto);

    @PostMapping("/uc/reaptv_user/delete/{id}")
    BaseResult<Object> delete(@PathVariable("id") Long id);

    @PostMapping("/uc/reaptv_user/delete")
    BaseResult<Object> deleteById(Long id);

    @PostMapping("/uc/reaptv_user/update")
    BaseResult<?> update(@RequestBody ReaptvUserUpdateDTO dto);

    @GetMapping("/uc/reaptv_user/get/{id}")
    @ApiOperation(value = "用户详情显示接口")
    BaseResult<ReaptvUserDetailVO> get(@PathVariable("id") Long id);

    @GetMapping("/uc/reaptv_user/get")
    BaseResult<ReaptvUserDetailVO> getById(@RequestParam Long id);

    @PostMapping("/uc/reaptv_user/page")
    PagedResult<List<ReaptvUserVO>> page(@RequestBody UserPagedDTO dto);

    @PostMapping("/uc/reaptv_user/page_ca")
    PagedResult<List<ReaptvUserVO>> pageCa(@RequestBody UserPagedCADTO dto);

    @PostMapping("/uc/reaptv_user/batch_auth")
    BaseResult<?> authorize(@RequestBody UserAuthorizeDTO dto);

    @PostMapping("/uc/reaptv_user/batch_auth_delete")
    BaseResult<?> authorizeDelete(@RequestBody UserAuthorizeDTO dto);

    @PostMapping("/uc/reaptv_user/reset_pwd/{id}")
    BaseResult<String> resetPassword(@PathVariable("id") Long id);

    @PostMapping("/uc/reaptv_user/reset_pwd")
    BaseResult<String> resetPasswordById(@RequestBody Long id);

    @PostMapping("/uc/reaptv_user/reset_pwd_by_name")
    BaseResult<String> resetPasswordByName(@RequestBody String username);

    @PostMapping("/uc/reaptv_user/change_status")
    BaseResult<?> changeStatus(@RequestBody UserChangeStatusDTO dto);

    @PostMapping("/uc/reaptv_user/change_status_by_name")
    BaseResult<?> changeStatusByName(@RequestBody UserChangeStatusByNameDTO dto);

    @PostMapping("/uc/reaptv_user/modify_pwd")
    BaseResult<?> modifyPassword(@RequestBody UserChangePasswordDTO dto);

    @GetMapping("/uc/reaptv_user/ent_admin")
    BaseResult<UserEnterpriseAdminVO> entAdmin();

    @PostMapping("/uc/reaptv_user/choose_page")
    PagedResult<List<ReaptvUserEmployeeVO>> choosePage(@RequestBody UserPagedDTO dto);

    @PostMapping("/uc/reaptv_user/get_by_ids")
    BaseResult<List<ReaptvUserAllVO>> getByIds(@RequestBody List<Long> ids);

    @PostMapping("/uc/reaptv_user/work_for_org")
    BaseResult<List<ReaptvUserEmployeeVO>> workForOrg(@RequestBody UserWorkForOrgDTO dto);

    @PostMapping("/uc/reaptv_user/get_by_pos")
    BaseResult<List<ReaptvUserVO>> getByPos(@RequestBody UserForPositionDTO dto);

    @PostMapping("/uc/reaptv_user/belong_to_org")
    BaseResult<List<ReaptvUserEmployeeVO>> belongToOrg(@RequestBody UserBelongToOrgDTO dto);

    @GetMapping("/uc/reaptv_user/user_brief_info")
    public BaseResult<UserBriefInfoVO> getUserBriefInfo(@RequestParam(name = "id", required = true)Long id);

    @PostMapping("/bind_wechat_openid")
    void bindWechatOpenid(@RequestParam(name = "userName", required = true)String userName, 
            @RequestParam(name = "password", required = true)String password, 
            @RequestParam(name = "appId", required = true)String appId,
            @RequestParam(name = "openId", required = true)String openId);
}
