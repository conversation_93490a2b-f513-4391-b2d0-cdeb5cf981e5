package com.swcares.aps.compensation.model.complaint.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


@TableName("COMPLAINT_ACCIDENT_INFO")
@Data
public class ComplaintAccidentInfoEntity {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    private Long tenantId;

    @ApiModelProperty(value = "旅客投诉事故单单号")
    private String accidentId;

    @ApiModelProperty(value = "事故单状态")
    private String accidentStatus;

    @ApiModelProperty(value = "事故单来源")
    private String accidentSource;

    @ApiModelProperty(value = "归属航司")
    private String belongAirline;

    @ApiModelProperty(value = "归属航司-中文简称")
    private String belongAirlineAbbr;

    @ApiModelProperty(value = "事故单类型：旅客投诉")
    private String accidentType;

    @ApiModelProperty(value = "事故类型")
    private String accidentSubType;

    @ApiModelProperty(value = "原因类型")
    private String reasonType;

    @ApiModelProperty(value = "航班日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date flightDate;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "选择航段")
    private String segment;

    @ApiModelProperty(value = "创建人")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createdTime;

    @ApiModelProperty(value = "作废人")
    private String invalidUser;

    @ApiModelProperty(value = "作废时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date invalidTime;

    @ApiModelProperty(value = "旅客电话")
    private String contactInfo;

    @ApiModelProperty(value = "投诉部门")
    private String complaintDep;

    @ApiModelProperty(value = "投诉渠道")
    private String complaintChannel;

    @ApiModelProperty(value = "附件")
    private String filesUrl;

    @ApiModelProperty(value = "投诉事故描述")
    private String accidentDes;

    @ApiModelProperty(value = "选择航段中文")
    private String segmentCh;
}