package com.swcares.aps.compensation.model.compensation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import java.math.BigDecimal;

/**
 * @ClassName：CompensationCashAddCommandDTO
 * @Description：现金补偿单创建
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 15:53
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationCashAddCommandDTO对象", description="现金补偿单创建参数")
public class CompensationCashAddCommandDTO extends  CompensationAddAndEditCommandDTO implements CompensationAddCommand{

    /**
     * 补偿总金额
     */
    @ApiModelProperty(value = "补偿总金额")
    private BigDecimal compensationAllMoney;

    /**
     * 自定义现金，赔偿金额标准
     */
    @ApiModelProperty(value = "自定义赔偿金额标准")
    private BigDecimal customCompensationMoney;

    /**
     * 自定义现金，旅客人数
     */
    @ApiModelProperty(value = "旅客人数")
    @Min(value =1)
    private Long passengerNum;


}
