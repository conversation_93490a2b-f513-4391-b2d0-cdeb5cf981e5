package com.swcares.aps.staff.bff.service.impl;

import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageInfoPageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageInfoVO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.aps.staff.bff.service.BaggageLuggageService;
import com.swcares.aps.staff.bff.vo.StaffBaggageLuggageVO;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName：BaggageLuggageServiceImpl
 * @Description：行李查询服务
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/16 9:46
 * @version： v1.0
 */
@Service
public class BaggageLuggageServiceImpl implements BaggageLuggageService {

    @Autowired
    private CompensationInfoApi compensationInfoApi;

    public PagedResult<List<StaffBaggageLuggageVO>> page(LuggageInfoPageDTO dto) {
        if(ObjectUtils.isEmpty(dto.getMustHasStock())){
            dto.setMustHasStock(true);
        }
        PagedResult<List<LuggageInfoVO>> luggageResult = compensationInfoApi.findLuggageList(dto);

        PagedResult<List<StaffBaggageLuggageVO>> result=new PagedResult<>();
        result.setCode(luggageResult.getCode());
        result.setMessage(luggageResult.getMessage());

        if(luggageResult.getCode()== BaseResult.OK_CODE){
            List<StaffBaggageLuggageVO> staffBaggageLuggageVOs = Optional.ofNullable(luggageResult.getData()).map(Collection::stream).orElse(Stream.empty())
                    .map(luggageInfoVO -> ObjectUtils.copyBean(luggageInfoVO,StaffBaggageLuggageVO.class)
                    ).collect(Collectors.toList());

            result=PagedResult.ok(staffBaggageLuggageVOs,
                    luggageResult.getTotalRecords(),
                    luggageResult.getTotalPages(),
                    luggageResult.getPageSize(),
                    luggageResult.getCurrentPage());
        }


        return result;
    }
}
