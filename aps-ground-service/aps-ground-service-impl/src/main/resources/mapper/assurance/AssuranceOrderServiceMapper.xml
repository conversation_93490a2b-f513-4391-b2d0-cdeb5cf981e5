<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.ground.assurance.mapper.AssuranceOrderServiceMapper">
    <update id="logicRemoveById">
        update ASSURANCE_ORDER_SERVICE set DELETED=1 where id = #{id}
    </update>

    <select id="h5PageQueryOrderList" resultType="com.swcares.aps.ground.models.assurance.vo.AssuranceOrderServiceListVO">
        select
        aos.id orderServiceId,SERVICE_PER_AMOUNT*TOTAL_USER_AMOUNT as totalServiceAmount,
        aos.*,gof.ORDER_NO,GOF.FLIGHT_DATE,GOF.FLIGHT_NO,gof.ORDER_STATE,
        GOF.LAST_SUBMIT_TIME , GOF.SUBMIT_BY,GOF.SUBMIT_ID
        from ASSURANCE_ORDER_SERVICE aos
        LEFT JOIN ASSURANCE_ORDER_INFO gof on aos.ORDER_ID = gof."ID"
        where AOS.DELETED ='0' and GOF.DELETED = '0'

        <if test="dto.orderId!=null and dto.orderId!='' ">and aos.ORDER_ID = #{dto.orderId}</if>
        <if test="dto.orderServiceId!=null and dto.orderServiceId!='' ">and aos.id = #{dto.orderServiceId}</if>
        <if test="dto.flightNo!=null and dto.flightNo!='' "> and GOF.FLIGHT_NO = #{dto.flightNo}</if>
        <if test="dto.startFlightDate!=null">and GOF.FLIGHT_DATE  &gt;=   #{dto.startFlightDate}</if>
        <if test="dto.endFlightDate!=null">and GOF.FLIGHT_DATE &lt;=   #{dto.endFlightDate} </if>
        <if test="dto.orderNo!=null and dto.orderNo!='' ">and gof.ORDER_NO = #{dto.orderNo}</if>
        <if test="dto.orderState!=null and dto.orderState!='' ">and gof.ORDER_STATE = #{dto.orderState}</if>
        <if test="dto.serviceAirport!=null and dto.serviceAirport!='' ">and aos.SERVICE_AIRPORT = #{dto.serviceAirport}</if>
        <if test="dto.serviceTarget!=null and dto.serviceTarget!='' ">and AOS.SERVICE_TARGET = #{dto.serviceTarget}</if>
<!--        <if test="dto.belongTenantType!=null and dto.belongTenantType!='' ">and gof.BELONG_TENANT_TYPE  = #{dto.belongTenantType}</if>-->
        <if test="dto.belongAirlineCode!=null and dto.belongAirlineCode!='' ">and gof.BELONG_AIRLINE_CODE = #{dto.belongAirlineCode}</if>


        <if test="dto.serviceTargets!=null and dto.serviceTargets.size()>0 ">
            and AOS.SERVICE_TARGET  in
            <foreach collection="dto.serviceTargets" open="(" close=")" separator="," item="st">
                #{st}
            </foreach>
        </if>

        <if test="dto.orderStates!=null and dto.orderStates.size()>0 ">
            and gof.ORDER_STATE   in
            <foreach collection="dto.orderStates" open="(" close=")" separator="," item="orderState">
                #{orderState}
            </foreach>
        </if>

        <if test="dto.serviceAirports!=null and dto.serviceAirports.size()>0 ">
            and aos.SERVICE_AIRPORT    in
            <foreach collection="dto.serviceAirports" open="(" close=")" separator="," item="serviceAirport">
                #{serviceAirport}
            </foreach>
        </if>

        order by GOF.FLIGHT_DATE desc, (select SORT_NUM from SYS_DICTIONARY_DATA where DICT_TYPE = 'assurance_order_state' and DICT_ITEM = gof.ORDER_STATE) asc
    </select>
    <select id="selectDistinctServiceName" resultType="java.lang.String">
        select DISTINCT(SERVICE_NAME) from  ASSURANCE_ORDER_SERVICE where  DELETED = '0'
    </select>
    <update id="refreshServiceVerifyAmount">
        update ASSURANCE_ORDER_SERVICE t set TOTAL_VERIFY_AMOUNT = (
        select count(*) from ASSURANCE_ORDER_SERVICE_VERIFY v where v.ORDER_SERVICE_ID=t.ID and v.VERIFICATION_STATE = 'CONFIRM'
        ) where t.ID in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
</mapper>