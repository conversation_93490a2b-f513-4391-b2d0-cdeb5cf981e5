package com.swcares.aps.compensation.model.irregularflight.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: CpcNodeInfoDTO
 * @projectName aps
 * @description:
 * @date 2021/12/23 14:38
 */
@Data
public class CpcNodeInfoDTO {
    //节点名称
    private String nodeName;
    private String nodeKey;
    private String nodeType;
    //当前处理状态1:开始2:已执行3:未执行
    private String status;
    private String assignee;
    //审核节点信息（可能是角色id，人员id，部门id；例如:"userId:612,613"）
    private List<String> candidateUsers;
    private List<Object> candidateGroups;
}
