package com.swcares.aps.compensation.model.compensation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CompensationMaterialDetailFinalVO
 * @Description：箱包补偿单详情分段展示信息VO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 唐康
 * @Date： 2022/4/14 14:55
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationMaterialDetailFinalVO", description="箱包补偿单详情分段展示信息VO")
public class CompensationMaterialDetailFinalVO {

    @ApiModelProperty(value = "箱包补偿单详情信息下的事故信息")
    private CompensationMaterialDetailCompensationVO compensationMaterialDetailCompensationVO;

    //箱包补偿单详情信息下的审核信息待添加

    @ApiModelProperty(value = "箱包补偿单详情信息下的航班信息")
    private CompensationMaterialDetailFlightVO compensationMaterialDetailFlightVO;

    @ApiModelProperty(value = "箱包补偿单详情信息下的补偿旅客信息")
    private CompensationMaterialDetailPaxVO compensationMaterialDetailPaxVO;

    @ApiModelProperty(value = "箱包补偿单详情信息下的补偿实物与成本信息")
    private CompensationMaterialDetailMaterialVO compensationMaterialDetailMaterialVO;

    @ApiModelProperty(value = "是否可以进行审核，Y是 N否")
    private String toExamine;

    @ApiModelProperty(value = "审核节点id")
    private String taskId;

    @ApiModelProperty(value = "是否为发起人")
    private Boolean isSponsor;
}
