package com.swcares.aps.web.bff.controller.ground;

import com.swcares.aps.ground.SupplierApi;
import com.swcares.aps.ground.models.supplier.dto.SupplierListRequest;
import com.swcares.aps.ground.models.supplier.dto.SupplierServiceFullRequest;
import com.swcares.aps.ground.models.supplier.vo.SupplierFullVO;
import com.swcares.aps.ground.models.supplier.vo.SupplierListVO;
import com.swcares.aps.ground.models.supplier.vo.SupplierServiceFullVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName：SupplierQueryController
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/19 12:08
 * @version： v1.0
 */
@RestController
@RequestMapping("/supplier/query")
@Api(tags = {"服务商相关查询接口"})
@ApiVersion({"服务商相关Api"})
public class SupplierQueryController extends BaseController {

    @Autowired
    private SupplierApi supplierApi;

    @GetMapping("/airport")
    @ApiOperation(value = "查询所有有效供应商的航站CODE")
    public BaseResult<List<String>> getHasSupplierAirport(){

        return supplierApi.getHasSupplierAirportCode();
    }


    @GetMapping("/airline")
    @ApiOperation(value = "查询所有有效供应商的航司CODE")
    public BaseResult<List<String>> getHasSupplierAirline(){

        return supplierApi.getHasSupplierAirlineCode();
    }

    @PostMapping("/page")
    @ApiOperation(value = "分页查询供应商列表信息")
    public PagedResult<List<SupplierListVO>> pageQuerySupplierList(@RequestBody SupplierListRequest request){
        return supplierApi.pageQuerySupplierList(request);
    }

    @GetMapping("/get/{id}")
    @ApiOperation(value = "根据ID查询服务项基础信息")
    public BaseResult<SupplierFullVO> detail(@PathVariable @ApiParam(value = "主键id", required = true) String id){
        return supplierApi.getSupplierFullDetail(id);
    }

    @PostMapping("/service")
    @ApiOperation(value = "获取供应商服务项")
    public BaseResult<List<SupplierServiceFullVO>> getSupplierServiceFulls(@RequestBody SupplierServiceFullRequest request){
        return supplierApi.getSupplierServiceFulls(request);
    }
}
