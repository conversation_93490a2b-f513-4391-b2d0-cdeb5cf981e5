package com.swcares.aps.msg.service;

import com.swcares.aps.msg.model.entity.TempletePlaceHolder;
import com.swcares.baseframe.common.mybatis.base.BaseService;

/**
 * ClassName：com.swcares.reaptv.sms.SMS.service.TempletePlaceHolderService <br>
 * Description： 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-09-08 <br>
 * @version v1.0 <br>
 */
public interface TempletePlaceHolderService extends BaseService<TempletePlaceHolder> {

}
