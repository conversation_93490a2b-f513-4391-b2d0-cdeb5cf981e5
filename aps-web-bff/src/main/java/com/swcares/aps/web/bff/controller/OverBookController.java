package com.swcares.aps.web.bff.controller;

import com.swcares.aps.compensation.model.overbook.dto.*;
import com.swcares.aps.compensation.model.overbook.entity.OverBookConfigDO;
import com.swcares.aps.compensation.model.overbook.vo.*;
import com.swcares.aps.compensation.remote.api.irregularflight.OverBookAccidentApi;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @description 超售事故单
 * <AUTHOR>
 * @date 2024/6/4 10:10
 * @return
 */
@RestController
@RequestMapping("/pc/overbook")
@Api(tags = "超售事故单接口")
@ApiVersion(value = "超售事故单接口v1.0.1")
@Slf4j
public class OverBookController {

    @Autowired
    OverBookAccidentApi overBookAccidentApi;

    @PostMapping("/getPcList")
    @ApiOperation(value = "PC超售事故单信息列表查询")
    public PagedResult<List<OverBookPcListVO>> getPcList(@RequestBody OverBookPcSearchDTO dto){
        return overBookAccidentApi.getPcList(dto);
    }
    @GetMapping("/getPcDetail")
    @ApiOperation(value = "PC超售事故单详情")
    public BaseResult<OverBookPcDetailsVO> getPcDetail(@RequestParam Long accidentId){

        return overBookAccidentApi.getPcDetail(accidentId);
    }

    @PostMapping("/getCompensateNumList")
    @ApiOperation(value = "PC超售事故单详情-补偿次数列表")
    public BaseResult<List<CompensationNumListVO>> getCompensateNumList(@RequestBody OverBookPaxSearchDTO dto){
        return overBookAccidentApi.getCompensateNumList(dto);
    }
}
