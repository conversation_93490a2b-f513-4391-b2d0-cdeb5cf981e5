package com.swcares.aps.component.drools.impl.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.swcares.aps.component.drools.impl.init.KieSessionBuilder;
import com.swcares.aps.component.drools.impl.mapper.DroolsRuleMapper;
import com.swcares.aps.component.drools.impl.service.DroolsRuleService;
import com.swcares.aps.component.drools.model.dto.DroolsRulePageDTO;
import com.swcares.aps.component.drools.model.entity.DroolsRuleDO;
import com.swcares.aps.component.drools.model.vo.DroolsRuleVO;
import com.swcares.baseframe.utils.lang.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.kie.api.runtime.KieSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;

/**
 * ClassName：com.swcares.component.drools.impl.service.impl.DroolsRuleServiceImpl <br>
 * Description：规则引擎服务类 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021/10/19 16:17 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Transactional(rollbackFor = Exception.class)
@Service
public class DroolsRuleServiceImpl extends ServiceImpl<DroolsRuleMapper, DroolsRuleDO> implements DroolsRuleService {

    @Autowired
    private KieSessionBuilder KieSessionBuilder;

    @Override
    public boolean logicRemoveById(Long id) {
        return SqlHelper.retBool(getBaseMapper().logicRemoveById(id));
    }

    @Override
    public IPage<DroolsRuleVO> page(DroolsRulePageDTO dto) {
        IPage<DroolsRuleVO> result = getBaseMapper().page(dto, dto.createPage());
        return result;
    }

    /**
     * Title：disposeSession <br>
     * Description：销毁规则会话 <br>
     * author：王磊 <br>
     * date：2021/10/20 13:49 <br>
     *
     * @param kieSession <br>
     * @return <br>
     */
    @Override
    public void disposeSession(KieSession kieSession) {
        if (kieSession != null) {
            kieSession.dispose();
        }
    }

    /**
     * Title：getKieSessionFromDrl <br>
     * Description：构建动态规则 <br>
     * author：王磊 <br>
     * date：2021/10/20 13:49 <br>
     *
     * @param code <br>
     * @return <br>
     */
    @Override
    public KieSession getKieSessionFromDrl(String code) throws UnsupportedEncodingException {
        long startTime = System.currentTimeMillis();
        if (StringUtils.isNotEmpty(code)) {
            KieSession kieSession = KieSessionBuilder.getKieSession(code);
            log.info("【aps-compensation-impl】耗时统计——规则引擎->从KieSessionBuilder组件（redis中）获取kieSession，当前（非累计）耗时：【{}】-------", (System.currentTimeMillis() - startTime));
            return kieSession;
        }
        return null;
    }
}
