package com.swcares.aps.basic.data.businessimpl.model.vo;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: FocFlightInfoDTO
 * @projectName aps
 * @description:  临时航班数据表DTO对象
 * @date 2021/11/2 10:22
 */
@Data
@ApiModel(value="FocFlightInfoDTO对象", description="临时航班表")
public class FocFlightInfoDTO implements BaseDTO {
    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航段三字码")
    private String segment;

}
