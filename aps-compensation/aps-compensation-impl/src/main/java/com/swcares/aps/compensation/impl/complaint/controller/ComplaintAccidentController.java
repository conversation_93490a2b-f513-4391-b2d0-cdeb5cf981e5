package com.swcares.aps.compensation.impl.complaint.controller;

import com.swcares.aps.compensation.impl.complaint.service.impl.ComplaintAccidentService;
import com.swcares.aps.compensation.model.complaint.dto.ComplaintAccidentCreateDto;
import com.swcares.aps.compensation.model.complaint.dto.ComplaintAccidentQueryDto;
import com.swcares.aps.compensation.model.complaint.entity.ComplaintAccidentInfoEntity;
import com.swcares.aps.compensation.model.complaint.vo.ComplaintAccidentCreateCheckVo;
import com.swcares.aps.compensation.model.complaint.vo.ComplaintAccidentDetailVo;
import com.swcares.aps.compensation.model.complaint.vo.ComplaintAccidentQueryVo;
import com.swcares.aps.compensation.model.complaint.vo.ComplaintDraftVo;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/complaint/accident")
@Api(tags = "旅客投诉事故单统一接口")
@ApiVersion(value = "旅客投诉事故单统一接口 v1.0.1")
public class ComplaintAccidentController extends BaseController {

    @Autowired
    ComplaintAccidentService complaintAccidentService;

    @PostMapping("/page")
    @ApiOperation(value = "事故单列表查询")
    public PagedResult<List<ComplaintAccidentQueryVo>> page(@RequestBody ComplaintAccidentQueryDto complaintAccidentQueryDto){
        return ok(complaintAccidentService.pageQuery(complaintAccidentQueryDto));
    }

    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "id", value = "事故单主键id", required = true),
            @ApiImplicitParam(name = "flag", value = "操作标识：0：删除，1：作废", required = true)
    })
    @GetMapping("/operate/{id}/{flag}")
    @ApiOperation(value = "操作事故单，删除或作废")
    public BaseResult operate(@PathVariable(value = "id") Long id, @PathVariable(value = "flag") String flag) {
        return ok(complaintAccidentService.operate(id,flag));
    }

    @PostMapping("/check")
    @ApiOperation(value = "事故单校验")
    public BaseResult<ComplaintAccidentCreateCheckVo> check(@RequestBody @Valid ComplaintAccidentCreateDto complaintAccidentCreateDto){
        return ok(complaintAccidentService.check(complaintAccidentCreateDto));
    }

    @PostMapping("/create")
    @ApiOperation(value = "保存事故单草稿/生成事故单/包含事故单下一步")
    public BaseResult<Long> create(@RequestBody @Valid ComplaintAccidentCreateDto complaintAccidentCreateDto){
        return ok(complaintAccidentService.create(complaintAccidentCreateDto));
    }

    @ApiImplicitParam(name = "id",value = "事故单主键id",required = true)
    @GetMapping("/draft/{id}")
    @ApiOperation(value = "填写补偿单信息，选择补偿单旅客/草稿编辑查询统一接口")
    public BaseResult<ComplaintDraftVo> draft(@PathVariable(value = "id") Long id){
        return ok(complaintAccidentService.draft(id));
    }

    @ApiImplicitParam(name = "id", value = "事故单主键id", required = true)
    @GetMapping("/detail/{id}")
    @ApiOperation(value = "事故单详情")
    public BaseResult<ComplaintAccidentDetailVo> detail(@PathVariable(value = "id") Long id){
        return ok(complaintAccidentService.detail(id));
    }

    @ApiImplicitParam(name = "id", value = "事故单主键id", required = true)
    @GetMapping("/getOne/{id}")
    @ApiOperation(value = "单一的事故单详情")
    public BaseResult<ComplaintAccidentInfoEntity> getAccidentDetail(@PathVariable(value = "id") Long id){
        return ok(complaintAccidentService.getAccidentDetail(id));
    }
}
