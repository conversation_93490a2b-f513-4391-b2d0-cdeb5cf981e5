/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：PlaneChangeSmsTaskBuilder <br>
 * Package：com.swcares.reaptv.msg.flightevent.taskbuilder <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.flightevent.taskbuilder;

import com.swcares.aps.component.dict.model.vo.DictCacheVO;
import com.swcares.aps.msg.model.dto.SemiAutoSmsTaskDTO;
import com.swcares.aps.msg.model.entity.*;
import com.swcares.aps.msg.model.enums.FlightEventEnum;
import com.swcares.aps.msg.service.FlightEventPassengerService;
import com.swcares.aps.msg.service.TempleteService;
import com.swcares.aps.usercenter.remote.api.uc.ReaptvDictApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * ClassName：com.swcares.reaptv.msg.flightevent.taskbuilder.PlaneChangeSmsTaskBuilder <br>
 * Description：机型调整类的数据的短信任务构造器
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/21 15:22 <br>
 * @version:
 */
@Component
@Slf4j
public class PlaneChangeSmsTaskBuilder extends AbstractSemiSmsTaskBuilder{
    private static final String SUPPORTED_PROCESS_TYPE = "EQT";
    private static final String SMS_TEMPLATE_ID = "PLANE_ADJUST_SEMI_AUTO";

    @Autowired
    private FlightEventPassengerService flightEventPassengerService;

    @Autowired
    private TempleteService templeteService;
    
    @Autowired
    private ReaptvDictApi reaptvDictApi;

    @Override public boolean isSupport(FlightEvent flightEvent) {
        return FlightEventEnum.PLANE_CHANGE.equals(flightEvent);
    }

    @Override
    public List<SemiAutoSmsTaskDTO> buildSemiAutoTask(FlightEvent flightEvent, List<FlightEventPassenger> passengers) {
        List<SemiAutoSmsTaskDTO> results = new ArrayList<>();
        List<FlightEventPassenger> cabinDegradedPassengers = filterDegradedPassenger(passengers);
        if (cabinDegradedPassengers == null || cabinDegradedPassengers.isEmpty()){
            //没有降舱的旅客，不需要创建任务
            log.info("航变消息{}中没有降舱的旅客，不需要创建任务", flightEvent.getMessageId());
            return results;
        }
        
        SemiAutoSmsTaskDTO semiAutoSmsTaskDTO = new SemiAutoSmsTaskDTO();
        Templete templete = templeteService.getById(SMS_TEMPLATE_ID);
        SmsTaskInfo taskInfo = createSmsTaskInfo(flightEvent, SMS_TEMPLATE_ID, templete.getTempleteContent(), cabinDegradedPassengers);
        semiAutoSmsTaskDTO.setTaskInfo(taskInfo);
        
                
        List<SmsTaskDetailInfo> taskDetailInfos = createTaskDetailInfos(flightEvent, cabinDegradedPassengers);
        semiAutoSmsTaskDTO.setTaskDetailInfoList(taskDetailInfos);


        results.add(semiAutoSmsTaskDTO);
        return results;
    }

    private List<FlightEventPassenger> filterDegradedPassenger(List<FlightEventPassenger> passengers) {
        List<DictCacheVO> cabinClassConfig =  reaptvDictApi.getByDictType("cabin_class").getData();
        List<FlightEventPassenger> degraedPassengers = new ArrayList<>();
        
        for (FlightEventPassenger passenger : passengers){
            if(isCabinDegraded(passenger, cabinClassConfig)){
                degraedPassengers.add(passenger);
            }
        }
        
        return degraedPassengers;
    }


    @PostConstruct
    public void registerToFactory(){
        SemiAutoSmsTaskBuilderFactory.register(SUPPORTED_PROCESS_TYPE, this);
    }

    @Override
    FlightEventEnum getFlightEventEnum() {
        return FlightEventEnum.PLANE_CHANGE;
    }
}
