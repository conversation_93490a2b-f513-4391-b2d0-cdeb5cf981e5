package com.swcares.aps.compensation.impl.apply.controller;

import com.swcares.aps.compensation.impl.apply.service.ApplyBehalfPaxService;
import com.swcares.aps.compensation.impl.apply.service.ApplyPaxService;
import com.swcares.aps.compensation.model.apply.dto.AuthBehalfPaxDTO;
import com.swcares.aps.compensation.model.apply.vo.AuthCompensationOrderVO;
import com.swcares.aps.compensation.model.apply.vo.CompensationFlightInfoVO;
import com.swcares.aps.compensation.model.apply.vo.CompensationOrderInfoVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * ClassName：com.swcares.compensation.controller.ApplyPaxController <br>
 * Description：航延补偿代领验证 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022-01-06 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/applyBehalf/applyBehalfPax")
@Api(tags = "航延补偿申领旅客信息表接口")
@ApiVersion(value = "申领单接口 v1.0")
public class ApplyBehalfPaxController extends BaseController {
    @Autowired
    private ApplyPaxService applyPaxService;

    @Autowired
    private ApplyBehalfPaxService applyBehalfPaxService;

    @PostMapping("/authBehalfPax")
    @ApiOperation(value = "验证代领人领取信息")
    public BaseResult<Object> authBehalfPax(@RequestBody @Valid AuthBehalfPaxDTO dto) {
        applyBehalfPaxService.authBehalfPax(dto);
        return ok();
    }

    @GetMapping("/sendSMSBehalf")
    @ApiOperation(value = "发送短信验证码")
    public BaseResult<Object> sendSMSBehalf(@ApiParam(value = "手机号", required = true) @RequestParam String phoneNum) {
        applyPaxService.sendSMS(phoneNum);
        return ok();
    }

    @GetMapping("/verificationSMSBehalf")
    @ApiOperation(value = "验证短信验证码")
    public BaseResult<Object> verificationSMSBehalf(@ApiParam(value = "手机号", required = true) @RequestParam String phoneNum,@ApiParam(value = "验证码", required = true) @RequestParam String authCode) {
        applyPaxService.verificationSMS(phoneNum,authCode);
        return ok();
    }

    @GetMapping("/findFlightInfo")
    @ApiOperation(value = "通过航班号和航班日期获取航班信息")
    public BaseResult<CompensationFlightInfoVO> findFlightInfo(@ApiParam(value = "航班号", required = true) @RequestParam String flightNo, @ApiParam(value = "航班日期", required = true) @RequestParam String flightDate) {
        return ok(applyBehalfPaxService.findFlightInfo(flightNo,flightDate));
    }

    @PostMapping("/findCompensationOrderBehalf")
    @ApiOperation(value = "获取旅客赔偿单详情")
    public BaseResult<List<List<CompensationOrderInfoVO>>> findCompensationOrderBehalf(@RequestBody @Valid List<AuthBehalfPaxDTO> dtos) {
        return ok(applyBehalfPaxService.findCompensationOrderBehalf(dtos));
    }

    @GetMapping("/getReplaceRule")
    @ApiOperation(value = "获取配置的代领规则")
    public  BaseResult<Object> getReplaceRule() {
        return ok(applyBehalfPaxService.getReplaceRule());
    }

    @PostMapping("/authCompensationOrderBehalf")
    @ApiOperation(value = "过参数校验旅客数据是否存在异常,如存在异常则返回对应错误的身份证号")
    public BaseResult<AuthCompensationOrderVO> authCompensationOrderBehalf(@RequestBody @Valid List<AuthBehalfPaxDTO> dtos){
        return ok(applyBehalfPaxService.authCompensationOrderBehalf(dtos));
    }

}
