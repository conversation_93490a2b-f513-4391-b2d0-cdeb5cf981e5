package com.swcares.aps.basic.data.remoteapi.model.vo;

import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR> <br>
 * @version v1.0 <br>
 * @ClassName：com.swcares.aps.flightpassenger.model.flight.vo.FlightUnitInfoVO <br>
 * @Description：航班监控详情机组信息 <br>
 * @Copyright 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * @Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * @Date 2022/3/9 14:55 <br>
 */
@Data
@ApiModel(value = "FlightUnitInfoVO", description = "航班监控详情机组信息VO")
@SecretInfoEntity
public class FlightUnitInfoVO {
    @ApiModelProperty(value = "机组编号")
    private String flightUnitCode;
    @ApiModelProperty(value = "职务")
    private String flightUnitPersonnelJob;
    @ApiModelProperty(value = "职务编码")
    private String flightUnitPersonnelJobCode;
    @ApiModelProperty(value = "编号")
    private String flightUnitPersonnelCode;
    @ApiModelProperty(value = "姓名")
    private String flightUnitPersonnelName;
    @ApiModelProperty(value = "性别")
    private String flightUnitPersonnelSex;
    @ApiModelProperty(value = "电话")
    @SecretValue
    private String flightUnitPersonnelPhone;
    @ApiModelProperty(value = "工号")
    private String flightUnitPersonnelWorkNo;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        FlightUnitInfoVO that = (FlightUnitInfoVO) o;
        return Objects.equals(flightUnitCode, that.flightUnitCode) && Objects.equals(flightUnitPersonnelJob, that.flightUnitPersonnelJob) && Objects.equals(flightUnitPersonnelJobCode, that.flightUnitPersonnelJobCode) && Objects.equals(flightUnitPersonnelCode, that.flightUnitPersonnelCode) && Objects.equals(flightUnitPersonnelName, that.flightUnitPersonnelName) && Objects.equals(flightUnitPersonnelSex, that.flightUnitPersonnelSex) && Objects.equals(flightUnitPersonnelPhone, that.flightUnitPersonnelPhone) && Objects.equals(flightUnitPersonnelWorkNo, that.flightUnitPersonnelWorkNo);
    }

    @Override
    public int hashCode() {
        int result = Objects.hashCode(flightUnitCode);
        result = 31 * result + Objects.hashCode(flightUnitPersonnelJob);
        result = 31 * result + Objects.hashCode(flightUnitPersonnelJobCode);
        result = 31 * result + Objects.hashCode(flightUnitPersonnelCode);
        result = 31 * result + Objects.hashCode(flightUnitPersonnelName);
        result = 31 * result + Objects.hashCode(flightUnitPersonnelSex);
        result = 31 * result + Objects.hashCode(flightUnitPersonnelPhone);
        result = 31 * result + Objects.hashCode(flightUnitPersonnelWorkNo);
        return result;
    }
}
