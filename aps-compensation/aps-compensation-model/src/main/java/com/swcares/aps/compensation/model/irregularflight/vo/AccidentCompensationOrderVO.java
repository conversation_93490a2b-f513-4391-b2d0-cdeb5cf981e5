package com.swcares.aps.compensation.model.irregularflight.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.aps.compensating.mode.irregularflight.vo <br>
 * Description：事故单下关联的补偿单 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 10月27日 10:24 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="CompensationOrderInfoDO对象", description="赔偿单信息")
public class AccidentCompensationOrderVO {
    private static final long serialVersionUID = 1L;
//    补偿单号 、 补偿方式 、补偿状态 、 服务航站 、执行人数（/前为已领取人数，/后为总人数）
//    冻结人数  补偿金额（前为已领取金额，/后为总金额） 申请人（工号+姓名） 申请时间   补偿单号正序

    @ApiModelProperty(value = "赔偿单单号")
    private String orderNo;

    @ApiModelProperty(value = "赔偿单id")
    private String orderId;

    @ApiModelProperty(value = "补偿方式 1现金，2虚拟，3实物")
    private String compensateType;

    @ApiModelProperty(value = "0草稿 1驳回 2审核中 3审核通过 4生效 5关闭 6逾期 7审核不通过")
    private String status;

    @ApiModelProperty(value = "服务航站 成都ctu")
    private String serviceCity;

    @ApiModelProperty(value = "计划执行人数（已领取人数）")
    private String planCarryOutNum;

    @ApiModelProperty(value = "实际执行人数（后为总人数）")
    private String actualCarryOutNum;

    @ApiModelProperty(value = "冻结人数")
    private String frozenNum;

    @ApiModelProperty(value = "补偿金额（前为已领取金额）")
    private String planCompensateMoney;

    @ApiModelProperty(value = "补偿金额（后为总金额）")
    private String actualCompensateMoney;

    @ApiModelProperty(value = "申请人- 姓名(工号)")
    private String applyUser;

    @ApiModelProperty(value = "申请时间")
    private String applyDate;


}
