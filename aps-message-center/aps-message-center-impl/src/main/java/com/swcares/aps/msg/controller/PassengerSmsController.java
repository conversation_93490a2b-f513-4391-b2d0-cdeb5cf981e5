/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：PassengerSmsController <br>
 * Package：com.swcares.reaptv.msg.controller <br> 
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.msg.model.dto.AssistantSmsSendDTO;
import com.swcares.aps.msg.model.dto.MessageStatusStaticsDTO;
import com.swcares.aps.msg.model.dto.PassengerSmsSearchDTO;
import com.swcares.aps.msg.model.dto.PassengerSmsSendDTO;
import com.swcares.aps.msg.model.entity.PassengerSmsRecord;
import com.swcares.aps.msg.model.enums.SendStatusEnum;
import com.swcares.aps.msg.model.util.ModelConverter;
import com.swcares.aps.msg.model.vo.MessageStatusStaticsVO;
import com.swcares.aps.msg.model.vo.PassengerSmsRecordVO;
import com.swcares.aps.msg.model.vo.SmsSendResultVO;
import com.swcares.aps.msg.service.AssistantSmsService;
import com.swcares.aps.msg.service.PassengerSmsRecordService;
import com.swcares.aps.msg.service.impl.SearchCriteriaUtils;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.components.bd.entity.AirportInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.swcares.aps.usercenter.remote.api.uc.ReaptvAirportInfoApi;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.reaptv.msg.controller.PassengerSmsController <br>
 * Description：旅客短信发送记录的接口
 * <AUTHOR> <br>
 * date 2022/9/20 9:38 <br>
 * @version v1.0.0 <br>
 */
@RestController
@RequestMapping({"/msg/sms/passenger"})
@Api(tags = {"旅客短信发送记录接口"})
@ApiVersion({"Message Center Api v1.0"})
public class PassengerSmsController {
    @Autowired
    private PassengerSmsRecordService passengerSmsRecordService;

    @Autowired
    private ReaptvAirportInfoApi airportInfoApi;

    @Autowired
    private SearchCriteriaUtils searchCriteriaUtils;

    @Autowired
    private AssistantSmsService assistantSmsService;

    @PostMapping("/send")
    @ApiOperation("给旅客发送短信")
    public BaseResult<SmsSendResultVO> sendSms(@RequestBody PassengerSmsSendDTO dto){
        PassengerSmsRecord entity = passengerSmsRecordService.sendSms(dto);

        SmsSendResultVO result = new SmsSendResultVO();
        result.setStatus(entity.getStatus().getKey());
        result.setReason(entity.getReason());
        result.setId(entity.getId());
        return BaseResult.ok(result);
    }

    @PostMapping("/list")
    @ApiOperation("分页获取旅客短信发送记录")
    public PagedResult<List<PassengerSmsRecordVO>> getPassengerSmsRecord(@RequestBody
                                                                         PassengerSmsSearchDTO searchDTO){
        transferBusinessTypeSearchCriteria(searchDTO);
        IPage<PassengerSmsRecord> datas = passengerSmsRecordService.getPassengerSmsRecordPage(searchDTO);
        List<PassengerSmsRecordVO> result = ModelConverter.convertToPassengerSmsRecordVO(datas.getRecords());
        List<AirportInfo> airportInfos = airportInfoApi.getAll().getData();
        Map<String, AirportInfo> airportInfoMap = AirportInfoUtils.convertToMap(airportInfos);
        AirportInfoUtils.populateAirportInfo(result, airportInfoMap);
        return PagedResult.ok(result, datas.getTotal(), datas.getPages(), datas.getSize(), datas.getCurrent());
    }

    @RequestMapping("/statics")
    @ApiOperation("获取满足指定的条件的短信发送状态统计")
    public BaseResult<MessageStatusStaticsVO> getMessageStatusStatics(@RequestBody
            PassengerSmsSearchDTO searchDTO){
        transferBusinessTypeSearchCriteria(searchDTO);
        List<MessageStatusStaticsDTO> staticsDTO = passengerSmsRecordService.getMessagesStatusStatics(searchDTO);

        MessageStatusStaticsVO messageStatusStaticsVO = new MessageStatusStaticsVO();
        if (staticsDTO != null && !staticsDTO.isEmpty()){
            for(MessageStatusStaticsDTO dto : staticsDTO){
                messageStatusStaticsVO.addTotalCount(dto.getRecordCount());

                if (SendStatusEnum.SUCCESS.equals(dto.getStatus())){
                    messageStatusStaticsVO.addSuccessCount(dto.getRecordCount());
                }
            }
        }

        return BaseResult.ok(messageStatusStaticsVO);
    }

    @GetMapping("/detail")
    @ApiOperation("获取指定记录的详情")
    public BaseResult<PassengerSmsRecordVO> getDetail(@RequestParam(name="id", required = true) Long id){
        PassengerSmsRecord record = passengerSmsRecordService.getById(id);

        PassengerSmsRecordVO vo = ModelConverter.convertToPassengerSmsRecordVO(record);
        List<AirportInfo> airportInfos = airportInfoApi.getAll().getData();
        Map<String, AirportInfo> airportInfoMap = AirportInfoUtils.convertToMap(airportInfos);
        AirportInfoUtils.populateAirportInfo(vo, airportInfoMap);
        return BaseResult.ok(vo);
    }

    //按照查询条件下载
    @PostMapping("/download")
    @ApiOperation("根据查询条件下载指定的旅客短信发送记录")
    public void download(@RequestBody PassengerSmsSearchDTO searchDto, HttpServletRequest request, HttpServletResponse response){
        transferBusinessTypeSearchCriteria(searchDto);
        passengerSmsRecordService.download(searchDto, request, response);
    }

    //按照指定的id进行下载
    @PostMapping("/downloadById")
    @ApiOperation("下载通过id指定的旅客短信发送记录")
    public void downloadById(@RequestBody List<Long> ids, HttpServletRequest request, HttpServletResponse response){
        passengerSmsRecordService.downLoad(ids, request,response);
    }

    private void transferBusinessTypeSearchCriteria(PassengerSmsSearchDTO dto){
        if (dto == null || dto.getBusinessTypes() == null){
            return;
        }

        List<String> transferedBusinessType = searchCriteriaUtils.getSmsBusinessTypeName(dto.getBusinessTypes());
        dto.setBusinessTypes(transferedBusinessType);
    }

    @PostMapping("/send_assistant_sms")
    @ApiOperation("给旅客发送辅助类短信")
    public BaseResult<Object> sendAssistantSms(@RequestBody AssistantSmsSendDTO dto){
        assistantSmsService.sendSmsViaCustomerChannel(dto);
        return BaseResult.ok();
    }
}
