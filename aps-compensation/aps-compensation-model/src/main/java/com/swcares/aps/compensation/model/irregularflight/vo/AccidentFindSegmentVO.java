package com.swcares.aps.compensation.model.irregularflight.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.aps.compensating.mode.irregularflight.vo.AccidentFindSegmentVO <br>
 * Description：新建事故单航段返回对象 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021/10/28 15:53 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="CompensationPaxInfoVO对象", description="")
public class AccidentFindSegmentVO {

    @ApiModelProperty(value = "出发航站中文拼接")
    private String departPortCH;

    @ApiModelProperty(value = "到达航站中文拼接")
    private String arrivalPortCH;

    @ApiModelProperty(value = "出发航站")
    private String departPort;

    @ApiModelProperty(value = "到达航站")
    private String arrivalPort;
}
