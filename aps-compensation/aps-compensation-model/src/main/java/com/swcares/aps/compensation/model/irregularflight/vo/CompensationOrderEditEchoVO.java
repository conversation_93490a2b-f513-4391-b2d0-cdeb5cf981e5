package com.swcares.aps.compensation.model.irregularflight.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.aps.compensating.mode.irregularflight.vo <br>
 * Description：赔偿单编辑回显-赔偿单信息 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 11月11日 9:55 <br>
 * @version v1.0 <br>
 */
@Data
public class CompensationOrderEditEchoVO {

    @ApiModelProperty(value = "赔偿单id")
    private String id;

    @ApiModelProperty(value = "赔偿单单号")
    private String orderNo;

    @ApiModelProperty(value = "服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "所选航段中文")
    private String choiceSegmentCh;

    @ApiModelProperty(value = "保障服务（0不存在，1存在） ")
    private String ensureType;

    @ApiModelProperty(value = "补偿方式 1现金，2虚拟，3实物 - 取字典表")
    private String compensateType;

    @ApiModelProperty(value = "备注")
    private String remark;

    private String compensateSubType;


    @ApiModelProperty(value = "已选旅客数量")
    private String choiceNum;

    @ApiModelProperty(value = "成人数量")
    private String adultNum;

    @ApiModelProperty(value = "儿童数量")
    private String childrenNum;

    @ApiModelProperty(value = "婴儿数量")
    private String babyNum;

    @ApiModelProperty(value = "补偿标准")
    private String compensateStandard;
}
