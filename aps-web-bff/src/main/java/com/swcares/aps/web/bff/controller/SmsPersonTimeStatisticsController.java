package com.swcares.aps.web.bff.controller;

import com.swcares.aps.msg.api.SentSmsReportApi;
import com.swcares.aps.msg.model.dto.SmsPersonTimeStatisticsSearchDTO;
import com.swcares.aps.msg.model.vo.SmsPersonTimeStatisticsResultVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.PagedResult;
import feign.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Collection;
import java.util.List;

/**
 * @ClassName：SmsPersonTimeStatisticsController
 * @Description：短信人次统计模块
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/11/25 10:24
 * @version： v1.0
 */
@RestController
@RequestMapping("/sms/person/report")
@Api(tags = "短信统计报表接口")
@ApiVersion({"SmsTask Api v1.0"})
@Slf4j
public class SmsPersonTimeStatisticsController {

    @Autowired
    SentSmsReportApi sentSmsReportApi;

    //列表查询
    @PostMapping("/statics")
    @ApiOperation("短信人次统计列表")
    public PagedResult<List<SmsPersonTimeStatisticsResultVO>> getSmsPersonTimeStatistics(@RequestBody SmsPersonTimeStatisticsSearchDTO searchDTO){

       return sentSmsReportApi.getSmsPersonTimeStatistics(searchDTO);
    }
    //按照查询条件下载
    @PostMapping("/download")
    @ApiOperation("根据查询条件下载指定短信人次统计")
    public void download(@RequestBody SmsPersonTimeStatisticsSearchDTO searchDto, HttpServletRequest request, HttpServletResponse response){
        Response feignResponse =  sentSmsReportApi.personTimeDownload(searchDto);
        downloadByFeign(response, feignResponse);
    }

    private void downloadByFeign(HttpServletResponse response, Response feignResponse) {
        response.setContentType("application/x-download;charset=UTF8");
        Collection<String> contentDispositionHeader =
                feignResponse.headers().get("Content-Disposition");
        response.setHeader("Content-Disposition", contentDispositionHeader.isEmpty() ?
                "" :
                contentDispositionHeader.iterator().next());

        Response.Body feignBody = feignResponse.body();
        try (OutputStream outputStream = response.getOutputStream();
             InputStream inputStream = feignBody.asInputStream()) {
            IOUtils.copy(inputStream, outputStream);
            outputStream.flush();
        } catch (IOException e) {
            log.error("[短信报表模块]根据查询条件下载指定短信人次统计-出现异常", e);
        }
    }
}
