package com.swcares.aps.web.bff.controller.ground;

import com.swcares.aps.ground.AssuranceFltPaxQueryApi;
import com.swcares.aps.ground.models.assurance.dto.AssuranceFlightInfoDTO;
import com.swcares.aps.ground.models.assurance.dto.PaxQueryDTO;
import com.swcares.aps.ground.models.assurance.vo.AssurancePassengerVO;
import com.swcares.aps.ground.models.assurance.vo.ProtectFltInfoVO;
import com.swcares.aps.ground.models.passengerCatefory.PassengerCategoryConfigureDepository;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/assurance/fltpax/query")
@Api(tags = {"航班&旅客信息查询接口"})
@ApiVersion({"保障单相关Api"})
public class AssuranceFltPaxQueryController extends BaseController {


    @Autowired
    AssuranceFltPaxQueryApi assuranceFltPaxQueryApi;

    @GetMapping("/getFltInfo")
    @ApiOperation(value = "通过航班号航班日期查询航段和服务航站")
    public BaseResult<ProtectFltInfoVO> getFltInfo(@ApiParam(value = "航班号", required = true) String flightNo,
                                                   @ApiParam(value = "航班日期", required = true) String flightDate) {
        return assuranceFltPaxQueryApi.getFltInfo(flightNo, flightDate);
    }

    @PostMapping("/queryPax")
    @ApiOperation(value = "选择查询旅客保障对象")
    public BaseResult<List<AssurancePassengerVO>> queryPax(@RequestBody @Valid PaxQueryDTO dto) {
        return assuranceFltPaxQueryApi.queryPax(dto);
    }

    @PostMapping("/getFlightUnitInfo")
    @ApiOperation(value = "选择查询机组保障对象")
    public BaseResult<Object> getFlightUnitInfo(@RequestBody @Valid List<AssuranceFlightInfoDTO> flightInfoDTO){
        return assuranceFltPaxQueryApi.getFlightUnitInfo(flightInfoDTO);
    }
    @GetMapping("/getPassengerCategoryInfo")
    @ApiOperation(value = "旅客类别获取接口")
    public BaseResult<List<PassengerCategoryConfigureDepository>> getPassengerCategoryInfo(){
        return assuranceFltPaxQueryApi.getPassengerCategoryInfo();
    }
}