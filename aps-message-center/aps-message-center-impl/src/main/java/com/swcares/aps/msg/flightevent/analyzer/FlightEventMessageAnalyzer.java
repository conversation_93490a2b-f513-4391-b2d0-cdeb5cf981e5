/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：FlightEventMessageAnalyzer <br>
 * Package：com.swcares.reaptv.msg.flightevent.analyzer <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.flightevent.analyzer;

import com.swcares.aps.msg.config.MessageCenterErrors;
import com.swcares.aps.msg.flightevent.deserializer.FlightEventDeserializer;
import com.swcares.aps.msg.flightevent.deserializer.FlightEventMessageWrapper;
import com.swcares.aps.msg.flightevent.deserializer.PassengerInfo;
import com.swcares.aps.msg.flightevent.deserializer.SsmInfo;
import com.swcares.aps.msg.model.entity.FlightEvent;
import com.swcares.aps.msg.model.entity.FlightEventPassenger;
import com.swcares.aps.msg.model.entity.FlightEventSourceMessage;
import com.swcares.aps.msg.model.enums.FlightEventEnum;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.lang.DateUtils;
import com.swcares.baseframe.utils.lang.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.reaptv.msg.flightevent.analyzer.FlightEventMessageAnalyzer <br>
 * Description：TODO：(这里用一句话描述这个类的作用) <br>
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/5 22:03 <br>
 * @version:
 */
@Slf4j
public class FlightEventMessageAnalyzer {

    private static int airport_code_length = 3;
    private static DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HHmm");
    private static DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static String ADJUST_TYPE_CANCEL = "取消调整";
    private static String BACKUP_FLIGHT_NOT_CONFIRMED = "补班待定";
    private static String BUSINESS_CABIN_FLAG = "C";    //公务舱标识
    private static String TOURIST_CABIN_FLAG = "Y";     //经济舱标识
    
    
    public static FlightEventAnalyzeResult analyzeFlightEvent(FlightEventSourceMessage flightEventMessage) throws ParseException {
        if (flightEventMessage == null){
            throw new IllegalArgumentException("航变事件原始消息不能为空");
        }
        FlightEventMessageWrapper flightEventMessageWrapper = FlightEventDeserializer.deserialize(flightEventMessage.getContent());
        if (flightEventMessageWrapper == null || flightEventMessageWrapper.getDatas() == null){
            log.error("解析处理的航变事件为空，请检查航变事件消息是否正确。");
            throw new BusinessException(MessageCenterErrors.FLIGHT_EVENT_SOURCE_MESSAGE_CONTENT_WRONG,
                    flightEventMessage.getId(), "解析出来的航变事件为空");
        }


        FlightEventAnalyzeResult result = new FlightEventAnalyzeResult();
        FlightEvent flightEvent = createFlightEvent(flightEventMessage.getId(), flightEventMessageWrapper);
        result.setFlightEvent(flightEvent);
        
        List<String> flightDates = getAvaliableFlightDates(flightEventMessageWrapper);
        result.setFlightDates(flightDates);
        
        List<FlightEventPassenger> passengers = createFlightEventPassengers(flightEvent, flightEventMessageWrapper);
        result.setFlightEventPassengers(passengers);
        flightEvent.setPassengerCount(passengers.size());
        
        return result;
    }

    private static List<String> getAvaliableFlightDates(FlightEventMessageWrapper flightEventMessageWrapper) {
        Map<String, List<PassengerInfo>> passengerMap = flightEventMessageWrapper.getDatas().getPassengerResultDetail();
        List<String> result = new ArrayList<>();
        for(String flightDate : passengerMap.keySet()){
            result.add(flightDate);
        }
        
        return result;
    }

    private static FlightEvent createFlightEvent(long messageId, FlightEventMessageWrapper flightEventMessageWrapper){

        SsmInfo ssmInfo = flightEventMessageWrapper.getDatas().getSsmInfo();
        if (ssmInfo == null){
            throw new BusinessException(MessageCenterErrors.FLIGHT_EVENT_SOURCE_MESSAGE_CONTENT_WRONG,
                    messageId, "没有ssmInfo");
        }
        
        FlightEvent flightEvent = new FlightEvent();
        flightEvent.setMessageId(messageId);
        flightEvent.setSsmid(ssmInfo.getSsmId());
        flightEvent.setAdjustType(getFlightEventEnumByProcessType(ssmInfo.getProcessType()));
        flightEvent.setProcessType(ssmInfo.getProcessType());
        flightEvent.setAdjustReason(ssmInfo.getAdjustReason());
        flightEvent.setRemark(ssmInfo.getRemark());
        flightEvent.setForecastNumber(ssmInfo.getForecastNo());
        flightEvent.setForecastType(ssmInfo.getForecastType());
        flightEvent.setFlightNumber(ssmInfo.getFlightNumber());
        flightEvent.setOrg(ssmInfo.getSegment().substring(0,airport_code_length));
        flightEvent.setDest(ssmInfo.getSegment().substring(airport_code_length));
        flightEvent.setCancelLastSeg(toBoolenValue(ssmInfo.getCancelLastSeg()));
        flightEvent.setTransitAirport(ssmInfo.getTransitPoint());
        if(StringUtils.isNotBlank(ssmInfo.getDepTime())) {
            //解析deptime，处理经停跨天情况下的depTime格式 （例如：0200+1）
            String[] depTimeInfo = ssmInfo.getDepTime().split("\\+");
            if (depTimeInfo.length > 1){
                flightEvent.setAcrossDay(true);
            }
            flightEvent.setDepTime(LocalTime.parse(depTimeInfo[0], timeFormatter));
        }
        if(StringUtils.isNotBlank(ssmInfo.getDesTime())) {
            //解析desTime，处理飞行时间跨天情况下的desTime格式（例如：0040+1)
            String[] desTimeInfo = ssmInfo.getDesTime().split("\\+");
            flightEvent.setArrTime(LocalTime.parse(desTimeInfo[0], timeFormatter));
        }
        flightEvent.setOrigDesTime(formatTimeString(ssmInfo.getOrigdesTime()));
        flightEvent.setOrigDepTime(formatTimeString(ssmInfo.getOrigdepTime()));
        flightEvent.setFrequency(ssmInfo.getFrequency());
        flightEvent.setBound(toBoolenValue(ssmInfo.getIsBound()));
        flightEvent.setPlaneType(ssmInfo.getPlaneType());
        flightEvent.setNewPlaneType(ssmInfo.getNewplaneType());
        flightEvent.setLayout(ssmInfo.getNewplaneType());
        flightEvent.setNewLayout(ssmInfo.getNewLayout());
        flightEvent.setStartDate(LocalDate.parse(ssmInfo.getStartDate(),dateFormatter));
        flightEvent.setEndDate(LocalDate.parse(ssmInfo.getEndDate(), dateFormatter));
        flightEvent.setImportTime(LocalDateTime.parse(ssmInfo.getImportTime(), dateTimeFormatter));
        
        return flightEvent;
    }

    private static String formatTimeString(String timeString){
        if (StringUtils.isBlank(timeString)){
            return timeString;
        }

        if (timeString.length() < 3){
            return timeString;
        }

        return timeString.substring(0,2) + ":" + timeString.substring(2);
    }

    private static FlightEventEnum getFlightEventEnumByProcessType(String processType){
        if (processType == null){
            return null;
        }

        FlightEventEnum flightEvent = null;
        switch (processType){
            case "TIM":
                flightEvent = FlightEventEnum.TIME_ADJUST;
                break;
            case "EQT":
                flightEvent = FlightEventEnum.PLANE_CHANGE;
                break;
            case "RPL":
            case "CNL":
                flightEvent = FlightEventEnum.CANCEL;
                break;
            case "NEW":
                flightEvent = FlightEventEnum.CANCEL_RECOVER;
                break;
        }

        return flightEvent;
    }
    
    private static List<FlightEventPassenger> createFlightEventPassengers(FlightEvent flightEvent, FlightEventMessageWrapper flightEventMessageWrapper) throws ParseException {
        Map<String, List<PassengerInfo>> passengerMap = flightEventMessageWrapper.getDatas().getPassengerResultDetail();
        if (passengerMap == null || passengerMap.isEmpty()){
            throw new BusinessException(MessageCenterErrors.FLIGHT_EVENT_SOURCE_MESSAGE_CONTENT_WRONG,
                    flightEvent.getMessageId(), "没有旅客信息");
        }
        
        List<FlightEventPassenger> result = new ArrayList<>();
        
        for(String key : passengerMap.keySet()){
            LocalDate flightDate = LocalDate.parse(key, dateFormatter);
            
            List<PassengerInfo> passengerInfos = passengerMap.get(key);
            
            for(PassengerInfo passengerInfo : passengerInfos){
                if (passengerInfo.getETinfo() == null) {
                    FlightEventPassenger flightEventPassenger = generateFlightEventPassenger(flightEvent, flightDate, passengerInfo, null);
                    result.add(flightEventPassenger);
                } else {
                    String[] ticketNumbers = passengerInfo.getETinfo().split(",");
                    for(String ticketNumber : ticketNumbers){
                        FlightEventPassenger flightEventPassenger = generateFlightEventPassenger(flightEvent, flightDate, passengerInfo, ticketNumber);
                        result.add(flightEventPassenger);
                    }
                }
            }
        }
        
        return result;
    }

    private static FlightEventPassenger generateFlightEventPassenger(FlightEvent flightEvent, LocalDate flightDate, PassengerInfo passengerInfo, String ticketNumber) throws ParseException {
        FlightEventPassenger flightEventPassenger = new FlightEventPassenger();
        flightEventPassenger.setPnrCode(passengerInfo.getPnrCode());
        flightEventPassenger.setPassengerName(passengerInfo.getPassengerName());
        flightEventPassenger.setSequenceNumber(passengerInfo.getSequenceNumber());
        flightEventPassenger.setFlightNumber(flightEvent.getFlightNumber());
        flightEventPassenger.setOrg(flightEvent.getOrg());
        flightEventPassenger.setDest(flightEvent.getDest());
        flightEventPassenger.setFlightDate(flightDate);
        if (flightEvent.getDepTime() != null) {
            flightEventPassenger.setDepTime(LocalDateTime.of(flightDate, flightEvent.getDepTime()));
        }
        if (flightEvent.getArrTime() != null) {
            if (flightEvent.isFlightOverNight()) {
                flightEventPassenger.setArrTime(LocalDateTime.of(flightDate.plusDays(1), flightEvent.getArrTime()));
            } else {
                flightEventPassenger.setArrTime(LocalDateTime.of(flightDate, flightEvent.getArrTime()));
            }
        }
        flightEventPassenger.setTicketNumber(ticketNumber);
        flightEventPassenger.setTelePhone(passengerInfo.getTelNumber());
        flightEventPassenger.setVip(toBoolenValue(passengerInfo.getIsVVIP()));
        flightEventPassenger.setGroupMember(toBoolenValue(passengerInfo.getIsGroup()));
        flightEventPassenger.setStandby(toBoolenValue(passengerInfo.getIsStandby()));
        flightEventPassenger.setCabinType(passengerInfo.getCabinType());
        flightEventPassenger.setNewCabinType(passengerInfo.getNewCabinType());
        flightEventPassenger.setTargetFlightNumber(passengerInfo.getTargetFlightNumber());
        flightEventPassenger.setTargetSegment(passengerInfo.getTargetSegment());
        flightEventPassenger.setTargetSegmentDate(parseDate(passengerInfo.getTargetSegmentDate()));
        flightEventPassenger.setTargetDepTime(parseTime(flightEventPassenger.getTargetSegmentDate(), passengerInfo.getTargetDepTime()));
        flightEventPassenger.setTargetArrTime(parseTime(flightEventPassenger.getTargetSegmentDate(), passengerInfo.getTargetDesTime()));
        adjustArrTimeIfNeed(flightEventPassenger);
        flightEventPassenger.setAdjustAdditionalInfo(getAdjustAdditionalInfo(flightEvent, passengerInfo));
        return flightEventPassenger;
    }

    private static Date parseDate(String dateString) throws ParseException {
        if (StringUtils.isBlank(dateString)){
            return null;
        }

        return DateUtils.parseDate(dateString, DateUtils.PTN_YMD);
    }

    private static void adjustArrTimeIfNeed(FlightEventPassenger flightEventPassenger){
        if (flightEventPassenger.getTargetDepTime() == null || flightEventPassenger.getTargetArrTime() == null){
            return;
        }

        if (flightEventPassenger.getTargetDepTime().before(flightEventPassenger.getTargetArrTime())){
            return;
        }

        flightEventPassenger.setTargetArrTime( DateUtils.addDays(flightEventPassenger.getTargetArrTime(), 1));
    }

    private static Date parseTime(Date flightDate, String timeString) throws ParseException {
        if(flightDate == null || StringUtils.isBlank(timeString)){
            return null;
        }

        String[] timeInfo = timeString.split("\\+");

        if (timeInfo.length > 1){
            flightDate = DateUtils.addDays(flightDate, Integer.parseInt(timeInfo[1]));
        }

        return DateUtils.parseDate(DateUtils.formatDate(flightDate, DateUtils.PTN_YMD) + " " + transferTimeFormat(timeInfo[0]), DateUtils.PTN_YMD_HMS);
    }

    /*
    将1100这样的时间格式转换为11:00:00这样的时间格式
     */
    private static String transferTimeFormat(String orgValue){
        String seperator = ":";
        if (StringUtils.isBlank(orgValue)){
            return  orgValue;
        }
        
        if (orgValue.indexOf(seperator) > 0){
            return orgValue;
        }
        
        //长度小于4，不是正常的时间格式
        if (orgValue.length() < 4){
            return orgValue;
        }
        
        return orgValue.substring(0,2) + seperator + orgValue.substring(2) + seperator + "00";
        
    }
    
    private static String getAdjustAdditionalInfo(FlightEvent flightEvent, PassengerInfo passengerInfo){
        StringBuilder adjustAdditionalInfo = new StringBuilder();
        adjustAdditionalInfo.append(translateAdjustType(flightEvent.getAdjustType()));
        if (isFlightCancel(flightEvent.getAdjustType())) {
            if (hasProtection(passengerInfo)) {
                adjustAdditionalInfo.append(",").append("有保护");
            } else {
                adjustAdditionalInfo.append(",").append("无保护");
            }
            if (isBackupFlightNotConfirmed(flightEvent.getRemark())){
                adjustAdditionalInfo.append(",").append("补班待定");
            }
        }
        if (isCabinTypeDegrade(passengerInfo.getCabinType(), passengerInfo.getNewCabinType())){
            adjustAdditionalInfo.append(",").append("有降舱");
        } else {
            adjustAdditionalInfo.append(",").append("无降舱");
        }
        
        return adjustAdditionalInfo.toString();
    }

    private static boolean hasProtection(PassengerInfo passengerInfo) {
        return StringUtils.isNotBlank(passengerInfo.getTargetFlightNumber());
    }

    private static boolean isCabinTypeDegrade(String oldCabinType, String newCabinType){
        if (StringUtils.isBlank(oldCabinType) || StringUtils.isBlank(newCabinType)){
            return false;
        }
        
        if (BUSINESS_CABIN_FLAG.equalsIgnoreCase(oldCabinType)
                && TOURIST_CABIN_FLAG.equalsIgnoreCase(newCabinType)) {
            return true;
        }
        
        return false;
    }
    
    private static boolean isFlightCancel(FlightEventEnum adjustType){
        return FlightEventEnum.CANCEL.equals(adjustType);
    }
    
    //是否是补班待定
    private static boolean isBackupFlightNotConfirmed(String remark){
        if (BACKUP_FLIGHT_NOT_CONFIRMED.equals(remark)){
            return true;
        } else {
            return false;
        }
    }
    
    //来自报文的调整类型和页面要显示的信息不太一致，做一下转换
    private static String translateAdjustType(FlightEventEnum adjustType){
        if (adjustType == null){
            return null;
        }

        return adjustType.getName();
    }
    
    private static boolean toBoolenValue(String value){
        if ("Y".equalsIgnoreCase(value)){
            return true;
        }
        
        return false;
    }
}
