package com.swcares.aps.compensation.impl.apply.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.compensation.impl.apply.service.PayRecordService;
import com.swcares.aps.compensation.model.apply.dto.PayRecordDTO;
import com.swcares.aps.compensation.model.apply.dto.PayRecordPagedDTO;
import com.swcares.aps.compensation.model.apply.entity.PayRecordDO;
import com.swcares.aps.compensation.model.apply.vo.PayRecordDOVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ClassName：com.swcares.compensation.controller.PayRecordController <br>
 * Description：航延补偿支付记录表 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-11-25 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/apply/payRecord")
@Api(tags = "航延补偿支付记录表接口")
@ApiVersion(value = "申领单接口 v1.0")
public class PayRecordController extends BaseController {

    @Autowired
    private PayRecordService payRecordService;

    @PostMapping("/save")
    @ApiOperation(value = "新建航延补偿支付记录表记录")
    public BaseResult<Object> save(@RequestBody PayRecordDTO dto) {
        PayRecordDO payRecordDO = ObjectUtils.copyBean(dto, PayRecordDO.class);
        boolean created = payRecordService.save(payRecordDO);
        if (!created) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        return ok();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "通过ID删除航延补偿支付记录表记录")
    public BaseResult<Object> delete(@ApiParam(value = "主键id", required = true) Long id) {
        boolean deleted = payRecordService.logicRemoveById(id);
        if (!deleted) {
            throw new BusinessException(CommonErrors.DELETE_ERROR);
        }
        return ok();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改航延补偿支付记录表记录")
    public BaseResult<Object> update(@RequestBody PayRecordDTO  dto) {
        PayRecordDO payRecordDO = ObjectUtils.copyBean(dto, PayRecordDO.class);
        boolean updated = payRecordService.updateById(payRecordDO);
        if (!updated) {
            throw new BusinessException(CommonErrors.UPDATE_ERROR);
        }
        return ok();
    }

    @GetMapping("/get")
    @ApiOperation(value = "通过ID查询航延补偿支付记录表记录")
    public BaseResult<PayRecordDOVO> get(@ApiParam(value = "主键id", required = true) Long id) {
        PayRecordDO payRecordDO = payRecordService.getById(id);
        PayRecordDOVO payRecordVO = ObjectUtils.copyBean(payRecordDO, PayRecordDOVO.class);
        return ok(payRecordVO);
    }

    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询航延补偿支付记录表记录")
    public PagedResult<List<PayRecordDOVO>> page(@RequestBody PayRecordPagedDTO dto) {
        IPage<PayRecordDOVO> result = payRecordService.page(dto);
        return ok(result);
    }
}
