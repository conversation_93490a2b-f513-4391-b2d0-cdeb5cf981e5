package com.swcares.aps.compensation.impl.apply.workflow.process;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.compensation.impl.apply.constants.ApplyConstants;
import com.swcares.aps.compensation.impl.apply.constants.ApplyErrors;
import com.swcares.aps.compensation.impl.apply.enums.ApplyWorkflowNodeBusiTypeEnum;
import com.swcares.aps.compensation.impl.apply.workflow.ApplyWorkflowService;
import com.swcares.aps.component.workflow.NodeNoticeProcess;
import com.swcares.aps.component.workflow.dto.NodeExtVarsDTO;
import com.swcares.aps.component.workflow.dto.NodeNoticeDTO;
import com.swcares.aps.component.workflow.dto.NodeNoticeProcessResult;
import com.swcares.aps.component.workflow.enums.ApsProjectEnum;
import com.swcares.baseframe.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * ClassName：CommonNodeProcess <br>
 * Description：申领单自动审核通知节点处理;直接修改cpc当前节点状态为complete<br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/1/17 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class ApplyAutomaticNodeProcess implements NodeNoticeProcess {

    @Autowired
    private ApplyWorkflowService applyWorkflowService;

    @Override
    public NodeNoticeProcessResult process(NodeNoticeDTO noticeDTO) {
        try {
        applyWorkflowService.automaticWorkflowProcess(noticeDTO);
        }catch (Exception e){
            e.printStackTrace();
            log.error("【aps-apply-impl】自动审核申领单流程出错，noticeDTO:{"+JSONUtil.toJsonStr(noticeDTO)+"}",e);
            throw new BusinessException(ApplyErrors.AUDIT_ERROR,"自动审核异常");
        }
        return new NodeNoticeProcessResult();
    }

    @Override
    public boolean canProcess(NodeNoticeDTO noticeDTO) {
        boolean result=false;

        NodeExtVarsDTO extVarsDTO = noticeDTO.getExtVars();
        if(StringUtils.equalsIgnoreCase(extVarsDTO.getProject(), ApsProjectEnum.APPLY_IMPL.getProjectType())
                && StringUtils.equalsIgnoreCase(extVarsDTO.getBusiness(), ApplyConstants.APPLY_WORKFLOW_BUSINESS)
                && StringUtils.equalsIgnoreCase(noticeDTO.getNodeBusinessType(), ApplyWorkflowNodeBusiTypeEnum.AUTOMATIC.getType())){
            result=true;
        }

        return result;
    }

}
