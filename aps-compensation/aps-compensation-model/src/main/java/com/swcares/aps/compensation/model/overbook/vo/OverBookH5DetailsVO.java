package com.swcares.aps.compensation.model.overbook.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：OverBookH5DetailsVO
 * @Description：H5超售事故单详情-超售信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/27 15:31
 * @version： v1.0
 */
@Data
public class OverBookH5DetailsVO {
    @ApiModelProperty(value = "事故单信息")
    OverBookAccidentDetailsVO accidentDetailsVO;
    @ApiModelProperty(value = "旅客信息")
    OverBookBasicPaxVO paxVO;
    @ApiModelProperty(value = "关联补偿单信息集合")
    List<OverBookH5CompensationDetailsVO> compensationDetailsVOs;



}
