<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.msg.mapper.TempleteMapper">
    <select id="page" resultType="com.swcares.aps.msg.model.vo.TempleteVO">
        select
        t.ID,
        t.TEMPLETE_NAME,
        t.TEMPLETE_TYPE,BUSINESS_TYPE,
        t.SMSTEMPLETE_COUNT_TYPE,
        d.item_value as SMSTEMPLETE_COUNT_TYPE_NAME,
        t.TEMPLETE_NUMBER,
        t.TEMPLETE_CONTENT,
        t.STATE,
        t.REMARKS,
        t.CREATED_ORG,
        t.CREATED_BY,
        t.CREATED_TIME,
        t.UPDATED_BY,
        t.UPDATED_TIME,
        t.USED_SERVICE
        from MSG_SMS_TEMPLETE t
        left join SYS_DICTIONARY_DATA d ON t.SMSTEMPLETE_COUNT_TYPE = d.dict_item and d.dict_type = 'smstemplete_count_type'
        <where>
            <if test="dto.templeteName != null ">
                AND t.templete_name like concat(concat('%', #{dto.templeteName}),'%')
            </if>
            <if test="dto.templeteType != null ">
                AND t.templete_type = #{dto.templeteType}
            </if>
            <if test="dto.businessType != null ">
                AND t.business_type = #{dto.businessType}
            </if>
            <if test="dto.smstempleteCountType != null ">
                AND t.smstemplete_count_type = #{dto.smstempleteCountType}
            </if>
            <if test="dto.templeteNumber != null ">
                AND t.templete_number = #{dto.templeteNumber}
            </if>
            <if test="dto.stCreateDate != null ">
                AND t.created_time between TO_DATE(#{dto.stCreateDate}, 'yyyy-mm-dd') and (TO_DATE(#{dto.endCreateDate}, 'yyyy-mm-dd') + 1)
            </if>
            <if test="dto.state != null ">
                AND t.state = #{dto.state}
            </if>
            <if test="dto.belongToOrg != null and dto.belongToOrg.size() > 0">
                AND t.created_org in
                <foreach collection="dto.belongToOrg" item="orgId" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
            </if>
        </where>
        order by t.created_time desc
    </select>
</mapper>
