package com.swcares.aps.compensation.model.irregularflight.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CompensationAuditReviewerVO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/5/12 12:14
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationAuditRecordVO对象", description="赔偿单审核记录各节点审核人&待审核人信息")
public class CompensationAuditReviewerVO {
    @ApiModelProperty(value = "操作人id")
    private Long reviewerId;
    @ApiModelProperty(value = "操作人姓名")
    private String reviewerName;
    @ApiModelProperty(value = "操作人工号")
    private String reviewerNo;
    @ApiModelProperty(value = "操作人联系方式")
    private String reviewerPhone;
    @ApiModelProperty(value = "操作人用户名")
    private String userName;
    @ApiModelProperty(value = "操作人所在机构名称")
    private String orgName;
    @ApiModelProperty(value = "操作人所在机构id")
    private Long orgId;
    @ApiModelProperty("员工ID")
    private String employeeId;
}
