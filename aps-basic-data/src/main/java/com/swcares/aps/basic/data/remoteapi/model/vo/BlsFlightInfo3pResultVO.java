package com.swcares.aps.basic.data.remoteapi.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName：BlsFlightInfo3pResultVO
 * @Description：对账通-重新航班数据返回的data
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2025/3/19 15:08
 * @version： v1.0
 */
@Data
public class BlsFlightInfo3pResultVO implements Serializable {

    private Boolean hasMore;

    private List<FlightInfo3pResultItemVO> dataList;

}
