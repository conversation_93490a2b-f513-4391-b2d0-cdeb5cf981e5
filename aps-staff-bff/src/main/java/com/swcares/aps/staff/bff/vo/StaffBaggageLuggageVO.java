package com.swcares.aps.staff.bff.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：LuggageVO <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/3 <br>
 * @version v1.0 <br>
 */
@Data
public class StaffBaggageLuggageVO {

    @ApiModelProperty(value = "箱包id")
    private Long id;

    @ApiModelProperty(value = "箱包编号")
    private String luggageNo;

    @ApiModelProperty(value = "箱包品牌")
    private String brand;

    @ApiModelProperty(value = "箱包名称规格")
    private String luggageName;

    @ApiModelProperty(value = "箱包尺寸")
    private Long size;

    @ApiModelProperty(value = "所属航站")
    private String segment;

    @ApiModelProperty(value = "单价")
    private Long price;

    @ApiModelProperty(value = "剩余库存")
    private Long stock;
}
