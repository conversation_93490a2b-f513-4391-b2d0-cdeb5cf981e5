package com.swcares.aps.basic.data.businessimpl.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: FocFlightInfoVO
 * @projectName aps
 * @description:  临时航班数据表VO对象
 * @date 2021/11/2 13:21
 */
@Data
@ApiModel(value="FocFlightInfoVO对象", description="临时航班表")
public class FocFlightInfoVO {
    @ApiModelProperty(value = "FOC返回的航班ID 主键")
    private String flightId;

    @ApiModelProperty(value = "机号")
    private String acReg;

    @ApiModelProperty(value = "机型")
    private String acType;

    @ApiModelProperty(value = "无(文档中无此字段含义)")
    private String adjustType;

    @ApiModelProperty(value = "到达机场")
    private String arrivalPort;

    @ApiModelProperty(value = "实际到达时间")
    private String ata;

    @ApiModelProperty(value = "实际起飞时间")
    private String atd;

    @ApiModelProperty(value = "机位")
    private String bay;

    @ApiModelProperty(value = "订座数")
    private String bookseat;

    @ApiModelProperty(value = "撤轮档时间")
    private String cldTime;

    @ApiModelProperty(value = "cobt时间")
    private String cobt;

    @ApiModelProperty(value = "取消")
    private String csAbnormalReason;

    @ApiModelProperty(value = "延误")
    private String delayAbnormalReason;

    @ApiModelProperty(value = "起飞机场")
    private String departPort;

    @ApiModelProperty(value = "预计到达时间")
    private String eta;

    @ApiModelProperty(value = "预计起飞时间")
    private String etd;

    @ApiModelProperty(value = "取消标志")
    private String flgCs;

    @ApiModelProperty(value = "延误标识")
    private String flgDelay;

    @ApiModelProperty(value = "备降返航")
    private String flgVr;

    @ApiModelProperty(value = "备降返航标识")
    private String flgVr1;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班类型")
    private String flightType;

    @ApiModelProperty(value = "到达机场")
    private String poa;

    @ApiModelProperty(value = "起飞机场")
    private String pod;

    @ApiModelProperty(value = "备降")
    private String rAbnormalReason;

    @ApiModelProperty(value = "总座数")
    private String seatNum;

    @ApiModelProperty(value = "计划到达时间")
    private String sta;

    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    @ApiModelProperty(value = "更新时间")
    private String updateTime;

    @ApiModelProperty(value = "返航")
    private String vAbnormalReason;

    @ApiModelProperty(value = "逻辑航段")
    private Boolean logic;

    @ApiModelProperty(value = "D国内I国际")
    private String dOrI;

    @ApiModelProperty(value = "起始航站三字码")
    private String org;

    @ApiModelProperty(value = "到达航站三字码")
    private String desc;

    @ApiModelProperty(value = "航段三字码")
    private String segment;

    @ApiModelProperty(value = "离港机位")
    private String bayOutGoing;

    @ApiModelProperty(value = "不正常对内原因")
    private String innerReason;

    @ApiModelProperty(value = "不正常对外原因")
    private String exterReason;
}
