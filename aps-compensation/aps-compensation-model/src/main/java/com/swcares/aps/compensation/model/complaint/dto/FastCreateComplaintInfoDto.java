package com.swcares.aps.compensation.model.complaint.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(value = "FastCreateComplaintInfoDto对象", description = "快速创建事故单和补偿单表单")
public class FastCreateComplaintInfoDto {

    @ApiModelProperty(value = "事故单投诉旅客信息")
    @NotNull(message = "投诉旅客信息不能为空")
    @Valid
    List<PassengerComplaintDto> passengerComplaintInfoList;

    @ApiModelProperty(value = "投诉旅客选择信息")
    @NotNull(message = "投诉旅客选择信息不能为空")
    @Valid
    PassengerSelectInfoDto passengerSelectInfo;

    @ApiModelProperty(value = "填写事故单信息")
    @NotNull(message = "填写事故单信息不能为空")
    @Valid
    PassengerCompensationInfoDto passengerCompensationInfoDto;

    @ApiModelProperty(value = "填写补偿信息")
    @Valid
    ComplaintCompensationCreateDto complaintCompensationCreateDto;
}
