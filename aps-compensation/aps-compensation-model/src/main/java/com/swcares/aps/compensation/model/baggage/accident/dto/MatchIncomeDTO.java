package com.swcares.aps.compensation.model.baggage.accident.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：MatchIncomeDTO
 * @Description： 匹配多/少收的规则封装对象
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/3/8 15:55
 * @version： v1.0
 */
@Data
@ApiModel(value="MatchIncomeDTO对象", description="匹配多/少收的规则封装对象")
public class MatchIncomeDTO {

    @ApiModelProperty(value = "行李号")
    private String baggageNo;

    @ApiModelProperty(value = "异常行李类型（21破损,22少收,23多收,24内件缺少,25丢失）")
    private String type;

    @ApiModelProperty(value = "服务航站")
    private String serveSegment;

    @ApiModelProperty(value = "旅客航班号")
    private String flightNo;

    @ApiModelProperty(value = "旅客航班日期")
    private String flightDate;

}
