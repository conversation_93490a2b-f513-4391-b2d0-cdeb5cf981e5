package com.swcares.aps.compensation.impl.apply.constants;

/**
 * ClassName：com.swcares.aps.apply.impl.constants.ApplyConstants <br>
 * Description：申领单常量类 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021/11/26 9:46 <br>
 * @version v1.0 <br>
 */
public final class ApplyConstants {
    //申领单手机验证redis key 前缀
    public static String AUTH_PHONE_PREFIX = "APPLY:PHONE_VERIFICATION_SMS";
    //申领单手机验证redis过期时间
    public static Long AUTH_PHONE_PREFIX_PAST_DUE = 300L;
    //冻结状态
    public static String AUTH_PAX_SWITCH_OFF = "1";
    //未领取状态
    public static String AUTH_PAX_RECEIVE_STATUS = "0";//"227";
    //已领取状态
    public static String ALREADY_RECEIVE_STATUS = "1";//"228";
    //领取中状态
    public static String GET_RECEIVE_STATUS = "2";//"229";
    //赔偿单状态生效
    public static String TAKE_EFFECT = "4";//"218";
    //商务舱标识
    public static String BUSINESS_CLASS = "1";
    //申领单保存时的redis分布式锁过期时间
    public static Long APPLY_SAVE_REDIS_LOCK_TIME = 120L;
    //不正常航班
    public static String IRREGULAR_FLIGHT = "1";//"222";
    //异常行李
    public static String ABNORMAL_LUGGAGE = "2";//"223";
    //超售
    public static String OVER_BOOKING = "3";//"224";
    // 旅客投诉
    public static String COMPLAINT = "4";//"224";
    //是否本航班旅客
    public static String SAME_FLIGHT = "1";
    //申领单保存是默认为领取中
    public static Long APPLY_PAX_STATUS_OPERATION = 1L;//257L;
    //协助领取的申领单保存默认为领取成功
    public static Long APPLY_PAX_STATUS_SUCCESS = 2L;//258L;
    //申领单领取失败
    public static Long APPLY_PAX_STATUS_FAIL = 3L;//259L;

    //申领失败审核拒绝原由
    public static String APPLY_PAX_STATUS_FAIL_ERROR_AUDIT="申领单审核被拒绝";

    //工作流业务申领单业务类型
    public static String APPLY_WORKFLOW_BUSINESS="apply";

    public static String APPLY_AUDIT_REJECT="0";
    public static String APPLY_AUDIT_APPROVED="1";


    //支付状态 - 0-未支付
    public static final String PAY_STATUS_UNPAID = "0";
    //支付状态 - 1-已支付
    public static final String PAY_STATUS_PAID = "1";
    //支付状态 - 2-支付失败
    public static final String PAY_STATUS_FAIL = "2";
    //支付状态 - 3-支付处理中
    public static final String PAY_STATUS_INPROCESS = "3";
    //快速支付
    public static final String QUICK_PAY="1";
    //等待期满-快速支付
    public static final String QUICK_PAY_EXPIRES="2";

//    申领方式：S本人领取、R代领领取、H员工协助
    public static final String APPLY_TYPE_S="S";
    public static final String APPLY_TYPE_R="R";
    public static final String APPLY_TYPE_H="H";

    /** 短信验证码验证成功或图形验证码验证成功后，token存入redis，避免越过短信或图形验证码验证步骤 */
    public static String AUTH_TOKEN_CAPTCHA_PASS_PREFIX = "COMPENSATION:TOKEN_VERIFICATION_CAPTCHA_PASS:";

    //验证码验证通过标识
    public static String AUTH_TOKEN_CAPTCHA_CODE_PASS = "CAPTCHA_PASS";


    //银联实名认证前缀
    public static String APPLY_CHINAPAY_AUTH_PASS_PREFIX = "APPLY:APPLY_CHINAPAY_AUTH:";
    //银联实名认证通过标识
    public static String APPLY_CHINAPAY_AUTH_PASS = "APPLY_CHINAPAY_AUTH_PASS";

    /**  存入redis， 默认过去时间5分钟 */
    public static Long AUTH_PREFIX_PAST_DUE = 300L;
}
