<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.usercenter.impl.uc.mapper.ReaptvEmployeeMapper">

    <sql id="employeeBase">
        e.id,
   	 	e.name,
   	 	e.gender,
   	 	e.employee_code,
   	 	e.job_number,
   	 	e.dingtalk,
   	 	e.wx_openid,
        e.id_card,
   	 	e.job_title,
   	 	e.job_position,
   	 	e.phone,
   	 	e.birthday,
   	 	e.status,
   	 	e.graduate_from,
   	 	e.political_status,
   	 	e.email_address,
   	 	e.work_terminal,
   	 	e.belong_to_org,
        e.info_is_show,
    </sql>

    <update id="logicRemoveById">
        update uc_employee set deleted=1
        where  id = #{id}
    </update>

    <!-- 批量修改人员所属机构 -->
    <update id="batchChgOrg">
        UPDATE
        uc_employee
        SET belong_to_org = #{orgId}
        WHERE id IN
        <foreach collection="empIds" index="index" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>
    <!-- 根据人员工号、电话、邮箱、身份证号进行查找，ID不为空时排查自己 -->
    <select id="selectByPhoneOrEmailOrJobNumberOrIdCard" resultType="com.swcares.aps.usercenter.model.uc.entity.ReaptvEmployee">
        SELECT e.id, e.job_number, e.phone, e.email_address, e.id_card FROM uc_employee e
        <where>
            e.deleted != 1
            <if test="emp.id != null and emp.id > 0">
                AND e.id != #{emp.id}
            </if>
            AND (
            1 = 0
            <if test="emp.jobNumber != null and emp.jobNumber!= '' ">
                OR e.job_number = #{emp.jobNumber}
            </if>
            <if test="emp.phone != null and emp.phone!= '' ">
                OR e.phone = #{emp.phone}
            </if>
            <if test="emp.emailAddress != null and emp.emailAddress!= '' ">
                OR e.email_address = #{emp.emailAddress}
            </if>
            <if test="emp.idCard !=null and emp.idCard != ''">
                OR e.id_card = #{emp.idCard}
            </if>
            <if test="emp.wxOpenid !=null and emp.wxOpenid != ''">
                OR e.wx_openid = #{emp.wxOpenid}
            </if>
            )
        </where>
    </select>
    <!--一级机构-->
    <sql id="one_level_id">
        <if test="_DB_TYPE_ == 'mysql'">
            (with recursive org as(
            select
            id, parent_id, cast(id as char) one_level_id
            from
            uc_organization
            where
            (deleted is null or deleted = 0) and PARENT_ID is null
            union
            select
            o.id, o.parent_id , (case when org.parent_id is null then o.id  else org.one_level_id end) one_level_id
            from
            uc_organization o join org on o.parent_id = org.id
            where
            (o.deleted is null or o.deleted = 0)
            )
            select * from org  where org.parent_id is not null)
        </if>
        <if test="_DB_TYPE_ == 'oracle'">
            (SELECT
            ID, connect_by_root id one_level_id
            FROM
            UC_ORGANIZATION
            WHERE
            deleted is null OR deleted = 0
            START WITH
            PARENT_ID =(
            SELECT id FROM
            UC_ORGANIZATION
            WHERE
            (deleted is null OR deleted = 0)
            AND PARENT_ID IS NULL )
            CONNECT BY PRIOR id = parent_id)
        </if>

    </sql>

    <!-- 查找管理范围的人员，flag=1排除已关联用户的人员（不包含用户已删除）， -->
    <select id="selectEmployeeByScopeOrg" resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvUserEmployeeVO">
        SELECT e.id, e.name,e.employee_code, e.job_number, e.dingtalk, e.wx_openid, o.name_full_path,
        e.belong_to_org as org_id,e.work_terminal,e.info_is_show
        org_level.one_level_id
        FROM uc_employee e
        LEFT JOIN uc_organization o ON o.id = e.belong_to_org
        left join <include refid="one_level_id"></include> org_level on org_level.id = e.belong_to_org
        WHERE 1=1 AND e.deleted != 1
        <if test="dto.status != null and dto.status != ''">
            AND e.status = #{dto.status}
        </if>
        <if test="dto.flag != null and dto.flag != 0">
            AND NOT EXISTS (SELECT 1 FROM uc_user u WHERE u.employee_id = e.id AND u.deleted != 1)
        </if>
        <choose>
            <when test="dto.mgtScopeOrgIds != null and dto.mgtScopeOrgIds.size() > 0">
                AND e.belong_to_org IN (
                <bind name="parent" value="dto.mgtScopeOrgIds" />
                <include refid="com.swcares.common.uc.mapper.OrganizationMapper.recursive_children"></include>
                )
            </when>
        </choose>
        <if test="dto.condition != null and dto.condition != ''">
            AND (e.name LIKE #{dto.condition} OR e.phone LIKE #{dto.condition} OR e.employee_code LIKE #{dto.condition})
        </if>
        <if test="dto.jobTitle != null and dto.jobTitle != ''">
            AND e.job_title = #{dto.jobTitle}
        </if>

        <if test="dto.jobPosition != null and dto.jobPosition != ''">
            AND e.job_position like #{dto.jobPosition}
        </if>
        ORDER BY e.id DESC
    </select>

    <select id="selectByIds" resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvUserEmployeeVO">
        SELECT e.id, e.name,e.employee_code, e.job_number, e.dingtalk, e.wx_openid, o.name_full_path,
        e.belong_to_org as org_id, e.work_terminal,o.name as organization_name,e.info_is_show
        FROM uc_employee e
        LEFT JOIN uc_organization o ON o.id = e.belong_to_org
        WHERE 1=1 AND e.deleted != 1 AND e.id IN
        <foreach collection="ids" index="index" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <!-- 分页列表查询方法 -->
    <select id="page" resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvEmployeeVO">
        SELECT
        <include refid="employeeBase" />
        e.created_by,
        e.created_time,
        e.updated_by,
        e.updated_time,
        e.photo_rul,
        uo.name as organization_name
        FROM uc_employee e
        LEFT JOIN uc_organization uo ON uo.id = e.belong_to_org
        WHERE e.deleted = 0
        <!-- 参数传的管理机构 -->
        <if test="p.mgtScopeOrgIds != null and p.mgtScopeOrgIds.size() > 0">
            AND e.belong_to_org IN (
            <bind name="parent" value="p.mgtScopeOrgIds" />
            <include refid="com.swcares.common.uc.mapper.OrganizationMapper.recursive_children"></include>
            )
        </if>
        <!-- 当前用户的管理机构 -->
        <if test="p.manageOrganizationId != null and p.manageOrganizationId.size() > 0">
            AND e.belong_to_org IN
            <foreach collection="p.manageOrganizationId" index="index" item="orgId" separator="," open="(" close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="p.workTerminal != null and p.workTerminal != ''">
            AND  INSTR(#{p.workTerminal}, e.work_terminal) > 0
        </if>
        <if test="p.status != null and p.status != ''">
            AND e.status = #{p.status}
        </if>
        <if test="p.condition != null and p.condition != ''">
            AND (e.name LIKE #{p.condition} OR e.job_number LIKE #{p.condition} OR e.phone = #{p.phone})
        </if>

        <if test="p.jobTitle != null and p.jobTitle != ''">
            AND e.job_title = #{p.jobTitle}
        </if>

        <if test="p.jobPosition != null and p.jobPosition != ''">
            AND e.job_position like #{p.jobPosition}
        </if>

        ORDER BY e.id DESC
    </select>

    <select id="getDingtalkEmployeeList" resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvEmployeeVO">
        select e.*
        from uc_employee e
        where e.deleted != 1
        and e.dingtalk is not null
    </select>

    <select id="selectEmployeeVOById" resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvEmployeeVO">
        SELECT
        <include refid="employeeBase" />
        org.name as organization_name
        FROM uc_employee e
        LEFT JOIN uc_organization org ON org.id = e.belong_to_org
        WHERE e.id IN
        <foreach collection="ids" index="index" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
    <sql id="employeeDetail">
        SELECT
        <include refid="employeeBase" />
        org.name as organization_name,
        u.name as user_name,
        u.id as user_id
        FROM uc_employee e
        LEFT JOIN uc_organization org ON org.id = e.belong_to_org
        LEFT JOIN uc_user u ON u.employee_id = e.id
    </sql>
    <select id="selectEmployeeDetail" resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvEmployeeDetailVO">
        <include refid="employeeDetail" />
        WHERE e.deleted != 1 AND e.id IN
        <foreach collection="ids" index="index" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectEmployeesByCondition" resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvEmployeeDetailVO">
        <include refid="employeeDetail" />
        <include refid="selectEmployeeWhere"/>
    </select>

    <select id="selectEmployeePageByCondition"
            resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvEmployeeDetailVO">
        <include refid="employeeDetail" />
        <include refid="selectEmployeeWhere"/>
    </select>


    <sql id="selectEmployeeWhere">
        WHERE e.deleted != 1 and e.status = 1
        AND u.deleted = 0
        <if test="q.empIds != null and q.empIds.size() > 0">
            AND e.id IN
            <foreach collection="q.empIds" index="index" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>

        <if test="q.orgIds != null and q.orgIds.size() > 0">
            AND e.belong_to_org IN
            <choose>
                <when test="q.isContainChildren">
                    (
                    <bind name="parent" value="q.orgIds" />
                    <include refid="com.swcares.common.uc.mapper.OrganizationMapper.recursive_children"></include>
                    )
                </when>
                <otherwise>
                    <foreach collection="q.orgIds" index="index" item="id" separator="," open="(" close=")">
                        #{id}
                    </foreach>
                </otherwise>
            </choose>
        </if>

        <if test="q.jobPosition != null and q.jobPosition != ''">
            AND e.job_position like #{q.jobPosition}
        </if>

        <if test="q.condition != null and q.condition != ''">
            AND (e.name LIKE #{q.condition} OR e.email_address LIKE #{q.condition} OR u.name LIKE #{q.condition})
        </if>
    </sql>
</mapper>
