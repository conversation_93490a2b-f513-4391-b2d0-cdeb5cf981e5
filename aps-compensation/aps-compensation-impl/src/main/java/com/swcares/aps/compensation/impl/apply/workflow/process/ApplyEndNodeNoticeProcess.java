package com.swcares.aps.compensation.impl.apply.workflow.process;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.compensation.impl.apply.constants.ApplyConstants;
import com.swcares.aps.compensation.impl.apply.constants.ApplyErrors;
import com.swcares.aps.compensation.impl.apply.enums.ApplyWorkflowNodeBusiTypeEnum;
import com.swcares.aps.compensation.impl.apply.workflow.ApplyWorkflowService;
import com.swcares.aps.component.workflow.NodeNoticeProcess;
import com.swcares.aps.component.workflow.dto.NodeExtVarsDTO;
import com.swcares.aps.component.workflow.dto.NodeNoticeDTO;
import com.swcares.aps.component.workflow.dto.NodeNoticeProcessResult;
import com.swcares.aps.component.workflow.entity.WorkflowAuditorIdInfoDO;
import com.swcares.aps.component.workflow.enums.ApsProjectEnum;
import com.swcares.aps.component.workflow.service.WorkflowAuditorIdInfoService;
import com.swcares.baseframe.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * ClassName：EndNodeNoticeProcess <br>
 * Description：申领单END通知节点处理；直接将申领单改为审核通过或者审核不通过 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/1/17 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class ApplyEndNodeNoticeProcess implements NodeNoticeProcess {


    @Autowired
    private ApplyWorkflowService applyWorkflowService;

    @Autowired
    private WorkflowAuditorIdInfoService workflowAuditorIdInfoService;

    @Override
    public NodeNoticeProcessResult process(NodeNoticeDTO noticeDTO) {
        try {
        applyWorkflowService.endWorkflowProcess(noticeDTO);

        WorkflowAuditorIdInfoDO workflowAuditorIdInfoDO = new WorkflowAuditorIdInfoDO();
        workflowAuditorIdInfoDO.setProject(ApsProjectEnum.APPLY_IMPL.getProjectType());
        workflowAuditorIdInfoDO.setBusiness(ApplyConstants.APPLY_WORKFLOW_BUSINESS);
        workflowAuditorIdInfoDO.setBusinessValue(noticeDTO.getBusiKey());

        workflowAuditorIdInfoService.deleteWorkflowAuditorIds(workflowAuditorIdInfoDO);
        }catch (Exception e){
            e.printStackTrace();
            log.error("【aps-apply-impl】结束流程申领单流程出错，noticeDTO:{"+ JSONUtil.toJsonStr(noticeDTO)+"}",e);
            throw new BusinessException(ApplyErrors.AUDIT_ERROR,"END流程异常");
        }
        return new NodeNoticeProcessResult();
    }

    @Override
    public boolean canProcess(NodeNoticeDTO noticeDTO) {
        boolean result=false;
        NodeExtVarsDTO extVarsDTO = noticeDTO.getExtVars();
        if(StringUtils.equalsIgnoreCase(extVarsDTO.getProject(), ApsProjectEnum.APPLY_IMPL.getProjectType())
                && StringUtils.equalsIgnoreCase(extVarsDTO.getBusiness(), ApplyConstants.APPLY_WORKFLOW_BUSINESS)
                && StringUtils.equalsIgnoreCase(noticeDTO.getNodeBusinessType(), ApplyWorkflowNodeBusiTypeEnum.END.getType())){
            result=true;
        }
        return result;
    }

}
