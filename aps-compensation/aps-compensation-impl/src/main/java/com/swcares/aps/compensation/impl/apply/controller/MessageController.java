package com.swcares.aps.compensation.impl.apply.controller;

import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.components.msg.CustomerMessageRemoteService;
import com.swcares.components.msg.dto.CustomerMessageDepositoryDTO;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：MessageController <br>
 * Package：com.swcares.aps.apply.impl.controller <br> 
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2021年 11月26日 9:47 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/apply/message")
@Api(tags = "对接基础框架短信接口")
@ApiVersion(value = "对接基础框架短信接口 v1.0")
public class MessageController {

    // 发给非系统用户
    @Autowired
    private CustomerMessageRemoteService customerMessageRemoteService;


    /**
     * @title sendDelay
     * @description 发送定时消息
     * <AUTHOR>
     * @date 2021/11/16 13:35
     * @param dto
     * @return com.swcares.baseframe.common.base.BaseResult<java.util.Map<java.lang.String,java.lang.Long>>
     */
    @PostMapping("/send_delay")
    BaseResult<Map<String, Long>> sendDelay(@RequestBody List<CustomerMessageDepositoryDTO> dto){
        return customerMessageRemoteService.sendDelay(dto);
    }

    /**
     * @title sendNow
     * @description 发送即时消息
     * <AUTHOR>
     * @date 2021/11/16 13:35
     * @param dto
     * @return com.swcares.baseframe.common.base.BaseResult<java.util.Map<java.lang.String,java.lang.Long>>
     */
    @PostMapping("/send_now")
    BaseResult<Map<String, Long>> sendNow(@RequestBody List<CustomerMessageDepositoryDTO> dto){
        return customerMessageRemoteService.sendNow(dto);
    }

}
