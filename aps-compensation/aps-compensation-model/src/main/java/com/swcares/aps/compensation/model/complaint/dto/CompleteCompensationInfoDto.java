package com.swcares.aps.compensation.model.complaint.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@ApiModel(value = "CompleteCompensationInfoDto对象", description = "填写补偿信息")
public class CompleteCompensationInfoDto {

    @ApiModelProperty(value = "补偿航站",required = true)
    @NotBlank(message = "补偿航站不能为空")
    private String compensationAirport;

    @ApiModelProperty(value = "补偿原因")
    private String compensationReason;

    @ApiModelProperty(value = "补偿方式",required = true)
    @NotBlank(message = "补偿方式不能为空")
    private String compensationWay;

    @ApiModelProperty(value = "方式子类型",required = true)
    @NotBlank(message = "方式子类型不能为空")
    private String compensationWaySubType;

    @ApiModelProperty(value = "补偿金额",required = true)
    private String compensationAmount;

    @ApiModelProperty(value = "补偿金额子类型",required = true)
    @NotBlank(message = "补偿金额子类型不能为空")
    private String compensationAmountSubType;

    @ApiModelProperty(value = "补偿金额对照信息")
    @Valid
    private List<CompensationPassengerAmountDto> compensationPassengerAmountDto;
}
