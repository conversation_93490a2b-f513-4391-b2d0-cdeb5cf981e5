## oracle改造的问题总结
>* 不支持if判断，使用nvl2代替，但是仅用于判断字段是否是null（oracle中，空字符串==null）
>    - nvl2（exp1,exp2,exp3）,相当于ifnull用法，当exp1（不支持带运算的表达式）为null时输出结果exp3，反之输出exp2
>    - 如果真的需要使用带运算的表达式，使用case when(通用)可以实现

>* 获取当前时间:
>   - oracle--->SYSDATE;<![CDATA[ < ]]>
>   - mysql--->now(),current_timestamp(),current_date()

>* 比较运算符用下面的表达式包裹:
>   - oracle---><![CDATA[ ~ ]]>，其中~代表比较运算符


>* 时间的相加:
>   - mysql-->adddate(某个时间,interval 1 year),此函数在mysql中会自动处理闰月以及大小月问题。
>   - oracle---> 某个时间 + interval '1' YEAR,此函数可以实现时间增加一年但是不会处理闰月以及大小月；
   建议使用： ADD_MONTHS (某个时间, 12) 加一年，ADD_MONTHS (某个时间, -12) 减一年

>* 将当前结果作为临时表（子查询）
>   - 例子：select * from (select * from  A) as A
>   - mysql可以使用as
>   - oracle不能使用as，否则会报错：> ORA-00933: SQL command not properly ended

>* 表字段与Oracle关键字冲突解决方法
>   - 改表的字段名称，这是从根本上解决问题的
>   - 在select的时候，用表别名."DAXIE"用双引号包起来并且双引号里面的字段名大写这种方式

>* left()函数和字符截取
>   - left函数是mysql独有，oracle没有，我们建议都适用substr()函数
>   - mysql中的start是从1开始的，如果截取第一位，mysql写法：SUBSTR(name, 1, 1), 而oracle除了这种写法，可以支持从0开始即：SUBSTR(name, 0, 1)

>* convert()函数
>   - mysql写法：convert( name using gbk ),
>   - oracle写法：convert(name, 'UTF8')； -- 查看oracle中支持哪些字符集： SELECT * FROM V$NLS_VALID_VALUES WHERE parameter = 'CHARACTERSET'

>* concat()函数
>   - 在Oracle中，CONCAT函数将只允许您将两个值连接在一起。如果需要连接多个值，那么我们可以嵌套多个CONCAT函数调用
>   - SELECT CONCAT(CONCAT('A', 'B'),'C') FROM dual;

>* 获取查询结果集第N条数据:
>   - mysql-->结尾时使用LIMT N;
>   - oracle---> WHERE条件内rownum = N;

>* 同一列分组拼接:
>   - mysql--> GROUP_CONCAT(拼接列名);
>   - oracle---> 两种方式1.wm_concat(拼接列名)默认按照逗号来间隔;2.listagg(拼接列名,单引号中可以设置分隔符) within group(order by 拼接列名排序);

>* WITH RECURSIVE 递归
>   - mysql中语法为：```
      WITH RECURSIVE children AS (
      SELECT d.* FROM uc_organization d WHERE d.id IN (199,200,201,202,203,204,205)
      UNION
      SELECT d.* FROM children c, uc_organization d WHERE d.parent_id = c.id
      )
      SELECT id FROM children ```CONCAT函数将只允许您将两个值连接在一起。如果需要连接多个值，那么我们可以嵌套多个CONCAT函数调用
>   - mysql中语法为：``` WITH children(id, parent_id) AS (
      SELECT
      id,
      parent_id
      FROM
      uc_organization
      WHERE
      id IN ( 199, 200, 201, 202, 203, 204, 205 )
      UNION ALL
      SELECT
      d.id, d.parent_id
      FROM
      uc_organization d, children
      WHERE
      d.parent_id = children.id
      )
      SELECT
      id
      FROM
      children ```
>  - oracle使用参考官方文档：https://oracle-base.com/articles/11g/recursive-subquery-factoring-11gr2
        

>* 字符串根据指定分隔符动态截取:
>   -  mysql--> substring(str1,str2,count)
>               str1 -->  原字符串
>               str2 -->  分隔符
>               count -->  count为正数时，从左往右数，第count个分隔符左边的内容；为负数时，从右往左数，第count个分隔符右边的内容
>   - oracle--> substr(字符串,起始位置,长度) 与 instr(原字符串,目标字符串,[原字符串开始位置],[目标字符串出现次数])
>               substr --> 截取字符串
>               instr --> 获取子字符串在原字符串的位置

>* 构造连续的数字，通过连续有规律的数字实现各种动态的数据，如连续的日期，动态拆解字符串，拆解区间等等
>   - mysql --> 使用MYSQL.HELP_TOPIC,主键为自增的连续数字，间距为1，起始值为0
>               通过多表连接使用，通过where条件限制取值，将id值带入相应的表达式中，实现功能
>   - oracle --> 使用 connect by LEVEL,同样也为自增的数字，间距为1，起始值为1
>               举例：select level from dual connect by level<=5;   产生1-5的连续数字，将level带入相应的地方实现功能

>* 带有order by 的两个sql，使用union/union all ,产生 sql命令未正常结束错误
>   - oracle 检测到 order by 就认为整个sql结束，但是后面又找到 union all，导致数据库无法解析
>   - 使用： with a as (...),b as (...) select * from a union all select * from b 