package com.swcares.aps.staff.bff.controller;

import com.swcares.aps.basic.data.remoteapi.model.dto.FlightBaseQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightBasicnfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.SegmentFindVO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * ClassName：FlightController <br>
 * Description： <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/7 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/h5")
@Api(tags = "航班相关接口")
@ApiVersion(value = "工作人员端v1.0")
public class FlightController extends BaseController {

	@Autowired
	CompensationInfoApi compensationInfoApi;

	@PostMapping("/flight/getFlightList")
	BaseResult<List<FlightBasicnfoVO>> getFlightBasicInfo(@RequestBody FlightBaseQueryDTO dto){
		return compensationInfoApi.getFlightBasicInfo(dto);
	}


	@GetMapping("/flight/baggage/getFlight")
	public BaseResult<Object> getFlight(@RequestParam(value = "date", required = true)String date, @RequestParam(value = "flightNo", required = true)  String flightNo, @RequestParam(value = "choiceSegment", required = true)  String choiceSegment){
		return ok(compensationInfoApi.getFlight(date,flightNo,choiceSegment));
	}

	@GetMapping("/flight/getFltAirStation")
	public BaseResult<Set<String>> getFltAirStation(@RequestParam(value = "date", required = true)String date, @RequestParam(value = "flightNo", required = true)  String flightNo, @RequestParam(value = "choiceSegment", required = true)  String choiceSegment){
		return compensationInfoApi.getFltAirStation(date, flightNo, choiceSegment);
	}
}
