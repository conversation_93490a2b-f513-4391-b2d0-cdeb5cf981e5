<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.ground.common.mapper.TenantMapper">
    <select id="getTenantIdByCode" resultType="java.lang.Long">
        select id FROM SYS_TENANT where TENANT_CODE = #{tenantCode}
    </select>

    <select id="getTenantById" resultType="map">
        select TENANT_CODE,COMPANY_NAME,TENANT_NAME,ID FROM SYS_TENANT where ID = #{id}
    </select>

    <select id="getTenantByCode" resultType="map">
        select TENANT_CODE,COMPANY_NAME,TENANT_NAME,ID FROM SYS_TENANT where TENANT_CODE = #{tenantCode}
    </select>
</mapper>