<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.msg.mapper.SmsTaskImportReciverMapper">
    <select id="importDetail" resultType="com.swcares.aps.msg.model.vo.SmsTaskReciverImportVO">
        select PNR,
               MOBILE phoneNumber,
               TKT_NO ticketNumber,
               FLIGHT_NO flightNumber,
               FLIGHT_DATE flightDate,
               FLIGHT_SEGMENT flightSegment,
               STATUS "result",
               REASON reason
               from MSG_SMS_TASK_IMPORT_RECIVER
                <where>
                    <if test="dto.id!=null and dto.id!=''">
                        AND IMPORT_ID = #{dto.id}
                    </if>
                    <if test="dto.status!=null and dto.status!=''">
                        AND STATUS = #{dto.status}
                    </if>
                </where>
    </select>

    <select id="importDetailByImportId" resultType="com.swcares.aps.msg.model.entity.SmsTaskImportReciverInfo">
        select PNR,
        MOBILE ,
        TKT_NO ,
        FLIGHT_NO ,
        FLIGHT_DATE ,
        FLIGHT_SEGMENT ,
        STATUS ,
        REASON ,
        org,
        dst,
        IMPORT_ID,
        TYPE
        from MSG_SMS_TASK_IMPORT_RECIVER
        <where>
            <if test="dto.id!=null and dto.id!=''">
                AND IMPORT_ID = #{dto.id}
            </if>
            <if test="dto.status!=null and dto.status!=''">
                AND STATUS = #{dto.status}
            </if>
        </where>
    </select>

    <delete id="deleteByImportId">
        delete from MSG_SMS_TASK_IMPORT_RECIVER where IMPORT_ID = #{importId}
    </delete>
</mapper>