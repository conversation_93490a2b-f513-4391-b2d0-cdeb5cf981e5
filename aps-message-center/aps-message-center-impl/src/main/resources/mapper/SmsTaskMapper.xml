<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.msg.mapper.SmsTaskMapper">

    <select id="getBusinessType" resultType="com.swcares.aps.msg.model.vo.BusinessTypeVO">
        SELECT DICT_LABEL code,
        DICT_VALUE category
        from SYS_DICTIONARY_DATA
        WHERE DICT_TYPE in (
        <foreach collection="value" item="item" index="index" separator=",">
            CONCAT('mc_business_type',#{item})
        </foreach>
              )
    </select>
    <select id="getTaskId" resultType="java.lang.String">
        SELECT NVL(max(TASK_ID)+1,CONCAT(#{date},'00001'))
        FROM MSG_MESSAGE_TASK
        WHERE SUBSTR(TASK_ID,1,8) = #{date}
    </select>

    <select id="getAuditInfo" resultType="com.swcares.aps.msg.model.vo.SmsTaskAuditInfoVO">
        SELECT
               b.FLIGHT_NUMBER flightNo,
               a.FLIGHT_START_DATE flightBeginDate,
               a.FLIGHT_END_DATE flightEndDate,
               a.FLIGHT_SCHEDULE flightSchedule,
               b.ORG||'-'||b.DST flightSegment ,
               a.SMS_CONTENT content,
               a.SEND_PHONE sendNumber,
               a.SMS_TYPE smsType,
               a.BUSINESS_TYPE businessType,
               a.CREATE_TIME creatTime,
               a.CREATE_USER || a.CREATE_USER_JOB_NUMBER createUser,
               a.TEMPLATE_ID templateId,
               a.EMERGENCY_DEGREE degree,
               a.ORIG_DES_Time origDesTime,
               a.ORIG_DEP_TIME origDepTime,
               a.DEP_TIME depTime,
               a.DES_TIME desTime,
               a.PLANE_TYPE planeType,
               a.NEW_PLANE_TYPE newPlaneType,
               a.PEOPLE_COUNT peopleCount,
               a.PNR_COUNT pnrCount
        FROM
            MSG_MESSAGE_TASK a
        LEFT JOIN MSG_MESSAGE_TASK_DETAIL b ON
            a.TASK_ID = b.TASK_ID
        WHERE a.TASK_ID =#{taskId}
    </select>

    <select id="getMinEtd" resultType="java.util.Date">
        select min(ETD) from MSG_MESSAGE_TASK_DETAIL where TASK_ID = #{taskId}
    </select>

    <select id="getSmsTaskDetail" resultType="com.swcares.aps.msg.model.vo.SmsTaskDetailVO">
        SELECT
            a.VIEW_MODE viewMode,
            b.FLIGHT_NUMBER flightNo,
            a.FLIGHT_START_DATE flightBeginDate,
            a.FLIGHT_END_DATE flightEndDate,
            a.FLIGHT_SCHEDULE flightSchedule,
            nvl((SELECT  ai.AIRPORT_NAME||ai.AIRPORT_3CODE  org from BD_AIRPORT_INFO ai WHERE ai.AIRPORT_3CODE=b.ORG),b.ORG) org,
            nvl((SELECT  ai.AIRPORT_NAME||ai.AIRPORT_3CODE  org from BD_AIRPORT_INFO ai WHERE ai.AIRPORT_3CODE=b.DST),b.DST) dst,
            a.DATA_SOURCE dataSource,
            c.TEMPLETE_NAME templateName,
            a.SMS_CONTENT content,
            a.SEND_PHONE sendNumber,
            a.SMS_TYPE smsType,
            a.BUSINESS_TYPE businessType,
            a.CREATE_TIME creatTime,
            a.FINISH_TIME finishTime,
            a.CREATE_USER|| a.CREATE_USER_JOB_NUMBER createUser,
            a.STOP_USER stopUser,
            CASE TASK_STATUS
                WHEN '1' THEN '1'
                WHEN '0' THEN NULL
                ELSE '0'
                END auditOpinion,
            a.REASON reason,
            a.AUDIT_USER auditUser,
            a.AUDIT_TIME auditTime,
            a.EMERGENCY_DEGREE degree,
            a.ORIG_DES_Time origDesTime,
            a.ORIG_DEP_TIME origDepTime,
            a.DEP_TIME depTime,
            a.DES_TIME desTime,
            a.PLANE_TYPE planeType,
            a.NEW_PLANE_TYPE newPlaneType,
            a.PEOPLE_COUNT peopleCount,
            a.PNR_COUNT pnrCount
        FROM
            MSG_MESSAGE_TASK a
                left JOIN MSG_MESSAGE_TASK_DETAIL b
                          ON a.TASK_ID = b.TASK_ID
                left JOIN MSG_SMS_TEMPLETE c
                          ON a.TEMPLATE_ID  = c.ID
        WHERE a.TASK_ID =#{taskId}
    </select>

    <select id="getSmsTaskDetailSegment" resultType="com.swcares.aps.msg.model.vo.SmsTaskDetailVO">
        SELECT
            a.VIEW_MODE viewMode,
            b.FLIGHT_NUMBER flightNo,
            a.FLIGHT_START_DATE flightBeginDate,
            a.FLIGHT_END_DATE flightEndDate,
            a.FLIGHT_SCHEDULE flightSchedule,
            nvl((SELECT  ai.AIRPORT_NAME||ai.AIRPORT_3CODE  org from BD_AIRPORT_INFO ai WHERE ai.AIRPORT_3CODE=b.ORG),b.ORG) org,
            nvl((SELECT  ai.AIRPORT_NAME||ai.AIRPORT_3CODE org from BD_AIRPORT_INFO ai WHERE ai.AIRPORT_3CODE=b.DST),b.DST) dst,
            a.DATA_SOURCE dataSource,
            c.TEMPLETE_NAME templateName,
            a.SMS_CONTENT content,
            a.SEND_PHONE sendNumber,
            a.SMS_TYPE smsType,
            a.BUSINESS_TYPE businessType,
            a.CREATE_TIME creatTime,
            a.FINISH_TIME finishTime,
            a.CREATE_USER|| a.CREATE_USER_JOB_NUMBER createUser,
            a.STOP_USER stopUser,
            CASE TASK_STATUS
                WHEN '1' THEN '1'
                WHEN '0' THEN NULL
                ELSE '0'
                END auditOpinion,
            a.REASON reason,
            a.AUDIT_USER auditUser,
            a.AUDIT_TIME auditTime,
            a.EMERGENCY_DEGREE degree,
            a.ORIG_DES_Time origDesTime,
            a.ORIG_DEP_TIME origDepTime,
            a.DEP_TIME depTime,
            a.DES_TIME desTime,
            a.PLANE_TYPE planeType,
            a.NEW_PLANE_TYPE newPlaneType,
            a.PEOPLE_COUNT peopleCount,
            a.PNR_COUNT pnrCount
        FROM
            MSG_MESSAGE_TASK a
                left JOIN MSG_MESSAGE_TASK_FLT_SEGMENT b
                          ON a.TASK_ID = b.TASK_ID
                left JOIN MSG_SMS_TEMPLETE c
                          ON a.TEMPLATE_ID  = c.ID
        WHERE a.TASK_ID =#{taskId}
    </select>
    <select id="getMobileByPnr" resultType="com.swcares.aps.msg.model.vo.PassengerContactInfoVO">
        SELECT DISTINCT fpc.PHONE_NUMBER, fpc.PHONE_TYPE, fpri.TICKET_NUMBER, fpri.PASSENGER_NAME, fpri.PASSENGER_NAME_EN
        FROM FLT_PASSENGER_REAL_INFO fpri
                 JOIN FLT_PASSENGER_CONTACTS fpc
                      ON fpri.ID =fpc.PASSR_ID
        WHERE fpri.PNR_REF= #{pnr}
          AND FPRI .FLIGHT_NUMBER = #{flightNo}
          AND FPRI .FLIGHT_DATE = #{flightDate}
          AND FPRI .ORG = #{org}
          AND FPRI .DST = #{dst}
    </select>

    <select id="getMobileByTktNo" resultType="com.swcares.aps.msg.model.vo.PassengerContactInfoVO">
        SELECT DISTINCT fpc.PHONE_NUMBER, fpc.PHONE_TYPE, fpri.pnr_ref pnr, fpri.PASSENGER_NAME, fpri.PASSENGER_NAME_EN
        FROM FLT_PASSENGER_REAL_INFO fpri
                 JOIN FLT_PASSENGER_CONTACTS fpc
                      ON fpri.ID =fpc.PASSR_ID
        WHERE fpri.TICKET_NUMBER= #{tktNo}
          AND FPRI .FLIGHT_NUMBER = #{flightNo}
          AND FPRI .FLIGHT_DATE = #{flightDate}
          AND FPRI .ORG = #{org}
          AND FPRI .DST = #{dst}
    </select>
</mapper>