package com.swcares.aps.msg.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.msg.model.dto.SmsPersonTimeStatisticsSearchDTO;
import com.swcares.aps.msg.model.vo.SmsPersonTimeStatisticsResultVO;
import com.swcares.aps.msg.service.SentSmsReportService;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @ClassName：SmsPersonTimeStatisticsController
 * @Description：短信人次统计模块
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/11/25 10:24
 * @version： v1.0
 */
@RestController
@RequestMapping("/sms/person/report")
@Api(tags = "短信统计报表接口")
public class SmsPersonTimeStatisticsController  extends BaseController {

    @Autowired
    SentSmsReportService sentSmsReportService;

    //列表查询
    @PostMapping("/statics")
    @ApiOperation("短信人次统计列表")
    public PagedResult<List<SmsPersonTimeStatisticsResultVO>> getSmsPersonTimeStatistics(@RequestBody SmsPersonTimeStatisticsSearchDTO searchDTO){
        IPage<SmsPersonTimeStatisticsResultVO> smsPersonTimeStatistics = sentSmsReportService.getSmsPersonTimeStatistics(searchDTO);
        return ok(smsPersonTimeStatistics);
    }
    //按照查询条件下载
    @PostMapping("/download")
    @ApiOperation("根据查询条件下载指定短信人次统计")
    public void personTimeDownload(@RequestBody SmsPersonTimeStatisticsSearchDTO searchDto, HttpServletRequest request, HttpServletResponse response){
        sentSmsReportService.downLoad(searchDto, request, response);
    }
}
