package com.swcares.aps.compensation.model.baggage.accident.dto;

import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName：VerifyAlikeOrderDTO
 * @Description：验证事故单||赔偿单 【异常行李】
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/6/5 11:05
 * @version： v1.0
 */
@SecretInfoEntity
@Data
public class VerifyAlikePaxOrderDTO {

    @ApiModelProperty(value = "补偿事故单类型- 1.不正常航班 2异常行李 3超售")
    @NotNull(message = "补偿事故单类型不能为空")
    private String accidentType;

    @ApiModelProperty(value = "校验类型【1事故单 2补偿单】")
    @NotNull(message = "校验类型不能为空")
    private String checkType;

    @ApiModelProperty(value = "异常行李事故类型（21破损,22少收,23多收,24内件缺少,25丢失）")
    private String type;

    @ApiModelProperty(value = "事故单号【编辑必传】")
    private String accidentNo;

    @ApiModelProperty(value = "赔偿单单号【编辑必传】")
    private String orderNo;

    @ApiModelProperty(value = "旅客姓名")
    @NotNull(message = "旅客姓名不能为空")
    private String paxName;

    @ApiModelProperty(value = "证件类型")
    @NotNull(message = "证件类型不能为空")
    private String idType;

    @ApiModelProperty(value = "证件号")
    @NotNull(message = "证件号不能为空")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "航班号")
    @NotNull(message = "航班号不能为空")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    @NotNull(message = "航班日期不能为空")
    private String flightDate;


}
