package com.swcares.aps.web.bff.controller;

import com.swcares.aps.compensation.model.compensation.dto.CompensationSMSDTO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName：SMSController
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 谭睿
 * @Date： 2022/9/1 13:49
 * @version： v1.0
 */
@RestController
@RequestMapping("/sms")
@Api(tags = "短信发送接口")
@ApiVersion(value = "短信发送接口 v1.0")
public class SMSController {

    @Autowired
    private CompensationInfoApi compensationInfoApi;

    @PostMapping("/sendSMS")
    @ApiOperation(value = "站内信接口")
    public BaseResult<Object>  sendSMS(@RequestBody @Validated CompensationSMSDTO dto){
        return compensationInfoApi.sendSMS(dto);
    }


    @PostMapping("/sendPassengerSMS")
    @ApiOperation(value = "发送旅客短信接口")
    @Deprecated
    public BaseResult<Object>  sendPassengerSMS(@RequestBody @Validated CompensationSMSDTO dto){
        return compensationInfoApi.sendPassengerSMS(dto);
    }

    /**
     * @title authPassengerCode
     * @description @TODO
     * <AUTHOR>
     * @date 2022/9/2 14:12
     * @param dto
     * @return BaseResult<Object>
     */
    @PostMapping("/authPassengerCode")
    @ApiOperation(value = "验证旅客短信验证码")
    @Deprecated
    public BaseResult<Object> authPassengerCode(@RequestBody CompensationSMSDTO dto){
        return compensationInfoApi.authPassengerCode(dto);
    }
}
