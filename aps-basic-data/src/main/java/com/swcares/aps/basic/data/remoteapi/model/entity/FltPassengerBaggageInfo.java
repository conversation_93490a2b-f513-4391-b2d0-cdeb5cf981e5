package com.swcares.aps.basic.data.remoteapi.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：FltPassengerBaggageInfo <br>
 * Package：com.swcares.reaptv.flightpassenger.model.passenger.entity <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2022年 04月21日 9:19 <br>
 * @version v1.0 <br>
 */
@TableName("flt_passenger_baggage_info")
public class FltPassengerBaggageInfo {
    @TableField("ID")
    private String ID; // 主键ID

    @TableField("PASSR_ID")
    private String PASSR_ID; //旅客信息ID

    @TableField("BAGGAGE_TYPE")
    private String BAGGAGE_TYPE; //行李类型 BG -普通行李; PETC = 客舱狗笼; AVIH = 货舱狗笼

    @TableField("BAGGAGE_COUNT")
    private BigDecimal BAGGAGE_COUNT; //行李数量

    @TableField("BAGGAGE_WEIGHT")
    private BigDecimal BAGGAGE_WEIGHT; //行李重量

    @TableField("BAGGAGE_NUMBER")
    private String BAGGAGE_NUMBER; //行李号

    @TableField("DEST")
    private String DEST; //行李目的地

    @TableField("TAG_TYPE")
    private String TAG_TYPE; //标签类型

    @TableField("HANDLER")
    private String HANDLER; //承运航司三字码 876

    public String getID() {
        return ID;
    }

    public void setID(String iD) {
        ID = iD;
    }

    public String getPASSR_ID() {
        return PASSR_ID;
    }

    public void setPASSR_ID(String pASSR_ID) {
        PASSR_ID = pASSR_ID;
    }

    public String getBAGGAGE_TYPE() {
        return BAGGAGE_TYPE;
    }

    public void setBAGGAGE_TYPE(String bAGGAGE_TYPE) {
        BAGGAGE_TYPE = bAGGAGE_TYPE;
    }

    public BigDecimal getBAGGAGE_COUNT() {
        return BAGGAGE_COUNT;
    }

    public void setBAGGAGE_COUNT(BigDecimal bAGGAGE_COUNT) {
        BAGGAGE_COUNT = bAGGAGE_COUNT;
    }

    public BigDecimal getBAGGAGE_WEIGHT() {
        return BAGGAGE_WEIGHT;
    }

    public void setBAGGAGE_WEIGHT(BigDecimal bAGGAGE_WEIGHT) {
        BAGGAGE_WEIGHT = bAGGAGE_WEIGHT;
    }

    public String getBAGGAGE_NUMBER() {
        return BAGGAGE_NUMBER;
    }

    public void setBAGGAGE_NUMBER(String bAGGAGE_NUMBER) {
        BAGGAGE_NUMBER = bAGGAGE_NUMBER;
    }

    public String getDEST() {
        return DEST;
    }

    public void setDEST(String dEST) {
        DEST = dEST;
    }

    public String getTAG_TYPE() {
        return TAG_TYPE;
    }

    public void setTAG_TYPE(String tAG_TYPE) {
        TAG_TYPE = tAG_TYPE;
    }

    public String getHANDLER() {
        return HANDLER;
    }

    public void setHANDLER(String hANDLER) {
        HANDLER = hANDLER;
    }

}
