package com.swcares.aps.msg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.msg.model.dto.FlightArriveEventDTO;
import com.swcares.aps.msg.model.dto.TicketIssueEventDTO;
import com.swcares.aps.msg.model.entity.SmsTaskDetailInfo;

import java.util.List;

public interface SmsTaskDetailService extends IService<SmsTaskDetailInfo> {

    List<SmsTaskDetailInfo> getIssueOrdinarySmsTaskDetailInfo(TicketIssueEventDTO ticketIssueEventDTO);

    List<SmsTaskDetailInfo> getArriveSmsTaskDetailInfo(FlightArriveEventDTO flightArriveEventDTO);

    List<SmsTaskDetailInfo> getDetailInfosOfTask(String taskId);
}
