package com.swcares.aps.compensation.model.overbook.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：OverBookConfigVO
 * @Description：超售规则查看
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/28 16:38
 * @version： v1.0
 */
@Data
public class OverBookConfigVO {

    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "类型：1改签，2退票")
    private String type;
    @ApiModelProperty(value = "左条件值（整数，延误小时 表达式<）")
    private String leftConditionValue;
    @ApiModelProperty(value = "右条件值（整数，延误小时 表达式<=）")
    private String rightConditionValue;
    @ApiModelProperty(value = "赔偿固定金额（类型为1退票时，此字段必须有值）")
    private String fixedMoney;
    @ApiModelProperty(value = "舱位全价（%）")
    private String fullPrice;
}
