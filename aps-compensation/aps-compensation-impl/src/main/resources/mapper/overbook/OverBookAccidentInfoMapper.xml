<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.overbook.mapper.OverBookAccidentInfoMapper">
  
    
    <select id="getPcPages" resultType="com.swcares.aps.compensation.model.overbook.vo.OverBookPcListVO">
        select
        ID as accidentId,
        ACCIDENT_NO,
        ACCIDENT_STATUS,
        type ,
        ACCIDENT_SOURCE as accidentSource,
        BELONG_AIRLINE,
        FLIGHT_NO,
        FLIGHT_DATE,
        SEGMENT,
        SEGMENT_CH segmentCh,
        CREATED_BY ,
        to_char(CREATED_TIME,'YYYY-MM-DD hh24:mi:ss')  CREATED_TIME,
        (select count(*) from COMPENSATION_ORDER_INFO where ACCIDENT_ID =ab.id) compensateNum

        from OVER_BOOK_ACCIDENT_INFO ab

        <where>
        <if test="dto.flightNo != null and dto.flightNo != ''">
            and FLIGHT_NO = #{dto.flightNo}
        </if>
        <if test="dto.flightStartDate != null and dto.flightStartDate != '' and dto.flightEndDate != null and dto.flightEndDate != ''">
            and FLIGHT_DATE  between #{dto.flightStartDate} and #{dto.flightEndDate}
        </if>
        <if test="dto.accidentNo != null and dto.accidentNo != ''">
            and ACCIDENT_NO = #{dto.accidentNo}
        </if>
        <if test="dto.accidentSource != null and dto.accidentSource != ''">
            and ACCIDENT_SOURCE = #{dto.accidentSource}
        </if>

        <if test="dto.orgCity != null and dto.orgCity != ''">
            and ORG_CITY_AIRP =  #{dto.orgCity}
        </if>

        <if test="dto.dstCity != null and dto.dstCity != ''">
            and DST_CITY_AIRP =  #{dto.dstCity}
        </if>

        <if test="dto.belongAirline != null  and dto.belongAirline.size() > 0">
            and BELONG_AIRLINE in
            <foreach collection="dto.belongAirline" item="belongAirline"  open="(" separator="," close=")">
                #{belongAirline}
            </foreach>
        </if>
        <if test="dto.type != null  and dto.type.size() > 0">
            and type in
            <foreach collection="dto.type" item="type"  open="(" separator="," close=")">
            #{type}
           </foreach>
        </if>
        <if test="dto.accidentStatus != null  and dto.accidentStatus.size() > 0">
            and ACCIDENT_STATUS in
            <foreach collection="dto.accidentStatus" item="accidentStatus"  open="(" separator="," close=")">
                #{accidentStatus}
            </foreach>
        </if>

        </where>

        order  by FLIGHT_DATE desc,ACCIDENT_STATUS asc
    </select>

    <select id="getH5List" resultType="com.swcares.aps.compensation.model.overbook.vo.OverBookH5ListVO">

        select
        ID as accidentId,
        ACCIDENT_NO,
        ACCIDENT_STATUS,
        ACCIDENT_SOURCE as source,
        type ,
        FLIGHT_NO,
        FLIGHT_DATE,
        SEGMENT_CH segmentCh,
        pax_name
        from OVER_BOOK_ACCIDENT_INFO
        <where>
            <if test="dto.paxName != null and dto.paxName != ''">
                and pax_name like concat(concat('%',#{dto.paxName}),'%')
            </if>
            <if test="dto.searchType != null and dto.searchType != ''">
                <if test="dto.searchType == '1'.toString() ">
                    <if test="dto.searchInfo != null and dto.searchInfo != ''">
                        and ID_NO =#{dto.searchInfo}
                    </if>

                </if>
                <if test="dto.searchType == '2'.toString() ">
                    <if test="dto.searchInfo != null and dto.searchInfo != ''">
                    and TKT_NO =#{dto.searchInfo}
                    </if>
                </if>
            </if>

            <if test="dto.flightNo != null and dto.flightNo != ''">
                and FLIGHT_NO = #{dto.flightNo}
            </if>
            <if test="dto.flightStartDate != null and dto.flightStartDate != '' and dto.flightEndDate != null and dto.flightEndDate != ''">
                and FLIGHT_DATE  between #{dto.flightStartDate} and #{dto.flightEndDate}
            </if>

            <if test="dto.typeList != null  and dto.typeList.size() > 0">
                and type in
                <foreach collection="dto.typeList" item="type"  open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="dto.accidentStatusList != null  and dto.accidentStatusList.size() > 0">
                and ACCIDENT_STATUS in
                <foreach collection="dto.accidentStatusList" item="accidentStatus"  open="(" separator="," close=")">
                    #{accidentStatus}
                </foreach>
            </if>

        </where>
        order  by FLIGHT_DATE desc,std ,ACCIDENT_STATUS asc

    </select>

    <select id="verifyAlikeAccidentOrder" resultType="java.lang.Integer">

        select count(1)

        from OVER_BOOK_ACCIDENT_INFO
        where ACCIDENT_STATUS !=4
        and FLIGHT_NO = #{dto.flightNo}
        and FLIGHT_DATE =#{dto.flightDate}
        and "TYPE" =#{dto.type}
        and PAX_NAME =#{dto.paxName}
        and ID_NO =#{dto.idNo}
        and ID_TYPE=#{dto.idType}
        <if test="dto.accidentNo != null and dto.accidentNo != ''">
            and ACCIDENT_NO != #{dto.accidentNo}
        </if>

    </select>


    <select id="verifyAlikeOrder" resultType="java.lang.Integer">
        <!-- // 当前航班下相同补偿单类型下是否存在该补偿单中存在旅客的其他补偿单（包含全部状态），-->
        select count(DISTINCT OI.id)
        from
        COMPENSATION_ORDER_INFO oi
        LEFT JOIN COMPENSATION_PAX_INFO pi on OI.id = PI.ORDER_ID
        where OI.FLIGHT_NO = #{dto.flightNo}
        and OI.FLIGHT_DATE =#{dto.flightDate}
        and oi.ACCIDENT_TYPE=#{dto.accidentType}
        and pi.PAX_NAME =#{dto.paxName}
        and pi.ID_NO =#{dto.idNo}
        and pi.ID_TYPE=#{dto.idType}
        <if test="dto.orderNo != null and dto.orderNo != ''">
            and OI.ORDER_NO != #{dto.orderNo}
        </if>
    </select>


    <select id="getCompensateNumList" resultType="com.swcares.aps.compensation.model.overbook.vo.CompensationNumListVO">


        select
            coi.COMPENSATE_TYPE,
            coi.STATUS,
            coi.ACCIDENT_TYPE,
            coi.ORDER_NO,
            pi.CURRENT_AMOUNT as sumMoney,
            PI.SWITCH_OFF,
        PI.CREATED_BY ,
        to_char(PI.CREATED_TIME,'YYYY-MM-DD hh24:mi:ss')  CREATED_TIME,
        PI.RECEIVE_STATUS
        from compensation_pax_info pi
        left join COMPENSATION_ORDER_INFO  coi on pi.order_id = coi."ID"
        where
        pi.PAX_ID = #{dto.paxId}
        and FLIGHT_NO = #{dto.flightNo}
        and FLIGHT_DATE =#{dto.flightDate}
        and coi.STATUS != '0'
        and coi.accident_type =#{dto.accidentType}
        order by coi.CREATED_TIME desc
    </select>



    <select id="getBookBasicPaxVO" resultType="com.swcares.aps.compensation.model.overbook.vo.OverBookBasicPaxVO">

        select

        (select
        count(DISTINCT pi.order_id)
        from compensation_pax_info pi
        left join COMPENSATION_ORDER_INFO  coi on pi.order_id = coi."ID"
        where
        pi.PAX_ID = ob.pax_id
        and coi.FLIGHT_NO = ob.FLIGHT_NO
        and coi.FLIGHT_DATE =ob.FLIGHT_DATE
        and coi.STATUS != '0'
        and coi.COMPENSATE_TYPE = '1'
        and coi.accident_type ='3' ) as compensateNum,
        ob.ID as accidentId ,
        ob.PAX_ID ,
        ob.PAX_NAME ,
        ob.ID_TYPE ,
        ob.ID_NO ,
        ob.TKT_NO ,
        ob.TELEPHONE ,
        ob.SEX ,
        ob.SEGMENT,
        ob.SEGMENT_CH ,
        ob.IS_CANCEL ,
        ob.MAIN_CLASS ,
        ob.SUB_CLASS ,
        ob.WITH_BABY ,
        ob.BABY_PAX_NAME ,
        ob.TKT_ISSUE_DATE as tktDate,
        ob.CANCEL_TIME ,
        ob.IS_CHILD ,
        ob.PNR ,
        ob.PAX_STATUS as checkStatus,
        ob.ORG_CITY_AIRP ,
        ob.DST_CITY_AIRP,
        ob.FULL_ECONOMY_FARE as fullEconomyFare

        from OVER_BOOK_ACCIDENT_INFO ob
        where ob.pax_id =#{paxId}
        and ob.id = #{accidentId}

    </select>


    <select id="getObCompensationDetails" resultType="com.swcares.aps.compensation.model.overbook.vo.OverBookCompensationDetailsVO">

        select
        oi.id as orderId,
        to_char(OI.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS') createdTime,
        OI.CREATED_BY,
        OI.SERVICE_CITY,
        oi.ORDER_NO ,
        oi.compensate_Type,
        oi.status,
        count(PI.id) as planCarryOutNum,
        COUNT(CASE
        WHEN PI.RECEIVE_STATUS = '1' THEN 1
        ELSE NULL
        END)as actualCarryOutNum,
        OI.SUM_MONEY as planSumMoney,
        sum(CASE
        WHEN PI.RECEIVE_STATUS = '1' THEN PI.CURRENT_AMOUNT
        ELSE NULL
        END)as actualSumMoney

        from COMPENSATION_ORDER_INFO oi
        LEFT JOIN COMPENSATION_PAX_INFO pi on oi.id = PI.ORDER_ID

         where  oi.ACCIDENT_ID = #{accidentId}

        GROUP BY
        oi.id ,
        to_char(OI.CREATED_TIME, 'YYYY-MM-DD HH24:MI:SS'),
        OI.CREATED_BY,
        OI.SERVICE_CITY,
        OI.ORDER_NO,
        OI.COMPENSATE_TYPE,
        OI.STATUS,
        OI.SUM_MONEY

    </select>

    <update id="updAccidentStatusBatch">
        UPDATE OVER_BOOK_ACCIDENT_INFO SET accident_status = #{accidentStatus}
        where 1=1
        <if test="accidentNo != null and accidentNo.size() >0">
            and accident_no in
            <foreach collection="accidentNo" item="items" open="(" separator="," close=")">
                #{items}
            </foreach>
        </if>
    </update>
</mapper>
