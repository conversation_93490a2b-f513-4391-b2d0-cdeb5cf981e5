package com.swcares.aps.compensation.model.complaint.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@ApiModel(value = "ComplaintCompensationCreateDto对象", description = "填写补偿信息")
public class ComplaintCompensationCreateDto {

    @ApiModelProperty(value = "事故单主键")
    private Long id;

    @ApiModelProperty(value = "补偿单主键,如果有就是编辑")
    private Long orderId;

    @ApiModelProperty(value = "旅客信息")
    @Valid
    private List<PassengerComplaintDto> passengerComplaintDtoList;

    @ApiModelProperty(value = "填写补偿信息")
    @Valid
    private CompleteCompensationInfoDto completeCompensationInfoDto;

    @ApiModelProperty(value = "操作标识：0：生成事故单及补偿单草稿，1：生成事故单及补偿单",example = "0：生成事故单及补偿单草稿，1：生成事故单及补偿单" ,required = true)
    @NotBlank(message = "操作标识不能为空")
    private String flag;

}
