package com.swcares.aps.compensation.model.irregularflight.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.compensation.entity.AuditInfo <br>
 * Description：赔偿单-待审核记录 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-11-24 <br>
 * @version v1.0 <br>
 */
@Data
@TableName("compensation_audit_info")
@ApiModel(value="AuditInfo对象", description="赔偿单-待审核记录")
public class CompensationAuditInfoDO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "赔偿单id")
    private Long orderId;

    private Long tenantId;

    @ApiModelProperty(value = "审核人id")
    private Long auditorId;

    @ApiModelProperty(value = "审核节点id")
    private String taskId;

}
