package com.swcares.aps.msg.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.msg.model.dto.TempleteDTO;
import com.swcares.aps.msg.model.dto.TempletePagedDTO;
import com.swcares.aps.msg.model.vo.TempleteBussinessTypeVO;
import com.swcares.aps.msg.model.vo.TempletePlaceHolderVO;
import com.swcares.aps.msg.model.vo.TempleteVO;
import com.swcares.aps.msg.service.TempleteService;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.base.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ClassName：com.swcares.reaptv.sms.SMS.controller.TempleteController <br>
 * Description： 短信模板 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-09-08 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/sms/templete")
@Api(tags = "短信模板接口")
public class TempleteController extends BaseController {
    @Autowired
    private TempleteService templeteService;

    @GetMapping("/business_type")
    @ApiOperation(value = "根据短信类型获取业务类型")
    public BaseResult<List<TempleteBussinessTypeVO>> getBusinessType(@RequestParam @ApiParam(value = "短信类型", required = false) String type) {
        return this.ok(templeteService.getBusinessType(type));
    }

    @GetMapping("/place_holder")
    @ApiOperation(value = "获取动态标识符")
    public BaseResult<List<TempletePlaceHolderVO>> getPlaceHolder() {
        return this.ok(templeteService.getPlaceHolder());
    }

    @PostMapping("/save")
    @ApiOperation(value = "短信模板保存")
    public BaseResult<Object> saveTemplete(@RequestBody TempleteDTO dto) {
        try {
            if (templeteService.saveTemplete(dto)) {
                return ok();
            }
        } catch (BusinessException e) {
            return ok(e.getParams()[0]);
        }
        throw new BusinessException(CommonErrors.CREATE_ERROR);
    }

    @PostMapping("/update")
    @ApiOperation(value = "短信模板修改")
    public BaseResult<Object> update(@RequestBody TempleteDTO dto) {
        templeteService.updateTemplete(dto);
        return ok();
    }

    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询记录")
    public PagedResult<List<TempleteVO>> page(@RequestBody TempletePagedDTO dto) {
        IPage<TempleteVO> result = templeteService.page(dto);
        return ok(result);
    }

    @GetMapping("/get")
    @ApiOperation(value = "通过ID查询详细记录")
    public BaseResult<TempleteVO> getDetail(@RequestParam @ApiParam(value = "模板编码", required = true) String id) {
        return ok(templeteService.getDetail(id));
    }

    @GetMapping("/state_change")
    @ApiOperation(value = "批量启用停用")
    public BaseResult<Object> stateChange(@RequestParam(value = "ids") @ApiParam(value = "模板编码数组", required = true) String ids,
                                          @RequestParam(value = "state") @ApiParam(value = "模板变更状态", required = true) String state) {
        boolean changed =  templeteService.stateChange(ids, state);
        if (!changed) {
            throw new BusinessException(CommonErrors.UPDATE_ERROR);
        }
        return ok();
    }

    @GetMapping("/delete")
    @ApiOperation(value = "通过编码删除记录")
    public BaseResult<Object> delete(@RequestParam @ApiParam(value = "模板编码", required = true) String id) {
        try {
            if (templeteService.removeById(id)) {
                return ok();
            }
        } catch (BusinessException e) {
            return ok(e.getMessage(), CommonErrors.DELETE_ERROR);
        }
        throw new BusinessException(CommonErrors.DELETE_ERROR);
    }

}
