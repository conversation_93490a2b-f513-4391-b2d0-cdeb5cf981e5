package com.swcares.aps.web.bff.service.impl;

import com.swcares.aps.compensation.model.baggage.accident.dto.FindBaggageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.FindBaggageVO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.aps.web.bff.service.BaggageAccidentWebService;
import com.swcares.baseframe.common.base.PagedResult;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * <AUTHOR>
 * @title: BaggageAccidentServiceImpl
 * @projectName aps
 * @description: TODO
 * @date 2022/3/4 13:33
 */
public class BaggageAccidentServiceImpl implements BaggageAccidentWebService {
    @Autowired
    CompensationInfoApi compensationInfoApi;

    @Override
    public PagedResult<List<FindBaggageVO>> findLuggageList(FindBaggageDTO dto) {
        return compensationInfoApi.findBaggageAccidentList(dto);//数据转换一下
    }
}
