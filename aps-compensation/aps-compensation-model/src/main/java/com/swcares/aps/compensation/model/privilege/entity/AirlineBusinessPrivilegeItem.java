package com.swcares.aps.compensation.model.privilege.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> <PERSON>
 * @Classname BusinessPrivilegeItem
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/13 16:09
 * @Version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("business_privilege_item")
public class AirlineBusinessPrivilegeItem extends BaseEntity {
    private Long businessPrivilegeId;
    private String businessTypeCode;
    private boolean grantBankroll;
    private Long tenantId;
}
