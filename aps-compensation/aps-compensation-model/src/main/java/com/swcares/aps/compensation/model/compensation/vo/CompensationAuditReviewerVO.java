package com.swcares.aps.compensation.model.compensation.vo;

import com.swcares.aps.compensation.model.irregularflight.vo.CompensationReviewerInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

import java.util.List;

/**
 * @ClassName：CompensationAuditReviewerVO
 * @Description：补偿单可选审核人
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/5/9 19:24
 * @version： v1.0
 */
@Getter
@ApiModel(value="CompensationAuditReviewerVO对象", description="补偿单可选审核人信息VO")
public class CompensationAuditReviewerVO {

    @ApiModelProperty(value = "当前等待审核的taskId")
    private String taskId;

    @ApiModelProperty(value = "审核人信息列表")
    private List<CompensationReviewerInfoVO> userInfo;

    public CompensationAuditReviewerVO(String taskId, List<CompensationReviewerInfoVO> userInfo) {
        this.taskId = taskId;
        this.userInfo = userInfo;
    }
}
