package com.swcares.aps.compensation.impl.apply.workflow.process;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.swcares.aps.compensation.impl.apply.constants.ApplyConstants;
import com.swcares.aps.compensation.impl.apply.constants.ApplyErrors;
import com.swcares.aps.compensation.impl.apply.enums.ApplyWorkflowNodeBusiTypeEnum;
import com.swcares.aps.compensation.impl.apply.workflow.ApplyWorkflowService;
import com.swcares.aps.component.workflow.NodeNoticeProcess;
import com.swcares.aps.component.workflow.dto.NodeExtVarsDTO;
import com.swcares.aps.component.workflow.dto.NodeNoticeDTO;
import com.swcares.aps.component.workflow.dto.NodeNoticeProcessResult;
import com.swcares.aps.component.workflow.enums.ApsProjectEnum;
import com.swcares.baseframe.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * ClassName：ApplySubmitterNodeProcess <br>
 * Description：申领单Submitter通知节点，传入业务参数至工作流引擎，真正开始整个流程的扭转 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/1/19 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class ApplySubmitterNodeProcess implements NodeNoticeProcess {

    @Autowired
    private ApplyWorkflowService applyWorkflowService;

    @Override
    public NodeNoticeProcessResult process(NodeNoticeDTO noticeDTO) {
        try {
            applyWorkflowService.submitterWorkflow(noticeDTO);
        }catch (Exception e){
            e.printStackTrace();
            log.error("【aps-apply-impl】提交申领单流程出错，noticeDTO:{"+JSONUtil.toJsonStr(noticeDTO)+"}",e);
            throw new BusinessException(ApplyErrors.AUDIT_ERROR,"提交流程异常");
        }
        return new NodeNoticeProcessResult();
    }

    @Override
    public boolean canProcess(NodeNoticeDTO noticeDTO) {
        boolean result=false;
        JSONObject jsonObject = JSONUtil.parseObj(noticeDTO.getExtVars());
        NodeExtVarsDTO extVarsDTO = JSONUtil.toBean(jsonObject, NodeExtVarsDTO.class);
        if(StringUtils.equalsIgnoreCase(extVarsDTO.getProject(), ApsProjectEnum.APPLY_IMPL.getProjectType())
                && StringUtils.equalsIgnoreCase(extVarsDTO.getBusiness(), ApplyConstants.APPLY_WORKFLOW_BUSINESS)
                && StringUtils.equalsIgnoreCase(noticeDTO.getNodeBusinessType(), ApplyWorkflowNodeBusiTypeEnum.SUBMITTER.getType())){
            result=true;
        }

        return result;
    }
}
