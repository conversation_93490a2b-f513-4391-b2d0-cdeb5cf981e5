package com.swcares.aps.staff.bff.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.ground.models.assurance.dto.AssuranceVerifyPaxScanQueryRequest;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderVerifyQueryResultVO;

/**
 * @ClassName：AssuranceOrderVerifyQueryService
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/9/27 10:53
 * @version： v1.0
 */
public interface AssuranceOrderVerifyQueryService {

    IPage<AssuranceOrderVerifyQueryResultVO> assuranceWaitVerifyByScan(AssuranceVerifyPaxScanQueryRequest request);

    IPage<AssuranceOrderVerifyQueryResultVO> assuranceWaitVerifyByIdNo(String idNo);

    Object waitVerifyNum(AssuranceVerifyPaxScanQueryRequest request);

}
