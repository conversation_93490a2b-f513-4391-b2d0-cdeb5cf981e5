<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationRuleRecordMapper">




    <select id="getCompensationRuleByOrderId" resultType="com.swcares.aps.compensation.model.irregularflight.vo.CompensationStandardVO">
        select * from compensation_rule_record
        where 1=1
        AND order_id = #{orderId}
    </select>
</mapper>
