package com.swcares.aps.compensation.model.compensation.vo;

import com.swcares.aps.compensation.model.compensation.dto.CompensationStandardInfoDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationSumMoneyDetailDTO;
import com.swcares.aps.compensation.model.irregularflight.entity.FlightAccidentInfoDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * @ClassName：CompensationAbnormalFlightDetailExtInfo
 * @Description：补偿单详情信息，不正常航班补偿单详情信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 唐康
 * @Date： 2022/4/18 17:20
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationAbnormalFlightDetailExtInfo对象", description="现金补偿单（不正常航班）详情VO")
public class CompensationAbnormalFlightDetailExtInfo extends CompensationDetailExtInfoVO {
    @ApiModelProperty(value = "已补偿人数")
    private Integer hasCompensate;

    @ApiModelProperty(value = "所有补偿数")
    private Integer allCompensate;

    @ApiModelProperty(value = "补偿标准")
    private List<CompensationStandardInfoDTO> compensationStandardInfo;

    @ApiModelProperty(value = "补偿总金额")
    private CompensationSumMoneyDetailDTO compensationSumMoneyDetailDTO;

    /**
     * 不正常航班事故信息
     */
    @ApiModelProperty(value = "现金补偿单（不正常航班）事故信息")
    private FlightAccidentInfoDO flightAccidentInfoDO;
}
