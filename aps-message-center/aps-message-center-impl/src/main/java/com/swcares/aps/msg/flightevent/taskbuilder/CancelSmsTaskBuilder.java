/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：CancelSmsTaskBuilder <br>
 * Package：com.swcares.reaptv.msg.flightevent.taskbuilder <br>
 * Copyright © 2023 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.flightevent.taskbuilder;


import com.swcares.aps.msg.model.dto.SemiAutoSmsTaskDTO;
import com.swcares.aps.msg.model.entity.*;
import com.swcares.aps.msg.model.enums.FlightEventEnum;
import com.swcares.aps.msg.service.TempleteService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.reaptv.msg.flightevent.taskbuilder.CancelSmsTaskBuilder <br>
 * Description：航班取消事件的短信任务创建工具的抽象父类
 * @author: 王翼 <br>
 * @CreatedAt: 2023/1/3 13:43 <br>
 * @version:
 */
public abstract class CancelSmsTaskBuilder extends AbstractSemiSmsTaskBuilder{


    static final String CANCEL_ALL_SEG_PROTECTED_NOT_DEGRAD = "CANCEL_ALL_SEG_PROTECTED_NOT_DEGRAD";
    static final String CANCEL_ALL_SEG_PORTECTED_DEGRAD = "CANCEL_ALL_SEG_PORTECTED_DEGRAD";
    static final String CANCEL_ALL_SEG_NOT_PROTECTED = "CANCEL_ALL_SEG_NOT_PROTECTED";
    static final String CANCEL_ALL_SEG_NOT_SUPPLEMENT = "CANCEL_ALL_SEG_NOT_SUPPLEMENT";


    static final String CANCEL_LAST_SEG_PROTECTED_NOT_DEGRAD = "CANCEL_LAST_SEG_PROTECTED_NOT_DEGRAD";
    static final String CANCEL_LAST_SEG_PORTECTED_DEGRAD = "CANCEL_LAST_SEG_PORTECTED_DEGRAD";
    static final String CANCEL_LAST_SEG_NOT_PROTECTED = "CANCEL_LAST_SEG_NOT_PROTECTED";
    static final String CANCEL_LAST_SEG_NOT_SUPPLEMENT = "CANCEL_LAST_SEG_NOT_SUPPLEMENT";
    @Autowired
    private TempleteService templeteService;

    @Override
    public List<SemiAutoSmsTaskDTO> buildSemiAutoTask(FlightEvent flightEvent, List<FlightEventPassenger> passengers) {
        Map<String, List<FlightEventPassenger>> groupedPassenger = groupPassenger(flightEvent, passengers);
        List<SemiAutoSmsTaskDTO> result = new ArrayList<>();

        for(Map.Entry<String, List<FlightEventPassenger>> group : groupedPassenger.entrySet()){
            buildSingleTask(flightEvent, group.getKey(), group.getValue(), result);
        }
        
        return result;

    }

    @Override
    FlightEventEnum getFlightEventEnum() {
        return FlightEventEnum.CANCEL;
    }

    private void buildSingleTask(FlightEvent flightEvent, String templateId, List<FlightEventPassenger> passengers, List<SemiAutoSmsTaskDTO> result){
        if (passengers == null || passengers.isEmpty()){
            return;
        }
        SemiAutoSmsTaskDTO semiAutoSmsTaskDTO = new SemiAutoSmsTaskDTO();
        Templete templete = templeteService.getById(templateId);
        SmsTaskInfo taskInfo = createSmsTaskInfo(flightEvent, templateId, templete.getTempleteContent(), passengers);
        semiAutoSmsTaskDTO.setTaskInfo(taskInfo);


        List<SmsTaskDetailInfo> taskDetailInfos = createTaskDetailInfos(flightEvent, passengers);
        semiAutoSmsTaskDTO.setTaskDetailInfoList(taskDetailInfos);

        result.add(semiAutoSmsTaskDTO);
    }
    
    abstract Map<String, List<FlightEventPassenger>> groupPassenger(FlightEvent flightEvent, List<FlightEventPassenger> passengers);
}
