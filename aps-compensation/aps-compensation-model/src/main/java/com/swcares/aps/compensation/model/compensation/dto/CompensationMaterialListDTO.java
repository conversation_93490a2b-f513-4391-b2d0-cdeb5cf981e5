package com.swcares.aps.compensation.model.compensation.dto;


import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：CompensationOrderCommandsController
 * @Description：箱包补偿单查询封装条件DTO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 唐康
 * @Date： 2022/4/13 15:36
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationMaterialListDTO对象", description="箱包补偿单查询封装条件DTO")
public class CompensationMaterialListDTO extends PagedDTO {
    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期（开始）")
    private String startDate;

    @ApiModelProperty(value = "航班日期（结束）")
    private String endDate;

    @ApiModelProperty(value = "服务航站")
    private List<String> serviceCity;

    @ApiModelProperty(value = "补偿单状态")
    private String status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "当前操作人Id（前端不传）")
    private String userId;

    @ApiModelProperty(value = "（前端不用传）逗号分隔后的补偿单状态数组 ")
    private String[] StatusSplits;
}
