package com.swcares.aps.basic.data.businessimpl.model.vo;

import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.ObjectUtils;

import java.util.*;

/**
 * @ClassName：FlightFindVO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/7/26 15:58
 * @version： v1.0
 */
public class FlightFindVO {

    @ApiModelProperty(value = "航班ID")
    private String flightId;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "航班类型")
    private String flightType;

    @ApiModelProperty(value = "起飞机场")
    private String departPort;

    @ApiModelProperty(value = "到达机场")
    private String arrivalPort;

    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    @ApiModelProperty(value = "预计起飞时间")
    private String etd;

    @ApiModelProperty(value = "实际起飞时间")
    private String atd;

    @ApiModelProperty(value = "机号")
    private String acReg;

    @ApiModelProperty(value = "机型")
    private String acType;

    @ApiModelProperty(value = "实际到达时间")
    private String ata;

    @ApiModelProperty(value = "计划到达时间")
    private String sta;

    @ApiModelProperty(value = "预计到达时间")
    private String eta;

    @ApiModelProperty(value = "订座数")
    private String bookSeat;

    @ApiModelProperty(value = "总座数")
    private String seatNum;

    @ApiModelProperty(value = "航班状态")
    private String flightStatus;

    @ApiModelProperty(value = "延误原因（对内对外）")
    private List<Map<String,String>> delayReason;

    @ApiModelProperty(value = "航班状态 D-延误|C-取消")
    private String flightRunState;

    @ApiModelProperty(value = "备降/返航标志,RC 返航 VC 备降")
    private String flgVr;

    @ApiModelProperty(value = "不正常对内原因")
    private String innerReason;

    @ApiModelProperty(value = "不正常对外原因")
    private String exterReason;

    @ApiModelProperty(value = "起飞三字码")
    private String pod;

    @ApiModelProperty(value = "到达三字码")
    private String poa;

    @ApiModelProperty(value = "航段三字码")
    private String segment;

    @ApiModelProperty(value = "航段中文")
    private String segmentCh;

    @ApiModelProperty(value = "经停站")
    private Set<String> stopoverStation;


    public String getFlightRunState() {
        return flightRunState;
    }

    public void setFlightRunState(String flightRunState) {
        this.flightRunState = flightRunState;
    }

    public String getFlightId() {
        return flightId;
    }

    public void setFlightId(String flightId) {
        this.flightId = flightId;
    }

    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo;
    }

    public String getFlightDate() {
        return flightDate;
    }

    public void setFlightDate(String flightDate) {
        this.flightDate = flightDate;
    }

    public String getFlightType() {
        return flightType;
    }

    public void setFlightType(String flightType) {
        this.flightType = flightType;
    }

    public String getDepartPort() {
        return departPort;
    }

    public void setDepartPort(String departPort) {
        this.departPort = departPort;
    }

    public String getArrivalPort() {
        return arrivalPort;
    }

    public void setArrivalPort(String arrivalPort) {
        this.arrivalPort = arrivalPort;
    }

    public String getStd() {
        return std;
    }

    public void setStd(String std) {
        this.std = std;
    }

    public String getEtd() {
        return etd;
    }

    public void setEtd(String etd) {
        this.etd = etd;
    }

    public String getAtd() {
        return atd;
    }

    public void setAtd(String atd) {
        this.atd = atd;
    }

    public String getAcReg() {
        return acReg;
    }

    public void setAcReg(String acReg) {
        this.acReg = acReg;
    }

    public String getAcType() {
        return acType;
    }

    public void setAcType(String acType) {
        this.acType = acType;
    }

    public String getAta() {
        return ata;
    }

    public void setAta(String ata) {
        this.ata = ata;
    }

    public String getSta() {
        return sta;
    }

    public void setSta(String sta) {
        this.sta = sta;
    }

    public String getEta() {
        return eta;
    }

    public void setEta(String eta) {
        this.eta = eta;
    }

    public String getBookSeat() {
        return bookSeat;
    }

    public void setBookSeat(String bookSeat) {
        this.bookSeat = bookSeat;
    }

    public String getSeatNum() {
        return seatNum;
    }

    public void setSeatNum(String seatNum) {
        this.seatNum = seatNum;
    }

    public String getFlightStatus() {
        return flightStatus;
    }

    public void setFlightStatus(String flightStatus) {
        this.flightStatus = flightStatus;
    }

    public List<Map<String, String>> getDelayReason() {
        List<Map<String, String>> mapList = new ArrayList<>();
        HashMap<String, String> map = new HashMap<>();
        String r1 = this.innerReason;
        String r2 = this.exterReason;
        StringBuffer r = new StringBuffer();
        if(ObjectUtils.isNotEmpty(this.flightRunState )&& (ObjectUtils.isNotEmpty(this.innerReason) ||ObjectUtils.isNotEmpty(this.exterReason))){
            if (ObjectUtils.isEmpty(r1)) {
                r = r.append(r2);
            }else if(ObjectUtils.isEmpty(r2)){
                r = r.append(r1);
            }else{
                r = r.append(r1).append("#").append(r2);
            }
            map.put(this.flightRunState,r.toString());
            mapList.add(map);
        }
        else if(ObjectUtils.isNotEmpty(this.flgVr ) && (ObjectUtils.isNotEmpty(this.innerReason) ||ObjectUtils.isNotEmpty(this.exterReason))) {
            if (ObjectUtils.isEmpty(r1)) {
                r = r.append(r2);
            }else if(ObjectUtils.isEmpty(r2)){
                r = r.append(r1);
            }else{
                r = r.append(r1).append("#").append(r2);
            }
            map.put(this.flgVr,r.toString());
            mapList.add(map);
        }
        if(ObjectUtils.isNotEmpty(mapList)){
            this.delayReason = mapList;
        }
        return delayReason;
    }

    public void setDelayReason() {
    /*    List<Map<String, String>> mapList = new ArrayList<>();
        HashMap<String, String> map = new HashMap<>();
        if(this.flightRunState != null){
            map.put(this.flightRunState,this.innerReason+"#"+this.exterReason);
        }
        else {
            map.put(this.flgVr,this.innerReason+"#"+this.exterReason);
        }
        mapList.add(map);
        this.delayReason = mapList;*/
        this.getDelayReason();
    }

    public String getPod() {
        return pod;
    }

    public void setPod(String pod) {
        this.pod = pod;
    }

    public String getPoa() {
        return poa;
    }

    public void setPoa(String poa) {
        this.poa = poa;
    }

    public String getSegment() {
        return segment;
    }

    public void setSegment(String segment) {
        this.segment = segment;
    }

    public String getSegmentCh() {
        return segmentCh;
    }

    public void setSegmentCh(String segmentCh) {
        this.segmentCh = segmentCh;
    }

    public Set<String> getStopoverStation() {
        return stopoverStation;
    }

    public void setStopoverStation(Set<String> stopoverStation) {
        this.stopoverStation = stopoverStation;
    }

    public String getFlgVr() {
        return flgVr;
    }

    public void setFlgVr(String flgVr) {
        this.flgVr = flgVr;
    }

    public String getInnerReason() {
        return innerReason;
    }

    public void setInnerReason(String innerReason) {
        this.innerReason = innerReason;
    }

    public String getExterReason() {
        return exterReason;
    }

    public void setExterReason(String exterReason) {
        this.exterReason = exterReason;
    }
}
