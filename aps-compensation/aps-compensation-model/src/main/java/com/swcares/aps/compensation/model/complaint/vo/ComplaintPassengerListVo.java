package com.swcares.aps.compensation.model.complaint.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "ComplaintPassengerListVO对象", description = "新增旅客投诉事故单旅客列表")
@SecretInfoEntity
public class ComplaintPassengerListVo {

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "证件类型")
    private String idType;

    @ApiModelProperty(value = "证件号码")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "航段")
    private String segment;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "补偿次数")
    private Integer frequency;

    @ApiModelProperty(value = "主舱位")
    private String mainClass;

    @ApiModelProperty(value = "子舱位")
    private String subClass;

    @ApiModelProperty(value = "值机")
    private String checkIn;

    @ApiModelProperty(value = "取消")
    private String isCancel;

    @ApiModelProperty(value = "取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String cancelTime;

    @ApiModelProperty(value = "购票日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String tktIssueTime;

    @ApiModelProperty(value = "pnr")
    private String pnr;

    @ApiModelProperty(value = "儿童标识0否1是")
    private String isChild;

    @ApiModelProperty(value = "是否携带婴儿标识N否Y是")
    private String isBaby;
}
