########################################## nacos配置  ###################################
spring:
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        namespace: local
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        refresh-enabled: true
        file-extension: yml
        namespace: local
        group: local
        shared-configs:
          - data-id: application-bash-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            refresh: true
            group: local
logging:
  config: classpath:logback.xml
  level:
    com.swcares: debug
    org.springframework: debug