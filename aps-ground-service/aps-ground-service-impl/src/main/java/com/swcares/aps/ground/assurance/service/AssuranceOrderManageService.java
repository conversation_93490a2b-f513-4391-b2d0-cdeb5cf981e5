package com.swcares.aps.ground.assurance.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderChangeStatusCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderListRequest;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderSaveCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceWorkflowAuditCommand;
import com.swcares.aps.ground.models.assurance.dto.*;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderServiceListVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderListVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderServiceDetailsVo;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderServiceVerifyUserDetailVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceWorkflowAuditResultVO;

import java.util.List;

/**
 * @ClassName：AssuranceManageService
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/21 16:24
 * @version： v1.0
 */
public interface AssuranceOrderManageService {

    /**
     * @title pageQueryOrderList
     * @description 分页查询列表
     * <AUTHOR>
     * @date 2024/9/18 14:26
     * @param request
     * @return
     */
    IPage<AssuranceOrderListVO> pageQueryOrderList(AssuranceOrderListRequest request);

    /**
     * @title saveOrUpdateAssuranceOrder
     * @description 保存或则编辑保障单信息
     * <AUTHOR>
     * @date 2024/8/22 14:43
     * @param command
     * @return
     */
    String saveOrUpdateAssuranceOrder(AssuranceOrderSaveCommand command);

    /**
     * @title changeAssuranceOrderStatus
     * @description 变跟保障单状态
     * <AUTHOR>
     * @date 2024/8/22 14:43
     * @param command
     * @return
     */
    Object changeAssuranceOrderStatus(AssuranceOrderChangeStatusCommand command);

    /**
     * @title processAssuranceOrderWorkflow
     * @description 审核操作（审核通过、审核驳回、审核拒绝）
     * <AUTHOR>
     * @date 2024/8/23 9:54
     * @param command
     * @return
     */
    AssuranceWorkflowAuditResultVO processAssuranceOrderWorkflow(AssuranceWorkflowAuditCommand command);

    /**
     * @title saveAuditReviewer
     * @description 保存审核人信息
     * <AUTHOR>
     * @date 2024/10/30 11:55
     * @param command
     * @return
     */
    void saveAuditReviewer(AssuranceOrderReviewerSaveCommand command);
    /**
     * @title pageQueryOrderServiceList
     * @description H5查询保障单列表
     * <AUTHOR>
     * @date 2024/9/27 14:13
     * @param request
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.ground.models.assurance.vo.AssuranceOrderH5ListVO>
     */
    IPage<AssuranceOrderServiceListVO> pageQueryOrderServiceList(AssuranceOrderServiceListRequest request);

    /**
     * @title getOrderServiceDetailsList
     * @description H5查询保障单详情
     * <AUTHOR>
     * @date 2024/9/27 14:13
     * @param orderServiceId
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.ground.models.assurance.vo.AssuranceOrderH5ListVO>
     */
    AssuranceOrderServiceDetailsVo getOrderServiceDetail(String orderServiceId);

    /**
     * @title overTimeAssuranceOrder
     * @description 保障单逾期
     * <AUTHOR>
     * @date 2024/9/29 16:01
     * @return
     */
    void overTimeAssuranceOrder();

    /**
     * @title finishAssuranceOrder
     * @description 完成处理保障单
     * <AUTHOR>
     * @date 2024/12/9 10:20
     * @return
     */
    void finishAssuranceOrder();

    /***
     * @title getOrderServiceVerifyDetailList
     * @description 返回旅客维度核销详情
     * <AUTHOR>
     * @date 2024/9/24 16:55
     * @param request
     * @return java.util.List<com.swcares.aps.ground.models.assurance.vo.AssuranceOrderServiceVerifyPaxDetailVO>
     */
    IPage<AssuranceOrderServiceVerifyUserDetailVO> getOrderServiceVerifyDetailList(AssuranceVerifyQueryRequest request);

    /**
     * @title getDistinctServiceName
     * @description 获取当前租户所有可提供服务的服务名称
     * <AUTHOR>
     * @date 2024/10/10 16:27
     * @return
     */
    List<String> getDistinctServiceName();
}
