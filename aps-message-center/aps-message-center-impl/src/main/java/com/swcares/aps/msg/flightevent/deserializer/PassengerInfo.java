/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：PassengerResults <br>
 * Package：com.swcares.reaptv.msg.flighteventanalyzer <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.flightevent.deserializer;

import lombok.Data;

/**
 * ClassName：com.swcares.reaptv.msg.flighteventanalyzer.PassengerResults <br>
 * Description：广分发送的航班变更事件中的旅客信息
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/3 21:01 <br>
 * @version:
 */
@Data
public class PassengerInfo {
    //pnr
    private String pnrCode;

    //旅客在pnr中的序号
    private String sequenceNumber;

    //旅客的姓名
    private String passengerName;

    //客票信息（客票号）
    private String eTinfo;

    //联系方式
    private String telNumber;

    //是否要客（Y/N)
    private String isVVIP;

    //是否团队旅客（Y/N)
    private String isGroup;
    
    //是否候补旅客（Y/N)
    private String isStandby;
    
    //原舱位
    private String cabinType;
    
    //新舱位
    private String newCabinType;
    
    //目标航班号
    private String targetFlightNumber;
    
    //目标航段
    private String targetSegment;
    
    //目标航段日期
    private String targetSegmentDate;
    
    //目标航班预计起飞时间
    private String targetDepTime;
    
    //目标航班预计到达时间
    private String targetDesTime;
}
