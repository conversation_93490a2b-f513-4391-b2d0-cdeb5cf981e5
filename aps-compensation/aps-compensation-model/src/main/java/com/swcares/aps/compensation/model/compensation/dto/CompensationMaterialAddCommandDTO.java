package com.swcares.aps.compensation.model.compensation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName：CompensationMaterialAddCommandDTO
 * @Description：实物补偿单创建
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 15:53
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationMaterialAddCommandDTO对象", description="实物补偿单创建参数")
public class CompensationMaterialAddCommandDTO extends CompensationAddAndEditCommandDTO implements CompensationAddCommand{

    /**
     * 实物
     */
    @ApiModelProperty(value = "实物信息")
    @NotNull
    private List<AddMaterialCompensationDTO> materials;

}
