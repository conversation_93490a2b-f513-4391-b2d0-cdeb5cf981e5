<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.usercenter.impl.recentlyused.mapper.RecentlyUsedMapper">

    <select id="selectRecentlyUsedResource" resultType="com.swcares.aps.usercenter.model.recentlyused.vo.RecentlyUsedResourceVO" parameterType="string">
        select *
        from RECENTLY_USED ru
        <where>
            <if test="platform != null and platform !=''">
                and ru.PLATFORM = #{platform}
            </if>

            <if test="name != null and name != ''">
                and ru.NAME = #{name}
            </if>
        </where>

        ORDER BY ru.UPDATED_TIME DESC,
                 ru.ID DESC
    </select>
</mapper>
