<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.msg.mapper.MsgMessageTaskSentCountMapper">

    <sql id="getSmsTaskSentCountMySql">
        SELECT
        DISTINCT  TSC.TASK_ID taskId,
        mt.SEND_START_TIME  sentTime,

        mt.FLIGHT_SCHEDULE flightSchedule,
        mt.TASK_TYPE taskType,
        mt.SMS_TYPE smsType ,
        mt.BUSINESS_TYPE businessType,
        mt.ADJUST_REASON adjustReason,
        mt.ADJUST_TYPE  adjustType,
        mt.PEOPLE_COUNT flightChangeNum ,
        mt.REFUNDED_COUNT refundedCount,
        mt.EXCHANGED_COUNT exchangedCount,
        mt.OPEN_COUNT openCount,
        mt.USED_COUNT usedCount ,
        mt.TWO_CLASS_PAX_COUNT twoClassPaxCount,
        mt.FREQUENT_COUNT frequentCount,
        mt.TEAM_COUNT teamCount,
        mt.ADULT_COUNT adultCount,
        mt.CHILD_COUNT childCount,
        mt.BABY_COUNT babyCount,
        tsc.READY_COUNT readyCount,
        tsc.SENDING_COUNT	sendingCount,
        tsc.PART_FAIL_COUNT	partFailCount,
        tsc.FAIL_COUNT	failCount,
        tsc.SUCCESS_COUNT	successCount,
        st.segment,
        st.segmentCh,
        st.FLIGHT_NUMBER,
        st.flightDateStart,
        st.flightDateEnd


        from MSG_MESSAGE_TASK_SENT_COUNT tsc
        LEFT JOIN MSG_MESSAGE_TASK mt on TSC.TASK_ID = MT.TASK_ID
        LEFT JOIN
        (
        select mtd.TASK_ID,
        max(mtd.FLIGHT_DATE) flightDateEnd,min(mtd.FLIGHT_DATE) flightDateStart,
        group_concat(DISTINCT mtd.FLIGHT_NUMBER) FLIGHT_NUMBER,
        group_concat(DISTINCT CONCAT(CONCAT(mtd.ORG, '-') , mtd.DST) segment,
        group_concat(CONCAT(CONCAT(CONCAT(GET_CITY_NAME(mtd.ORG),mtd.ORG), '-' ), CONCAT(GET_CITY_NAME(mtd.DST),  mtd.DST)) segmentCh
        from MSG_MESSAGE_TASK_DETAIL mtd
        GROUP BY mtd.TASK_ID
        ) st on TSC.TASK_ID = st.TASK_ID
        <!-- &#45;&#45;用于筛选航班日期-->
        <if test="dto.flightDateStart !=null and dto.flightDateStart !='' and dto.flightDateEnd !=null and dto.flightDateEnd !='' ">
            LEFT JOIN (
            select DISTINCT TASK_ID
            from MSG_MESSAGE_TASK_DETAIL
            where FLIGHT_DATE BETWEEN DATE_FORMAT(#{dto.flightDateStart}, 'yyyy-mm-dd') and DATE_FORMAT(#{dto.flightDateEnd}, 'yyyy-mm-dd')
            ) fd on TSC.TASK_ID = fd.TASK_ID
        </if>
        where 1=1
        and TSC."ID" is not NULL
        and mt.TASK_TYPE  is not NULL
        and mt.SMS_TYPE  is not NULL
        <if test="dto.flightNumber !=null and dto.flightNumber !=''">
            AND	st.FLIGHT_NUMBER like '%' || #{dto.flightNumber} || '%'
        </if>
        <if test="dto.org !=null and dto.org !=''">
            AND	st.segment like '%' || #{dto.org} || '-%'
        </if>
        <if test="dto.dst !=null and dto.dst !=''">
            AND	st.segment like '%-' || #{dto.dst} || '%'
        </if>
        <if test="dto.sentDateStart !=null and dto.sentDateStart !='' and dto.sentDateEnd !=null and dto.sentDateEnd !='' ">
            AND mt.SEND_START_TIME BETWEEN DATE_FORMAT(#{dto.sentDateStart}, 'yyyy-mm-dd') and DATE_FORMAT(#{dto.sentDateEnd}, 'yyyy-mm-dd')
        </if>
        <if test="dto.flightDateStart !=null and dto.flightDateStart !='' and dto.flightDateEnd !=null and dto.flightDateEnd !='' ">
            AND fd.TASK_ID IS NOT NULL
        </if>
        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.flightSchedule)">
            AND (
            <foreach collection="dto.flightSchedule" index="index" item="item" separator=" or " open="(" close=")">
                mt.FLIGHT_SCHEDULE  LIKE '%' || #{item} || '%'
            </foreach>
            )
        </if>
        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.taskType)">
            AND mt.TASK_TYPE IN
            <foreach collection="dto.taskType" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.smsType)">
            AND	mt.SMS_TYPE IN
            <foreach collection="dto.smsType" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.businessTypes)">
            AND	mt.BUSINESS_TYPE IN
            <foreach collection="dto.businessTypes" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.adjustType)">
            AND mt.ADJUST_TYPE IN
            <foreach collection="dto.adjustType" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.adjustReason !=null and dto.adjustReason !=''">
            AND	mt.ADJUST_REASON like '%' || #{dto.adjustReason} || '%'
        </if>

        order by mt.SEND_START_TIME desc
    </sql>

    <sql id="getSmsTaskSentCountSql">
        SELECT
        DISTINCT  TSC.TASK_ID taskId,
        mt.SEND_START_TIME  sentTime,

        mt.FLIGHT_SCHEDULE flightSchedule,
        mt.TASK_TYPE taskType,
        mt.SMS_TYPE smsType ,
        mt.BUSINESS_TYPE businessType,
        mt.ADJUST_REASON adjustReason,
        mt.ADJUST_TYPE  adjustType,
        mt.PEOPLE_COUNT flightChangeNum ,
        mt.REFUNDED_COUNT refundedCount,
        mt.EXCHANGED_COUNT exchangedCount,
        mt.OPEN_COUNT openCount,
        mt.USED_COUNT usedCount ,
        mt.TWO_CLASS_PAX_COUNT twoClassPaxCount,
        mt.FREQUENT_COUNT frequentCount,
        mt.TEAM_COUNT teamCount,
        mt.ADULT_COUNT adultCount,
        mt.CHILD_COUNT childCount,
        mt.BABY_COUNT babyCount,
        tsc.READY_COUNT readyCount,
        tsc.SENDING_COUNT	sendingCount,
        tsc.PART_FAIL_COUNT	partFailCount,
        tsc.FAIL_COUNT	failCount,
        tsc.SUCCESS_COUNT	successCount,
        st.segment,
        decode(length(st.segmentCh),1300, st.segmentCh||'...('||st.segmentCount||')', st.segmentCh) segmentCh,
        st.FLIGHT_NUMBER,
        st.flightDateStart,
        st.flightDateEnd


        from MSG_MESSAGE_TASK_SENT_COUNT tsc
        LEFT JOIN MSG_MESSAGE_TASK mt on TSC.TASK_ID = MT.TASK_ID
        LEFT JOIN
        (
        select mtd.TASK_ID,max(mtd.FLIGHT_DATE) flightDateEnd,min(mtd.FLIGHT_DATE) flightDateStart,
        to_char(wm_concat(DISTINCT mtd.FLIGHT_NUMBER)) FLIGHT_NUMBER,
        to_char(wm_concat(DISTINCT mtd.ORG || '-' || mtd.DST)) segment,
        to_char(substr(wm_concat(DISTINCT TO_CHAR(GET_CITY_NAME(mtd.ORG)) || mtd.ORG || '-' || TO_CHAR(GET_CITY_NAME(mtd.DST)) ||  mtd.DST),0,1300)) segmentCh,
        count(DISTINCT mtd.ORG || '-' || mtd.DST) segmentCount
        from MSG_MESSAGE_TASK_DETAIL mtd
        GROUP BY mtd.TASK_ID
        ) st on TSC.TASK_ID = st.TASK_ID
        <!-- &#45;&#45;用于筛选航班日期-->
        <if test="dto.flightDateStart !=null and dto.flightDateStart !='' and dto.flightDateEnd !=null and dto.flightDateEnd !='' ">
            LEFT JOIN (
            select DISTINCT TASK_ID
            from MSG_MESSAGE_TASK_DETAIL
            where FLIGHT_DATE BETWEEN TO_DATE(#{dto.flightDateStart}, 'yyyy-mm-dd') and TO_DATE(#{dto.flightDateEnd}, 'yyyy-mm-dd')
            ) fd on TSC.TASK_ID = fd.TASK_ID
        </if>
        where 1=1
        and TSC."ID" is not NULL
        <if test="dto.flightNumber !=null and dto.flightNumber !=''">
            AND	st.FLIGHT_NUMBER like '%' || #{dto.flightNumber} || '%'
        </if>
        <if test="dto.org !=null and dto.org !=''">
            AND	st.segment like '%' || #{dto.org} || '-%'
        </if>
        <if test="dto.dst !=null and dto.dst !=''">
            AND	st.segment like '%-' || #{dto.dst} || '%'
        </if>
        <if test="dto.sentDateStart !=null and dto.sentDateStart !='' and dto.sentDateEnd !=null and dto.sentDateEnd !='' ">
            AND TO_CHAR(mt.SEND_START_TIME, 'yyyy-mm-dd') BETWEEN #{dto.sentDateStart} and #{dto.sentDateEnd}
        </if>
        <if test="dto.flightDateStart !=null and dto.flightDateStart !='' and dto.flightDateEnd !=null and dto.flightDateEnd !='' ">
            AND fd.TASK_ID IS NOT NULL
        </if>
        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.flightSchedule)">
            AND (
            <foreach collection="dto.flightSchedule" index="index" item="item" separator=" or " open="(" close=")">
                mt.FLIGHT_SCHEDULE  LIKE '%' || #{item} || '%'
            </foreach>
            )
        </if>
        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.taskType)">
            AND mt.TASK_TYPE IN
            <foreach collection="dto.taskType" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.smsType)">
            AND	mt.SMS_TYPE IN
            <foreach collection="dto.smsType" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>

        </if>

        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.businessTypes)">
            AND	mt.BUSINESS_TYPE IN
            <foreach collection="dto.businessTypes" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.adjustType)">
            AND mt.ADJUST_TYPE IN
            <foreach collection="dto.adjustType" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.adjustReason !=null and dto.adjustReason !=''">
            AND	mt.ADJUST_REASON like '%' || #{dto.adjustReason} || '%'
        </if>

        order by mt.SEND_START_TIME desc
    </sql>

    <select id="getSmsTaskSentCountList" databaseId="oracle" resultType="com.swcares.aps.msg.model.vo.SmsPersonTimeStatisticsResultVO">
        <include refid="getSmsTaskSentCountSql"></include>
    </select>

    <select id="getSmsTaskSentCountPage" databaseId="oracle" resultType="com.swcares.aps.msg.model.vo.SmsPersonTimeStatisticsResultVO">
        <include refid="getSmsTaskSentCountSql"></include>
    </select>

    <select id="getSmsTaskSentCountList" databaseId="mysql" resultType="com.swcares.aps.msg.model.vo.SmsPersonTimeStatisticsResultVO">
        <include refid="getSmsTaskSentCountMySql"></include>
    </select>

    <select id="getSmsTaskSentCountPage" databaseId="mysql" resultType="com.swcares.aps.msg.model.vo.SmsPersonTimeStatisticsResultVO">
        <include refid="getSmsTaskSentCountMySql"></include>
    </select>
</mapper>
