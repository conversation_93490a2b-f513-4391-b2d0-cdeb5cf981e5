package com.swcares.aps.compensation.model.compensation.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CompensationBaseLuggageVO
 * @Description：现金补偿单最基本的信息，所有类型补偿单都有的基本信息+现金有的最基本信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/9 12:43
 * @version： v1.0
 */
@Data
public class CompensationBaseExtCashVO extends CompensationBaseExtInfoVO{
      @ApiModelProperty(value = "创建时间（日期格式）")
      private String compensationCreatedTime;


//    @ApiModelProperty(value = "异常航班现金补偿VO对象")
//    private List<CompensationAbnormalFlightExtCashVO> compensationAbnormalFlightExtCashVOS;
//
//
//    @ApiModelProperty(value = "异常行李现金补偿VO对象")
//    private List<CompensationAbnormalBaggageExtCashVO> compensationAbnormalBaggageExtCashVOS;
}
