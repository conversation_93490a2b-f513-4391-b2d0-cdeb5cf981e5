package com.swcares.aps.compensation.model.complaint.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ComplaintAccidentQueryVo对象", description = "事故单列表查询返回")
public class ComplaintAccidentQueryVo {

    @ApiModelProperty(value = "事故单主键")
    private String id;

    @ApiModelProperty(value = "旅客投诉事故单号")
    private String accidentId;

    @ApiModelProperty(value = "事故类型")
    private String accidentSubType;

    @ApiModelProperty(value = "事故单状态")
    private String accidentStatus;

    @ApiModelProperty(value = "事故单来源",example = "全部：0，机场：1，航司：2")
    private String accidentSource;

    @ApiModelProperty(value = "归属航司")
    private String belongAirline;

    @ApiModelProperty(value = "事故类型")
    private String accidentType;

    @ApiModelProperty(value = "原因类型")
    private String reasonType;

    @ApiModelProperty(value = "航班日期",example = "2024-01-01")
    private String flightDate;

    @ApiModelProperty(value = "航班号",example = "CA8888")
    private String flightNo;

    @ApiModelProperty(value = "航段",example = "CTU-PEK")
    private String segment;

    @ApiModelProperty(value = "补偿单数量")
    private String quantity;

    @ApiModelProperty(value = "创建人")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;
}
