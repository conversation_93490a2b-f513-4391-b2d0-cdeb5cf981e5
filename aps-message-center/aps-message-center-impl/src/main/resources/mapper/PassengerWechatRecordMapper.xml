<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.msg.mapper.PassengerWechatRecordMapper">
    <sql id="passengerWechatRecordDetail">
        SELECT
        ID,
        TITLE,
        CONTENT,
        BUSINESS_TYPE,
        SENT_TIME,
        REASON,
        PASSENGER_NAME,
        OPENID,
        PASSENGER_ID_NO,
        PASSENGER_PHONE,
        FLIGHT_DATE,
        FLIGHT_NUMBER,
        STATUS,
        SENDER_ACCOUNT,
        SENDER_INFO,
        DELETED,
        CREATED_TIME,
        CREATED_BY,
        UPDATED_TIME,
        UPDATED_BY
        FROM
        MSG_PASSENGER_WECHAT_RECORD MPWR
    </sql>
    <sql id="searchCriteriaOracle">
        WHERE MPWR.DELETED = 0
        <if test="dto.passengerName != null and dto.passengerName != '' ">
            AND MPWR.PASSENGER_NAME = #{dto.passengerName}
        </if>
        <if test="dto.passengerPhone != null and dto.passengerPhone != '' ">
            AND MPWR.PASSENGER_PHONE = #{dto.passengerPhone}
        </if>
        <if test="dto.passengerIdNo != null and dto.passengerIdNo != ''">
            AND MPWR.PASSENGER_ID_NO = #{dto.passengerIdNo}
        </if>
        <if test="dto.title != null and dto.title != '' ">
            AND MPWR.TITLE LIKE '%'||#{dto.title}||'%'
        </if>
        <if test="dto.flightNumber != null and dto.flightNumber != ''">
            AND MPWR.FLIGHT_NUMBER = #{dto.flightNumber}
        </if>
        <if test="dto.flightDateStart != null and dto.flightDateStart != ''">
            AND MPWR.FLIGHT_DATE <![CDATA[ >= ]]> to_date(#{dto.flightDateStart}, 'yyyy-MM-dd')
        </if>
        <if test="dto.flightDateEnd != null and dto.flightDateEnd != ''">
            AND MPWR.FLIGHT_DATE <![CDATA[ < ]]> (to_date(#{dto.flightDateEnd}, 'yyyy-MM-dd')+1)
        </if>
        <if test="dto.status != null">
            AND MPWR.STATUS = #{dto.status}
        </if>
        <if test="dto.businessTypes != null and dto.businessTypes.size() >0">
            AND MPWR.BUSINESS_TYPE IN
            <foreach collection="dto.businessTypes" index="index" item="businessType" separator="," open="(" close=")">
                #{businessType}
            </foreach>
        </if>
        <if test="dto.sentDateStart != null and dto.sentDateStart != ''">
            AND MPWR.SENT_TIME <![CDATA[ >= ]]> to_date(#{dto.sentDateStart}, 'yyyy-MM-dd')
        </if>
        <if test="dto.sentDateEnd != null and dto.sentDateEnd != '' ">
            AND MPWR.SENT_TIME <![CDATA[ < ]]> (to_date(#{dto.sentDateEnd}, 'yyyy-MM-dd')+1)
        </if>
    </sql>
    <select id="getPassengerWechatRecordPage" resultType="com.swcares.aps.msg.model.entity.PassengerWechatRecord" databaseId="oracle">
        <include refid="passengerWechatRecordDetail"></include>
        <include refid="searchCriteriaOracle"></include>
        ORDER BY SENT_TIME DESC
    </select>

    <select id="getByIds" resultType="com.swcares.aps.msg.model.entity.PassengerWechatRecord">
        <include refid="passengerWechatRecordDetail"></include>
        WHERE
        MPWR.ID IN
        <foreach collection="idList" index="index" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>

    <select id="messageStatusStatics" resultType="com.swcares.aps.msg.model.dto.MessageStatusStaticsDTO">
        select status, count(id) recordCount
        from MSG_PASSENGER_WECHAT_RECORD MPWR
        <include refid="searchCriteriaOracle"></include>
        group by status
    </select>
</mapper>
