package com.swcares.aps.compensation.model.irregularflight.vo;

import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: ChoosePaxInfoVO对象
 * @projectName aps
 * @description: 已选择的旅客信息vo对象
 * @date 2021/11/10 15:33
 */
@Data
@SecretInfoEntity
public class ChoosePaxInfoVO {
    @ApiModelProperty(value = "旅客ID")
    private String paxId;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "证件类型")
    private String idType;

    @ApiModelProperty(value = "证件号")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "性别C儿童M男F女")
    private String sex;

    @ApiModelProperty(value = "儿童标识0否1是")
    private String isChild;

    @ApiModelProperty(value = "是否携带婴儿标识N否Y是")
    private String withBaby;

    @ApiModelProperty(value = "航段")
    private String segment;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "主舱位")
    private String mainClass;

    @ApiModelProperty(value = "子舱位")
    private String subClass;

    @ApiModelProperty(value = "pnr")
    private String pnr;

    @ApiModelProperty(value = "值机状态PT（出票），NA（未值机），AC（值机），XR（值机取消），CL（订座取消），SB（候补），DL（拉下）")
    private String checkStatus;

    @ApiModelProperty(value = "是否取消0否1是")
    private String isCancel;

    @ApiModelProperty(value = "购票时间")
    private String tktDate;

    @ApiModelProperty(value = "联系电话")
    @SecretValue
    private String telephone;

    @ApiModelProperty(value = "出发航站")
    private String orgCityAirp;

    @ApiModelProperty(value = "到达航站")
    private String dstCityAirp;


    @ApiModelProperty(value = "总补偿次数")
    private String payCount;

}
