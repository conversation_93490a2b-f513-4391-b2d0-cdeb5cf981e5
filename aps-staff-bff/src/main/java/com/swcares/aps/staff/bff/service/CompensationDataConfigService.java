package com.swcares.aps.staff.bff.service;


import com.swcares.aps.staff.bff.vo.CompensationDataConfigVO;
import com.swcares.baseframe.common.base.BaseResult;

import java.util.Map;

/**
 * @ClassName：CompensationDataConfigService
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/23 16:03
 * @version： v1.0
 */
public interface CompensationDataConfigService {
    BaseResult<Map<String, CompensationDataConfigVO>> getBaggageAccidentConfigs();
}
