<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.msg.mapper.FlightEventPassengerMapper">
    <sql id="passengerInfo">
        SELECT
            ID,
            FLIGHT_EVENT_ID,
            PNR_CODE,
            SEQUENCE_NUMBER,
            TICKET_NUMBER,
            PASSENGER_NAME,
            TELE_PHONE,
            ORG,
            DEST,
            FLIGHT_NUMBER,
            FLIGHT_DATE,
            DEP_TIME,
            ARR_TIME,
            VIP,
            GROUP_MEMBER,
            STANDBY,
            ADJUST_ADDITIONAL_INFO,
            CABIN_TYPE,
            NEW_CABIN_TYPE,
            TARGET_FLIGHT_NUMBER,
            TARGET_SEGMENT,
            TARGET_SEGMENT_DATE,
            TARGET_DEP_TIME,
            TARGET_ARR_TIME,
            CREATED_BY,
            CREATED_TIME,
            UPDATED_BY,
            UPDATED_TIME
        FROM
            MSG_FLT_EVENT_PAX_INFO MFEPI
    </sql>
    
    <sql id="searchCriteriaOracle">
        WHERE 1=1
        <if test="dto.flightEventId != null and dto.flightEventId != ''">
            AND MFEPI.FLIGHT_EVENT_ID = #{dto.flightEventId}
        </if>
        <if test="dto.pnr != null and dto.pnr != ''">
            AND MFEPI.PNR_CODE = #{dto.pnr}
        </if>
        <if test="dto.name != null and dto.name !='' ">
            AND MFEPI.PASSENGER_NAME = #{dto.name}
        </if>
        <if test="dto.telphone != null and dto.telphone != '' ">
            AND MFEPI.TELE_PHONE like '%'||#{dto.telphone}||'%'
        </if>
        <if test="dto.ticketNumber != null and dto.ticketNumber !='' ">
            AND MFEPI.TICKET_NUMBER = #{dto.ticketNumber}
        </if>
        <if test="dto.flightDateStart != null and dto.flightDateStart != '' ">
            AND MFEPI.FLIGHT_DATE <![CDATA[ >= ]]> to_date(#{dto.flightDateStart},'yyyy-MM-dd')
        </if>
        <if test="dto.flightDateEnd != null and dto.flightDateEnd != '' ">
            AND MFEPI.FLIGHT_DATE <![CDATA[ < ]]> (to_date(#{dto.flightDateEnd},'yyyy-MM-dd')+1)
        </if>
        <if test="dto.type != null and dto.type == '1'.toString()">
            AND MFEPI.VIP = '1'
        </if>
        <if test="dto.type != null and dto.type == '2'.toString()">
            AND MFEPI.VIP = '0'
        </if>
    </sql>

    <select id="getPassengerInfoPage" resultType="com.swcares.aps.msg.model.entity.FlightEventPassenger" databaseId="oracle">
        <include refid="passengerInfo"/>
        <include refid="searchCriteriaOracle"/>
        ORDER BY MFEPI.VIP DESC, MFEPI.FLIGHT_DATE ASC, MFEPI.PNR_CODE ASC
    </select>

</mapper>
