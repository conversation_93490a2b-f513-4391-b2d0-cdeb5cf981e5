<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.msg.mapper.GFFlightInfoMapper">
    <select id="getSegments" resultType="com.swcares.aps.msg.model.vo.Segment">
        SELECT DISTINCT MFIG.ORG orgAirport, MFIG.DEST destAirport, MFIG.ACROSS_DAY
        FROM MSG_FLIGHT_INFO_GF MFIG
        WHERE 1=1
        <if test="flightNumber != null and flightNumber != ''">
            AND MFIG.FLIGHT_NUMBER = #{flightNumber}
        </if>
        <if test="flightDate != null and flightDate != ''">
            AND MFIG.FLIGHT_DATE = to_date(#{flightDate}, 'yyyy-MM-dd')
        </if>
        ORDER BY MFIG.ORG
    </select>
</mapper>
