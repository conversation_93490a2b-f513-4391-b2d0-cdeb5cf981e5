package com.swcares.aps.web.bff.controller;

import com.swcares.aps.basic.data.businessimpl.model.vo.FlightFindVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.SegmentFindVO;
import com.swcares.aps.compensation.model.irregularflight.dto.FlightAccidentIdDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.FlightAccidentInfoDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.FlightAccidentInfoPagedDTO;
import com.swcares.aps.compensation.model.irregularflight.vo.AccidentFindFlightVO;
import com.swcares.aps.compensation.model.irregularflight.vo.FlightAccidentInfoDetailsVO;
import com.swcares.aps.compensation.model.irregularflight.vo.FlightAccidentInfoVO;
import com.swcares.aps.compensation.model.irregularflight.vo.FlightExistsAccidentVO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.aps.component.dict.model.vo.DictCacheVO;
import com.swcares.aps.web.bff.service.FlightAccidentService;
import com.swcares.aps.web.bff.service.SysDictionaryService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * ClassName：com.swcares.irregularflight.controller.DpFlightAccidentInfoController <br>
 * Description： 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021-10-14 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/accident")
@Api(tags = "不正常航事故单接口")
@ApiVersion(value = "不正常航班赔付 v1.0")
public class FlightAccidentInfoController extends BaseController {
    @Autowired
    private CompensationInfoApi compensationInfoApi;
    @Autowired
    private FlightAccidentService flightAccidentService;
    @Autowired
    SysDictionaryService sysDictionaryService;

    @DeleteMapping("/delete")
    @ApiOperation(value = "通过ID删除记录")
    public BaseResult<Object> delete(@RequestBody @ApiParam(value = "主键id", required = true) FlightAccidentIdDTO idDTO) {
        return compensationInfoApi.delete(idDTO.getId());
    }

    @PutMapping("/toVoid")
    @ApiOperation(value = "通过ID作废记录")
    public BaseResult<Object> toVoid(@RequestBody @ApiParam(value = "主键id", required = true) FlightAccidentIdDTO idDTO) {
        return compensationInfoApi.toVoid(idDTO.getId());
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改记录")
    public BaseResult<Object> update(@RequestBody FlightAccidentInfoDTO dto) {
        return compensationInfoApi.update(dto);
    }

    @GetMapping("/find")
    @ApiOperation(value = "通过ID查询记录详情")
    public BaseResult<FlightAccidentInfoDetailsVO> findById(@ApiParam(value = "主键id", required = true) Long id) {
        return ok(flightAccidentService.accidentDetailsById(id));
    }


    @GetMapping("/page")
    @ApiOperation(value = "条件分页查询记录")
    public PagedResult<List<FlightAccidentInfoVO>> page(FlightAccidentInfoPagedDTO dto) {
        return compensationInfoApi.page(dto);
    }

    @PostMapping("/save")
    @ApiOperation(value = "新建记录")
    public BaseResult<Object> save(@Validated @RequestBody FlightAccidentInfoDTO dto) {
        return compensationInfoApi.save(dto);
    }

    @GetMapping("/findFilghtExistsAccident")
    @ApiOperation(value = "通过航班号和日期来获取是否存在事故单")
    public BaseResult<List<FlightExistsAccidentVO>> findFilghtExistsAccident(@ApiParam(value = "航班日期", required = true) @RequestParam(value = "date") String date,
                                                                             @ApiParam(value = "航班号", required = true) @RequestParam(value = "flightNo") String flightNo,
                                                                             @ApiParam(value = "主键") @RequestParam(value = "id", required = false) Long id) {
        return compensationInfoApi.findFilghtExistsAccident(flightNo,date,id);
    }

    @GetMapping("/findSegment")
    @ApiOperation(value = "航段查询接口")
    public BaseResult<List<SegmentFindVO>> findSegment(@ApiParam(value = "日期", required = true) String date, @ApiParam(value = "航班号", required = true) String flightNo) {
        return compensationInfoApi.getSegment(date, flightNo);

    }

    @GetMapping("/findFilght")
    @ApiOperation(value = "航班查询接口")
    public BaseResult<AccidentFindFlightVO> findFilght(@ApiParam(value = "日期", required = true) String date, @ApiParam(value = "航班号", required = true) String flightNo, @ApiParam(value = "航段", required = true) String choiceSegment) {
        FlightFindVO data = compensationInfoApi.getFlight(date, flightNo, choiceSegment).getData();
        AccidentFindFlightVO accidentFindFlightVO = ObjectUtils.copyBean(data, AccidentFindFlightVO.class);
        accidentFindFlightVO.setDelayReason(data.getDelayReason());
        accidentFindFlightVO.setAtdDate(data.getAtd());
        accidentFindFlightVO.setEtdDate(data.getEtd());
        accidentFindFlightVO.setStdDate(data.getStd());
        return ok(accidentFindFlightVO);
    }

    @GetMapping("findTerminal")
    @ApiOperation(value = "获取航站")
    public BaseResult<Object> getTerminal() {
        return compensationInfoApi.getTerminal();
    }

    @GetMapping("/findChoiceSegment")
    @ApiOperation(value = "通过事故单号查已选航段数据")
    public BaseResult<List<Map<String,Object>>> findChoiceSegment(@RequestParam("id") @ApiParam(value = "主键id", required = true) Long id) {
        return compensationInfoApi.findChoiceSegment(id);
    }


    @GetMapping("/findCompensationSubTypeKey")
    @ApiOperation(value = "赔偿类型key-获取赔偿子类型下拉框")
    public BaseResult<Map<String,List<DictCacheVO>>> findCompensationSubTypeKey(@ApiParam(value = "赔偿类型key", required = true) String key) {
        String subTypeKey = compensationInfoApi.findCompensationSubTypeKey(key).getData();
        return ok(sysDictionaryService.getDictionaryDataList(subTypeKey));
    }

    @GetMapping("/flight/getFltAirStation")
    @ApiOperation(value = "获取补偿航站")
    public BaseResult<Set<String>> getFltAirStation(@RequestParam(value = "date", required = true)String date, @RequestParam(value = "flightNo", required = true)  String flightNo, @RequestParam(value = "choiceSegment", required = true)  String choiceSegment){
        return compensationInfoApi.getFltAirStation(date, flightNo, choiceSegment);
    }

}
