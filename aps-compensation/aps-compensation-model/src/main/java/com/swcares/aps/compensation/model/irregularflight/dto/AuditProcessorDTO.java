package com.swcares.aps.compensation.model.irregularflight.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * ClassName：com.swcares.aps.compensation.model.irregularflight.dto <br>
 * Description：审核处理对象 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 11月24日 11:22 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="AuditProcessorDTO对象", description="赔偿单审核处理对象")
@Builder
public class AuditProcessorDTO {

    @ApiModelProperty(value = "赔偿单id")
    private Long orderId;

    @ApiModelProperty(value = "赔偿单单号")
    private String orderNo;

    @ApiModelProperty(value = "审核状态- 同意AGREE、拒绝REJECT、驳回BACK")
    private String auditStatus;

    @ApiModelProperty(value = "审核备注")
    private String remarks;

    @ApiModelProperty(value = "审核taskId")
    private String taskId;

    @ApiModelProperty(value = "审核人userId-前端不用传",hidden = true)
    private String userId;

}
