<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.complaint.mapper.ComplaintAccidentInfoMapper">


    <select id="pageQuery"
            resultType="com.swcares.aps.compensation.model.complaint.vo.ComplaintAccidentQueryVo">
        SELECT
        cai.ID as id,
        cai.ACCIDENT_ID as accidentId,
        (select COUNT(1) FROM COMPENSATION_ORDER_INFO WHERE ACCIDENT_ID = cai.ID) as quantity,
        cai.ACCIDENT_SOURCE as accidentSource,
        cai.ACCIDENT_STATUS as accidentStatus,
        cai.BELONG_AIRLINE as belongAirline,
        cai.ACCIDENT_TYPE as accidentType,
        cai.ACCIDENT_SUB_TYPE as accidentSubType,
        cai.REASON_TYPE as reasonType,
        cai.FLIGHT_NO as flightNo,
        cai.FLIGHT_DATE as flightDate,
        cai.SEGMENT as segment,
        cai.CREATED_USER as createdUser,
        cai.CREATED_TIME as createdTime
        FROM
        COMPLAINT_ACCIDENT_INFO cai
        <where>
            <if test="dto.flightNo != null and dto.flightNo != ''">
                and cai.FLIGHT_NO = #{dto.flightNo}
            </if>
            <if test="dto.flightStartDate != null and dto.flightStartDate != '' and dto.flightEndDate != null and dto.flightEndDate != '' ">
                and to_char(cai.FLIGHT_DATE,'yyyy-mm-dd') between #{dto.flightStartDate} and #{dto.flightEndDate}
            </if>
            <if test="dto.org != null and dto.org != ''">
                and INSTR( cai.SEGMENT, CONCAT(#{dto.org},'-'))>0
            </if>
            <if test="dto.dst != null and dto.dst != ''">
                and INSTR( cai.SEGMENT, CONCAT('-',#{dto.dst}))>0
            </if>
            <if test="dto.accidentId != null and dto.accidentId != ''">
                and cai.ACCIDENT_ID = #{dto.accidentId}
            </if>
            <!-- 事故单来源，0全部 1机场 2航司 -->
            <choose>
                <when test="dto.accidentSource != null and dto.accidentSource != ''">
                    <if test='dto.accidentSource == "1" or dto.accidentSource == "2" '>
                        and cai.ACCIDENT_SOURCE = #{dto.accidentSource}
                    </if>
                </when>
            </choose>
            <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.accidentStatus)">
                and cai.ACCIDENT_STATUS in
                <foreach collection="dto.accidentStatus" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.belongAirline)">
                and cai.BELONG_AIRLINE in
                <foreach collection="dto.belongAirline" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.reasonType != null and dto.reasonType != ''">
                and cai.REASON_TYPE = #{dto.reasonType}
            </if>
            <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.accidentSubType)">
                and cai.ACCIDENT_SUB_TYPE IN
                <foreach collection="dto.accidentSubType" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY cai.FLIGHT_DATE desc, cai.ACCIDENT_STATUS ASC
    </select>
    <select id="frequency" resultType="com.swcares.aps.compensation.model.complaint.vo.FrequencyVo">
        SELECT
        a.PAX_NAME as paxName,
        COUNT( 1 ) as frequency
        FROM
        (
        SELECT
        cai.ID AS ID,
        pai.PAX_NAME AS PAX_NAME,
        pai.ID_NO AS ID_NO
        FROM
        COMPLAINT_ACCIDENT_INFO cai
        LEFT JOIN PASSENGER_ACCIDENT_INFO pai ON cai.ID = pai.ACCIDENT_PRIMARY_ID
        <where>
            <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto)">
                pai.TKT_NO IN
                <foreach item="item" collection="dto" open="(" separator="," close=")">
                    #{item.ticketNumber}
                </foreach>
            </if>
            <if test="id != null">
                AND cai.id != #{id}
            </if>
        </where>
        GROUP BY
        cai.ID,
        pai.PAX_NAME,
        pai.ID_NO
        ) a
        GROUP BY
        a.PAX_NAME,
        a.ID_NO
    </select>


    <update id="updAccidentStatusBatch">
        UPDATE COMPLAINT_ACCIDENT_INFO SET accident_status = #{accidentStatus}
        where 1=1
        <if test="accidentNo != null and accidentNo.size() >0">
            and accident_id in
            <foreach collection="accidentNo" item="items" open="(" separator="," close=")">
                #{items}
            </foreach>
        </if>
    </update>

</mapper>