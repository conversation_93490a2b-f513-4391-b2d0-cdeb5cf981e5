<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.msg.mapper.MsgTemplateRecordMapper">

    <sql id="getSentSmsStatisticsSql">
        SELECT
        ID,
        DEPT sentDept,
        DEPT_PARENT_ORG sentDeptParentOrg,
        TEMPLATE_TYPE,
        SEND_NUMBER,
        SEND_COUNT,
        SEND_TIME
        FROM MSG_TEMPLATE_RECORD
        where 1=1

        <if test="dto.sentDateStart != null and dto.sentDateEnd != null ">
            AND to_char(SEND_TIME,'yyyy-mm-dd') BETWEEN  #{dto.sentDateStart} and #{dto.sentDateEnd}
        </if>
        <if test="dto.sentDeptParentOrg != null">
            AND DEPT_PARENT_ORG = #{dto.sentDeptParentOrg}
        </if>
        <if test="dto.templateStatisticsType != null">
            AND TEMPLATE_TYPE = #{dto.templateStatisticsType}
        </if>
        order by SEND_TIME desc,TEMPLATE_TYPE,DEPT_PARENT_ORG,ID
    </sql>

    <select id="getSentSmsStatisticsList" resultType="com.swcares.aps.msg.model.vo.SentSmsStatisticsPageVO">

            SELECT
                t.ID,
                t.DEPT sentDept,
                UO.NAME sentDeptName,
                uol.name  sentDeptParentOrgName,
                t.TEMPLATE_TYPE,
                d.item_value as templeteTypeName,
                t.SEND_NUMBER,
                t.SEND_COUNT,
                t.SEND_TIME
            FROM MSG_TEMPLATE_RECORD t
        left join UC_ORGANIZATION uo on t.DEPT = uo.id
        left join UC_ORGANIZATION uol on t.DEPT_PARENT_ORG = uol.id
        left join SYS_DICTIONARY_DATA d ON t.TEMPLATE_TYPE = d.dict_item and d.dict_type = 'smstemplete_count_type'
        where 1=1

            <if test="dto.sentDateStart != null and dto.sentDateEnd != null ">
                AND to_char(SEND_TIME,'yyyy-mm-dd') BETWEEN  #{dto.sentDateStart} and #{dto.sentDateEnd}
            </if>
            <if test="dto.sentDeptParentOrg != null">
                AND DEPT_PARENT_ORG = #{dto.sentDeptParentOrg}
            </if>
            <if test="dto.templateStatisticsType != null">
                AND TEMPLATE_TYPE = #{dto.templateStatisticsType}
            </if>
    </select>


    <select id="getSentSmsStatisticsPage" resultType="com.swcares.aps.msg.model.vo.SentSmsStatisticsPageVO">
        <include refid="getSentSmsStatisticsSql"></include>
    </select>


    <select id="getSentSmsTotalCount" resultType="com.swcares.aps.msg.model.vo.SentSmsStatisticsResultVO">
        SELECT
        sum(SEND_COUNT) sendCountSum,
        sum(SEND_NUMBER) sendNumberSum

        FROM MSG_TEMPLATE_RECORD
        where 1=1

        <if test="dto.sentDateStart != null and dto.sentDateEnd != null ">
            AND to_char(SEND_TIME,'yyyy-mm-dd') BETWEEN  #{dto.sentDateStart} and #{dto.sentDateEnd}
        </if>
        <if test="dto.sentDeptParentOrg != null">
            AND DEPT_PARENT_ORG = #{dto.sentDeptParentOrg}
        </if>
        <if test="dto.templateStatisticsType != null">
            AND TEMPLATE_TYPE = #{dto.templateStatisticsType}
        </if>

    </select>
</mapper>
