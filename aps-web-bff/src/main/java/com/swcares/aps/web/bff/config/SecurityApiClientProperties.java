/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：SecretApiClientProperties <br>
 * Package：com.swcares.reaptv.msg.config <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.web.bff.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * ClassName：com.swcares.reaptv.msg.config.SecretApiClientProperties <br>
 * Description：TODO：(这里用一句话描述这个类的作用) <br>
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/1 13:45 <br>
 * @version:
 */
@Data
@Component
@ConfigurationProperties("swcares.security-api")
public class SecurityApiClientProperties {
    Map<String, AppClientConfig> appClientConfigs;


    
    public String getAppSecret(String appId){
        AppClientConfig config = appClientConfigs.get(appId);
        if (config == null){
            return null;
        }
        
        return config.getSecret();
    }
    
    public String getAppSalt(String appId){
        AppClientConfig config = appClientConfigs.get(appId);
        if (config == null){
            return null;
        }
        
        return config.getSalt();
    }
    
    public String getAppPirvateKey(String appId){
        AppClientConfig config = appClientConfigs.get(appId);
        if (config == null){
            return null;
        }

        return config.getPrivateKey();
    }
}


