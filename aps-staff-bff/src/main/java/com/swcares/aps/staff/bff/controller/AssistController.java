package com.swcares.aps.staff.bff.controller;

import com.swcares.aps.compensation.model.apply.dto.ApplyBehalfOrderDTO;
import com.swcares.aps.compensation.model.apply.dto.ApplyCaptchaDTO;
import com.swcares.aps.compensation.model.apply.dto.AuthPaxDTO;
import com.swcares.aps.compensation.model.apply.vo.CompensationInfoVO;
import com.swcares.aps.compensation.model.assist.dto.CheckInfoDTO;
import com.swcares.aps.compensation.remote.api.irregularflight.ApplyApi;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @title: AssistController
 * @projectName aps
 * @description:
 * @date 2022/1/24 9:27
 */
@RestController
@RequestMapping("/assist")
@Api(tags = "现金协助补偿接口")
@ApiVersion(value = "工作人员端v1.0")
public class AssistController extends BaseController {
    @Autowired
    CompensationInfoApi compensationInfoApi;
    @Autowired
    ApplyApi applyApi;

    @GetMapping("/sendSMSCode")
    @ApiOperation(value = "发送短信验证码")
    public BaseResult<Object> sendSMSCode(@ApiParam(value = "手机号", required = true) @RequestParam String phoneNum) {
        compensationInfoApi.sendSMSCode(phoneNum);
        return ok();
    }

    @GetMapping("/verificationSMSCode")
    @ApiOperation(value = "验证短信验证码")
    public BaseResult<Object> verificationSMSCode(@ApiParam(value = "手机号", required = true) @RequestParam String phoneNum,@ApiParam(value = "验证码", required = true) @RequestParam String authCode) {
        compensationInfoApi.verificationSMSCode(phoneNum,authCode);
        return ok();
    }

    @PostMapping("/findPaxCompensation")
    @ApiOperation(value = "查询某个旅客未领取赔偿记录")
    public BaseResult<CompensationInfoVO> findPaxCompensation(@RequestBody @ApiParam(value = "校验信息DTO") AuthPaxDTO dto){
        return compensationInfoApi.findPaxCompensation(dto);
    }

    @PostMapping("/check")
    @ApiOperation(value = "输入信息校验")
    public BaseResult<Object> checked(@ApiParam(value = "校验信息DTO")@RequestBody CheckInfoDTO dto){
        return compensationInfoApi.checked(dto);
    }

    @GetMapping("/findPaxList")
    @ApiOperation(value = "查询满足申领条件的旅客列表")
    public BaseResult<Object> findPaxList(@ApiParam(value = "航班号")String flightNo,@ApiParam(value = "航班日期")String flightDate,@ApiParam(value = "旅客姓名")String paxName){
        return compensationInfoApi.findPaxList(flightNo, flightDate, paxName);
    }

    @PostMapping("/saveBehalfApply")
    @ApiOperation(value = "代领新建航延补偿申领单信息表记录")
    public BaseResult<Object> saveBehalfApply(@RequestBody @Valid ApplyBehalfOrderDTO dto) {
        compensationInfoApi.saveBehalfApply(dto);
        return ok();
    }

    @GetMapping("/findPaxAmount")
    @ApiOperation(value = "通过手机号查询半年内协助领取人数")
    public BaseResult<Object> findPaxAmount(@ApiParam(value = "申领人手机号") String phone,@ApiParam(value = "被协助旅客证件号") String idNo,@ApiParam(value = "被协助旅客姓名") String paxName) {
        return compensationInfoApi.findPaxAmount(phone,idNo,paxName);
    }

    @GetMapping("/getCaptchaConfig")
    @ApiOperation(value = "获取当前租户的短信验证码配置信息")
    public BaseResult<Object> getCaptchaConfigByTenant() {
        return applyApi.getCaptchaConfigByTenant();
    }

    @PostMapping("/getCaptchaByApply")
    @ApiOperation(value = "申领-获取图形验证码")
    public BaseResult<Object> getCaptchaByApply() {
        return applyApi.getCaptchaByApply();
    }

    @PostMapping("/validateCaptchaByApply")
    @ApiOperation(value = "申领-验证图形验证码")
    public BaseResult<Object> validateCaptchaByApply(@RequestBody @Valid ApplyCaptchaDTO dto) {
        return applyApi.validateCaptchaByApply(dto);
    }
}
