package com.swcares.aps.compensation.model.overbook.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：OverBookPcDetailsVO
 * @Description：pc-事故单详情
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/29 10:19
 * @version： v1.0
 */
@Data
public class OverBookPcDetailsVO {
    @ApiModelProperty(value = "事故单信息")
    OverBookAccidentDetailsVO accidentDetailsVO;
    @ApiModelProperty(value = "旅客信息")
    OverBookBasicPaxVO paxVO;
    @ApiModelProperty(value = "关联补偿单信息集合")
    List<OverBookCompensationDetailsVO> compensationDetailsVOs;
}
