package com.swcares.aps.compensation.model.overbook.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：OverBookH5ListVO
 * @Description：pc超售列表
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/27 15:15
 * @version： v1.0
 */
@Data
public class OverBookPcListVO {
    @ApiModelProperty(value = "事故单ID")
    private Long accidentId;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "事故单状态（0草稿、1待处理、2处理中、3已结案、4作废）")
    private String accidentStatus;

    @ApiModelProperty(value = "事故类型：1改签，2退票")
    private String type;

    @ApiModelProperty(value = "事故来源 1机场/2航司")
    private String accidentSource;

    @ApiModelProperty(value = "归属航司")
    private String belongAirline;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "航段")
    private String  segment;

    @ApiModelProperty(value = "航段-中文格式")
    private String  segmentCh;

    @ApiModelProperty(value = "补偿单数量")
    private String compensateNum;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;


}
