package com.swcares.aps.basic.data.remoteapi.model.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @ClassName：com.swcares.aps.flightpassenger.model.flight.dto.FlightInfoDTO <br>
 * @Description：航班详情 数据传输对象 <br>
 * @Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * @Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * @Date 2022-03-10 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="FlightInfoDTO对象", description="")
public class FlightInfoDTO implements BaseDTO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "航班ID")
    private String flightId;

    @ApiModelProperty(value = "航班号",required = true)
    @NotBlank(message = "航班号不能为空")
    private String flightNo;

    @ApiModelProperty(value = "航班日期",required = true)
    @NotBlank(message = "航班日期不能为空")
    private String flightDate;

    @ApiModelProperty(value = "起始三字码",required = true)
    @NotBlank(message = "起始三字码不能为空")
    private String org;

    @ApiModelProperty(value = "到达三字码",required = true)
    @NotBlank(message = "到达三字码不能为空")
    private String dst;

    @ApiModelProperty(value = "经停三字码",hidden = true)
    private String stop;

    @ApiModelProperty(value = "经停AB段",hidden = true)
    private FlightInfoDTO abFlight;

    @ApiModelProperty(value = "经停BC段",hidden = true)
    private FlightInfoDTO bcFlight;

    @ApiModelProperty(value = "乘务人员编号")
    private List<String> flightUnitPersonnelCodes;

}
