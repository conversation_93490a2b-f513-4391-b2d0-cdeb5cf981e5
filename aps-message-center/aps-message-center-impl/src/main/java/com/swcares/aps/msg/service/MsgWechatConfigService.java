/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：WechatConfigService <br>
 * Package：com.swcares.reaptv.msg.service <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.msg.model.entity.MsgWechatConfig;

/**
 * InterfaceName：com.swcares.reaptv.msg.service.WechatConfigService <br>
 * Description：消息中心微信配置数据的服务类接口<br>
 * <AUTHOR> <br>
 * date 2022/10/26 11:24 <br>
 * @version v1.0.0 <br>
 */
public interface MsgWechatConfigService extends IService<MsgWechatConfig> {
    public MsgWechatConfig getWechatConfigByClient(String clientId);
}
