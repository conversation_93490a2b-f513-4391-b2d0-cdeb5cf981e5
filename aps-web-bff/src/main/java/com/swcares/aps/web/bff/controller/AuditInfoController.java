package com.swcares.aps.web.bff.controller;

import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditOperationVO;
import com.swcares.aps.compensation.model.irregularflight.dto.AuditProcessorDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationAuditInfoDTO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * ClassName：com.swcares.compensation.controller.AuditInfoController <br>
 * Description：赔偿单-审核 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-11-24 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/audit")
@Api(tags = "赔偿单-审核")
@ApiVersion(value = "不正常航班赔付 v1.0")
public class AuditInfoController extends BaseController {
    @Autowired
    CompensationInfoApi compensationInfoApi;

    @PostMapping("/operation")
    @ApiOperation(value = "审核操作-同意、不同意、驳回")
    public BaseResult<Object> auditOperation(@RequestBody AuditProcessorDTO dto) {

        return compensationInfoApi.auditOperation(dto);

    }

    @GetMapping("/submit/{orderId}")
    @ApiOperation(value = "提交补偿单")
    public BaseResult<CompensationAuditOperationVO> auditOperation(@PathVariable("orderId") String orderId) {
        return compensationInfoApi.submitCompensation(orderId);

    }

    @GetMapping("/findReviewer")
    @ApiOperation(value = "查询可选审核人")
    public BaseResult<Object> findReviewer(@ApiParam(value = "部门id") Long orgId, @ApiParam(value = "节点id",required=true) String taskId, @ApiParam(value = "审核人信息：姓名/工号") String userInfo,@ApiParam(value = "赔偿单id")Long orderId) {
        return compensationInfoApi.findReviewer(orgId, userInfo,taskId,orderId);
    }

    @PostMapping("/saveReviewer")
    @ApiOperation(value = "审核人确认")
    public BaseResult<Object> saveReviewer(@RequestBody CompensationAuditInfoDTO dto) {
        return compensationInfoApi.saveReviewer(dto);
    }

    @GetMapping("/findAuditRecord")
    @ApiOperation(value = "查看审核记录")
    public BaseResult<Object> findAuditRecord(@ApiParam(value = "赔偿单id",required=true)Long orderId,@ApiParam(value = "赔偿单号",required=true)String orderNo) {
        return compensationInfoApi.findAuditRecord(orderId,orderNo);
    }

}
