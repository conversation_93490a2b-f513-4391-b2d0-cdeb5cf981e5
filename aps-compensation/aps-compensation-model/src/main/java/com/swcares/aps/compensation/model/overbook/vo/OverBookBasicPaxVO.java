package com.swcares.aps.compensation.model.overbook.vo;

import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：OverBookPaxVO
 * @Description：超售旅客信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/28 10:11
 * @version： v1.0
 */
@SecretInfoEntity
@Data
public class OverBookBasicPaxVO {
    @ApiModelProperty(value = "补偿单次数")
    private String compensateNum;
    @ApiModelProperty(value = "航段")
    private String segment;
    @ApiModelProperty(value = "航段-中文格式")
    private String segmentCh;

    @ApiModelProperty(value = "旅客id")
    private String paxId;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "证件类型")
    private String idType;

    @ApiModelProperty(value = "证件号")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "手机号码")
    @SecretValue
    private String telephone;

    @ApiModelProperty(value = "主舱位")
    private String mainClass;

    @ApiModelProperty(value = "子舱位")
    private String subClass;

    @ApiModelProperty(value = "pnr")
    private String pnr;

    @ApiModelProperty(value = "值机状态PT（出票），NA（未值机），AC（值机），XR（值机取消），CL（订座取消），SB（候补），DL（拉下）")
    private String checkStatus;

    @ApiModelProperty(value = "是否取消0否1是")
    private String isCancel;

    @ApiModelProperty(value = "购票时间")
    private String tktDate;

    @ApiModelProperty(value = "取消时间")
    private String cancelTime;

    @ApiModelProperty(value = "性别C儿童M男F女")
    private String sex;

    @ApiModelProperty(value = "儿童标识0否1是")
    private String isChild;

    @ApiModelProperty(value = "是否携带婴儿标识N否Y是")
    private String withBaby;


    @ApiModelProperty(value = "起始航站三字码")
    private String orgCityAirp;

    @ApiModelProperty(value = "到达航站三字码")
    private String dstCityAirp;

    @ApiModelProperty(value = "经济舱全价")
    private String fullEconomyFare;
}
