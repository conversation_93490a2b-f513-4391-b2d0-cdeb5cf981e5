package com.swcares.aps.ground.assurance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderInfoMapper;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderServiceChoiceSupplierMapper;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderServiceMapper;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderServiceVerifyMapper;
import com.swcares.aps.ground.assurance.repository.AssuranceOrderInfoRepository;
import com.swcares.aps.ground.assurance.repository.impl.AssuranceOrderDomainFactory;
import com.swcares.aps.ground.assurance.service.AssuranceOrderDomainService;
import com.swcares.aps.component.com.platform.PlatformTool;
import com.swcares.aps.ground.constants.GroundServicePubConstants;
import com.swcares.aps.ground.enums.AssuranceOrderState;
import com.swcares.aps.ground.enums.EntityUseFlag;
import com.swcares.aps.ground.enums.VerificationState;
import com.swcares.aps.ground.enums.VerificationType;
import com.swcares.aps.ground.models.assurance.domain.AssuranceOrderDomain;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderChangeStatusCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderVerifyCommand;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderInfoDO;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderServiceChoiceSupplierDO;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderServiceDO;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderServiceVerifyDO;
import com.swcares.aps.component.com.platform.CurrentUserTenantTypeInfo;
import com.swcares.aps.ground.models.supplier.entity.SupplierServiceDO;
import com.swcares.aps.ground.supplier.mapper.SupplierServiceMapper;
import com.swcares.baseframe.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName：AssuranceDomainServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/21 16:23
 * @version： v1.0
 */

@Service
@Slf4j
public class AssuranceOrderDomainServiceImpl implements AssuranceOrderDomainService {

    @Autowired
    private PlatformTool platformTool;

    @Autowired
    private AssuranceOrderDomainFactory assuranceOrderDomainFactory;

    @Autowired
    private AssuranceOrderInfoRepository assuranceOrderInfoRepository;

    @Autowired
    private AssuranceOrderInfoMapper assuranceOrderInfoMapper;

    @Autowired
    private AssuranceOrderServiceChoiceSupplierMapper assuranceOrderServiceChoiceSupplierMapper;

    @Autowired
    private AssuranceOrderServiceVerifyMapper assuranceOrderServiceVerifyMapper;

    @Autowired
    private AssuranceOrderServiceMapper assuranceOrderServiceMapper;

    @Autowired
    private SupplierServiceMapper supplierServiceMapper;

    private final Map<String, List<String>> allowChangeStatusMap=new HashMap<>(8);

    @PostConstruct
    public void init(){
        //草稿->审核中，审核驳回->审核中
        allowChangeStatusMap.put(AssuranceOrderState.AUDIT_ING.getCode(),
                Arrays.asList(AssuranceOrderState.DRAFT.getCode(),AssuranceOrderState.AUDIT_BACK.getCode()));

        //审核中->审核驳回
        allowChangeStatusMap.put(AssuranceOrderState.AUDIT_BACK.getCode(),
                Collections.singletonList(AssuranceOrderState.AUDIT_ING.getCode()));

        //审核中->审核拒绝
        allowChangeStatusMap.put(AssuranceOrderState.AUDIT_REJECT.getCode(),
                Collections.singletonList(AssuranceOrderState.AUDIT_ING.getCode()));

        //审核中->审核通过（保障中）
        allowChangeStatusMap.put(AssuranceOrderState.SERVICE_ING.getCode(),
                Collections.singletonList(AssuranceOrderState.AUDIT_ING.getCode()));

        //保障中,审核中,驳回->逾期
        allowChangeStatusMap.put(AssuranceOrderState.SERVICE_OVER_TIME.getCode(),
                Arrays.asList(AssuranceOrderState.SERVICE_ING.getCode(),
                        AssuranceOrderState.AUDIT_ING.getCode(),
                        AssuranceOrderState.AUDIT_BACK.getCode()));

        //保障中->关闭
        //审核中->关闭（同步过来的数据）
        allowChangeStatusMap.put(AssuranceOrderState.SERVICE_CLOSE.getCode(),
                Arrays.asList(AssuranceOrderState.SERVICE_ING.getCode(),
                        AssuranceOrderState.AUDIT_ING.getCode()));

        //保障中->完成
        allowChangeStatusMap.put(AssuranceOrderState.SERVICE_FINISH.getCode(),
                Collections.singletonList(AssuranceOrderState.SERVICE_ING.getCode()));
    }


    @Override
    public void create(AssuranceOrderDomain orderDomain) {
        assuranceOrderInfoRepository.saveDomain(orderDomain);
    }

    @Override
    public void update(AssuranceOrderDomain orderDomain) {
        AssuranceOrderDomain oldOrderDomain = assuranceOrderDomainFactory.createById(orderDomain.getDomainId());
        if(oldOrderDomain==null){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法保障单ID");
        }
        if(!StringUtils.equalsAny(oldOrderDomain.getDomainState(),AssuranceOrderState.DRAFT.getCode(),AssuranceOrderState.AUDIT_BACK.getCode())){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"保障单当前状态不能修改");
        }
        if(StringUtils.equals(oldOrderDomain.getDomainState(),AssuranceOrderState.AUDIT_BACK.getCode())
                && (!StringUtils.equals(platformTool.getCurrentOperatorUserId(),oldOrderDomain.getOrderInfoDO().getSubmitId()))){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"没有权限修改保障单内容");
        }
        orderDomain.updateCreateInfoByOld(oldOrderDomain);
        assuranceOrderInfoRepository.updateDomain(orderDomain);
    }

    @Override
    public void delete(String domainId) {
        AssuranceOrderDomain orderDomain = assuranceOrderDomainFactory.createById(domainId);
        if(orderDomain==null){return;}
        if(!AssuranceOrderState.DRAFT.getCode().equals(orderDomain.getDomainState())){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"当前状态不能被删除");
        }
        assuranceOrderInfoRepository.delete(orderDomain);
    }
    @Override
    public void checkAssuranceOrderChangeState(AssuranceOrderInfoDO orderInfoDO, AssuranceOrderState targetOrderState) {
        String oriOrderState = orderInfoDO.getOrderState();
        if (!allowChangeStatusMap.get(targetOrderState.getCode()).contains(oriOrderState)) {
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR, "保障单当前状态不能扭转为" + targetOrderState.getDescription());
        }
        if(targetOrderState.getCode().equals(AssuranceOrderState.AUDIT_ING.getCode())){

            LambdaQueryWrapper<AssuranceOrderServiceChoiceSupplierDO> choiceSupplierLambdaQuery = Wrappers
                    .lambdaQuery(AssuranceOrderServiceChoiceSupplierDO.class)
                    .eq(AssuranceOrderServiceChoiceSupplierDO::getOrderId, orderInfoDO.getId());

            List<String> supplierServiceIds = assuranceOrderServiceChoiceSupplierMapper
                    .selectList(choiceSupplierLambdaQuery)
                    .stream()
                    .map(AssuranceOrderServiceChoiceSupplierDO::getSupplierServiceId)
                    .collect(Collectors.toList());

            LambdaQueryWrapper<SupplierServiceDO> supplierServiceLambdaQuery = Wrappers
                    .lambdaQuery(SupplierServiceDO.class)
                    .in(SupplierServiceDO::getId, supplierServiceIds)
                    .eq(SupplierServiceDO::getDeleted, GroundServicePubConstants.DELETED_NO)
                    .eq(SupplierServiceDO::getSupplierServiceState,EntityUseFlag.USABLE.getCode())
                    .eq(SupplierServiceDO::getServiceState,EntityUseFlag.USABLE.getCode())
                    .eq(SupplierServiceDO::getSupplierState,EntityUseFlag.USABLE.getCode())
                    .le(SupplierServiceDO::getBeginDate,orderInfoDO.getFlightDate())
                    .ge(SupplierServiceDO::getEndDate,orderInfoDO.getFlightDate());
            List<SupplierServiceDO> supplierServiceDOS = supplierServiceMapper.selectList(supplierServiceLambdaQuery);
            if(CollectionUtils.size(supplierServiceDOS)!=supplierServiceIds.size()){
                throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"已无满足条件的服务项或供应商，请修改后提交");
            }
        }
    }

    @Override
    public void changeAssuranceOrderStatus(AssuranceOrderChangeStatusCommand command) {
        AssuranceOrderState targetOrderState = AssuranceOrderState.fromCode(command.getTargetStatus());
        if(Objects.equals(targetOrderState.getCode(), AssuranceOrderState.AUDIT_ING.getCode())){
            this.submit(command.getOrderId());
        }else if(Objects.equals(targetOrderState.getCode(), AssuranceOrderState.AUDIT_BACK.getCode())){
            this.auditBack(command.getOrderId());
        }else if(Objects.equals(targetOrderState.getCode(), AssuranceOrderState.AUDIT_REJECT.getCode())){
            this.auditReject(command.getOrderId());
        }else if(Objects.equals(targetOrderState.getCode(), AssuranceOrderState.SERVICE_ING.getCode())){
            this.auditSuccess(command.getOrderId());
        }else if(Objects.equals(targetOrderState.getCode(), AssuranceOrderState.SERVICE_CLOSE.getCode())){
            this.close(command.getOrderId());
        }else if(Objects.equals(targetOrderState.getCode(), AssuranceOrderState.SERVICE_OVER_TIME.getCode())){
            this.overTime(command.getOrderId());
        }else if(Objects.equals(targetOrderState.getCode(), AssuranceOrderState.SERVICE_FINISH.getCode())){
            this.finish(command.getOrderId());
        }else{
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"保障单当前状态不能扭转为"+targetOrderState.getDescription());
        }
    }

    @Override
    public void submit(String domainId) {
        AssuranceOrderDomain orderDomain = assuranceOrderDomainFactory.createById(domainId);
        if(orderDomain==null){ throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法保障单ID");}
        AssuranceOrderInfoDO orderInfoDO = orderDomain.getOrderInfoDO();
        checkAssuranceOrderChangeState(orderInfoDO, AssuranceOrderState.AUDIT_ING);
        CurrentUserTenantTypeInfo currentUserTenantTypeInfo = platformTool.getCurrentUserTenantTypeInfo();
        if(orderInfoDO.getFirstSubmitTime()==null){orderInfoDO.setFirstSubmitTime(new Date());}
        orderInfoDO.setLastSubmitTime(new Date());
        orderInfoDO.setUpdatedTime(new Date());
        orderInfoDO.setSubmitId(currentUserTenantTypeInfo.getCurrentOperatorUserId());
        orderInfoDO.setSubmitBy(currentUserTenantTypeInfo.getCurrentOperator());
        orderInfoDO.setUpdatedBy(currentUserTenantTypeInfo.getCurrentOperator());
        orderInfoDO.setOrderState(AssuranceOrderState.AUDIT_ING.getCode());
        assuranceOrderInfoRepository.updateDoInfo(orderInfoDO);

    }

    //驳回发站内信：您发起的【航班日期】【航班号】 内的补偿保障单单(单号:【XXX】)被驳回，请点击前往处理。
    @Override
    public void auditBack(String domainId) {
        AssuranceOrderInfoDO orderInfoDO = assuranceOrderInfoRepository.getById(domainId);
        if(orderInfoDO==null){ throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法保障单ID");}
        checkAssuranceOrderChangeState(orderInfoDO, AssuranceOrderState.AUDIT_BACK);
        orderInfoDO.setUpdatedTime(new Date());
        orderInfoDO.setUpdatedBy(platformTool.getCurrentOperator());
        orderInfoDO.setLastBackTime(new Date());
        orderInfoDO.setOrderState(AssuranceOrderState.AUDIT_BACK.getCode());
        assuranceOrderInfoRepository.updateDoInfo(orderInfoDO);
    }

    //审核不通过站内信：您发起的【航班日期】【航班号】 内的补偿保障单单(单号:【XXX】)已完成审核，审核结果为【审核状态】，请注意查看。
    @Override
    public void auditReject(String domainId) {
        AssuranceOrderInfoDO orderInfoDO = assuranceOrderInfoRepository.getById(domainId);
        if(orderInfoDO==null){ throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法保障单ID");}
         checkAssuranceOrderChangeState(orderInfoDO, AssuranceOrderState.AUDIT_REJECT);
        orderInfoDO.setUpdatedTime(new Date());
        orderInfoDO.setUpdatedBy(platformTool.getCurrentOperator());
        orderInfoDO.setRejectTime(new Date());
        orderInfoDO.setOrderState(AssuranceOrderState.AUDIT_REJECT.getCode());
        assuranceOrderInfoRepository.updateDoInfo(orderInfoDO);
    }

    //审核通过站内信：您发起的【航班日期】【航班号】 内的补偿保障单单(单号:【XXX】)已完成审核，审核结果为【审核状态】，请注意查看。
    @Override
    public void auditSuccess(String domainId) {
        AssuranceOrderInfoDO orderInfoDO = assuranceOrderInfoRepository.getById(domainId);
        if(orderInfoDO==null){ throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法保障单ID");}
        checkAssuranceOrderChangeState(orderInfoDO, AssuranceOrderState.SERVICE_ING);
        orderInfoDO.setUpdatedTime(new Date());
        orderInfoDO.setUpdatedBy(platformTool.getCurrentOperator());
        orderInfoDO.setAgreeTime(new Date());
        orderInfoDO.setOrderState(AssuranceOrderState.SERVICE_ING.getCode());
        assuranceOrderInfoRepository.updateDoInfo(orderInfoDO);
    }

    @Override
    public void close(String domainId) {
        AssuranceOrderInfoDO orderInfoDO = assuranceOrderInfoRepository.getById(domainId);
        if(orderInfoDO==null){ throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法保障单ID");}
        checkAssuranceOrderChangeState(orderInfoDO, AssuranceOrderState.SERVICE_CLOSE);
        orderInfoDO.setUpdatedTime(new Date());
        orderInfoDO.setUpdatedBy(platformTool.getCurrentOperator());
        orderInfoDO.setCloseTime(new Date());
        orderInfoDO.setOrderState(AssuranceOrderState.SERVICE_CLOSE.getCode());
        assuranceOrderInfoRepository.updateDoInfo(orderInfoDO);


        LambdaQueryWrapper<AssuranceOrderServiceVerifyDO> verifyDOLambdaQueryWrapper = Wrappers
                .lambdaQuery(AssuranceOrderServiceVerifyDO.class)
                .eq(AssuranceOrderServiceVerifyDO::getOrderId, domainId)
                .eq(AssuranceOrderServiceVerifyDO::getVerificationState, VerificationState.WAIT.getCode());

        List<AssuranceOrderServiceVerifyDO> assuranceOrderServiceVerifyDOS = assuranceOrderServiceVerifyMapper
                .selectList(verifyDOLambdaQueryWrapper);
        if(CollectionUtils.isEmpty(assuranceOrderServiceVerifyDOS)){return;}
        assuranceOrderServiceVerifyDOS.forEach(t->{
            t.setUpdatedTime(new Date());
            t.setUpdatedBy(platformTool.getCurrentOperator());
            t.setVerificationState(VerificationState.LOSE.getCode());
        });
        assuranceOrderInfoRepository.updateOrderServiceVerifyDOInfo(assuranceOrderServiceVerifyDOS);

    }

    @Override
    public void overTime(String domainId) {
        AssuranceOrderDomain orderDomain = assuranceOrderDomainFactory.createById(domainId);
        if(orderDomain==null){ throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法保障单ID");}
        AssuranceOrderInfoDO orderInfoDO = orderDomain.getOrderInfoDO();
        checkAssuranceOrderChangeState(orderInfoDO, AssuranceOrderState.SERVICE_OVER_TIME);
        Date agreeTime = orderInfoDO.getFirstSubmitTime();
        if(System.currentTimeMillis() < DateUtils.addHours(agreeTime,AssuranceOrderDomain.OVER_TIME_HOUR).getTime()){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"当前时间状态不能修改为"+AssuranceOrderState.SERVICE_OVER_TIME.getDescription());
        }

        List<AssuranceOrderServiceVerifyDO> assuranceOrderServiceVerifyDOS = orderDomain.getServiceCertificateDos()
                .stream().filter(t -> t.getVerificationState().equals(VerificationState.WAIT.getCode()))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(assuranceOrderServiceVerifyDOS)){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"当前数据状态不能修改状态为"+AssuranceOrderState.SERVICE_OVER_TIME.getDescription());
        }
        assuranceOrderServiceVerifyDOS.forEach(t->{
            t.setUpdatedTime(new Date());
            t.setUpdatedBy(platformTool.getCurrentOperator());
            t.setVerificationState(VerificationState.LOSE.getCode());
        });

        orderInfoDO.setUpdatedTime(new Date());
        orderInfoDO.setUpdatedBy(platformTool.getCurrentOperator());
        orderInfoDO.setOverTime(new Date());
        orderInfoDO.setOrderState(AssuranceOrderState.SERVICE_OVER_TIME.getCode());
        assuranceOrderInfoRepository.updateDoInfo(orderInfoDO);
        assuranceOrderInfoRepository.updateOrderServiceVerifyDOInfo(assuranceOrderServiceVerifyDOS);

    }

    @Override
    public void finish(String domainId) {
        AssuranceOrderDomain orderDomain = assuranceOrderDomainFactory.createById(domainId);
        AssuranceOrderInfoDO orderInfoDO = orderDomain.getOrderInfoDO();
        if(orderInfoDO==null){ throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法保障单ID");}
        checkAssuranceOrderChangeState(orderInfoDO, AssuranceOrderState.SERVICE_FINISH);
        List<AssuranceOrderServiceVerifyDO> assuranceOrderServiceVerifyDOS = orderDomain.getServiceCertificateDos()
                .stream().filter(t -> StringUtils.equalsAny(t.getVerificationState(), VerificationState.WAIT.getCode(), VerificationState.LOSE.getCode()))
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(assuranceOrderServiceVerifyDOS)){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"当前数据状态不能修改状态为"+AssuranceOrderState.SERVICE_FINISH.getDescription());
        }
        AssuranceOrderServiceVerifyDO maxDO=orderDomain.getServiceCertificateDos().get(0);
        for(AssuranceOrderServiceVerifyDO verifyDO:orderDomain.getServiceCertificateDos()){
            if(verifyDO.getUpdatedTime().getTime()>maxDO.getUpdatedTime().getTime()){
                maxDO=verifyDO;
            }
        }
        orderInfoDO.setUpdatedTime(new Date());
        orderInfoDO.setUpdatedBy(maxDO.getConfirmUser());
        orderInfoDO.setFinishTime(maxDO.getConfirmTime());
        orderInfoDO.setOrderState(AssuranceOrderState.SERVICE_FINISH.getCode());
        assuranceOrderInfoRepository.updateDoInfo(orderInfoDO);
    }

    @Override
    public void verifyAssuranceOrder(AssuranceOrderVerifyCommand command){
        VerificationType.fromCode(command.getVerificationType());
        List<AssuranceOrderServiceVerifyDO> orderServiceVerifyDOS = assuranceOrderServiceVerifyMapper
                .selectBatchIds(command.getOrderServiceVerifyIds());
        if(CollectionUtils.isEmpty(orderServiceVerifyDOS)
                || command.getOrderServiceVerifyIds().size()!=orderServiceVerifyDOS.size()){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法保障单服务核销数据【没有找到核销数据】");
        }

        if(VerificationType.FINANCE.getCode().equals(command.getVerificationType())){
            //财务核销的特殊逻辑
            orderServiceVerifyDOS=orderServiceVerifyDOS
                    .stream()
                    .filter(t->VerificationState.WAIT.getCode().equals(t.getVerificationState()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(orderServiceVerifyDOS)){return;}
        }

        Optional<AssuranceOrderServiceVerifyDO> any = orderServiceVerifyDOS
                .stream()
                .filter(t -> (!VerificationState.WAIT.getCode().equals(t.getVerificationState()))
                        || GroundServicePubConstants.DELETED_YES==t.getDeleted())
                .findAny();
        if(any.isPresent()){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"所选数据存在有不可核销的旅客权益/机组权益，请核实");
        }
        Set<String> orderServiceIds = orderServiceVerifyDOS.stream()
                .map(AssuranceOrderServiceVerifyDO::getOrderServiceId)
                .collect(Collectors.toSet());
        if(orderServiceIds.size()>1){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法保障单服务核销数据【核销集合数据不属于同一个服务项】");
        }

        AssuranceOrderServiceChoiceSupplierDO choiceSupplierDO = assuranceOrderServiceChoiceSupplierMapper.selectById(command.getOrderServiceChoiceId());
        if(choiceSupplierDO==null || choiceSupplierDO.getDeleted()==GroundServicePubConstants.DELETED_YES){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法保障单供应商数据【没有找到供应商】");
        }

        if(!orderServiceIds.contains(choiceSupplierDO.getOrderServiceId())){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法保障单供应商数据【供应商不支持当前服务】");
        }

        AssuranceOrderInfoDO assuranceOrderInfoDO = assuranceOrderInfoMapper.selectById(choiceSupplierDO.getOrderId());
        if(!StringUtils.equals(assuranceOrderInfoDO.getOrderState(),AssuranceOrderState.SERVICE_ING.getCode())){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法保障单服务核销数据【当前保障单状态不允许核销数据】");
        }

        CurrentUserTenantTypeInfo currentUserTenantTypeInfo = platformTool.getCurrentUserTenantTypeInfo();


        orderServiceVerifyDOS.forEach(t->{
            t.setVerificationType(command.getVerificationType());
            t.setVerificationState(VerificationState.CONFIRM.getCode());
            t.setConfirmTime(new Date());
            t.setConfirmUser(currentUserTenantTypeInfo.getCurrentOperator());
            t.setConfirmTenantCode(currentUserTenantTypeInfo.getTenantCode());
            t.setConfirmTenantId(currentUserTenantTypeInfo.getTenantId());
            t.setConfirmTenantType(currentUserTenantTypeInfo.getTenantType());
            t.setSupplierId(choiceSupplierDO.getSupplierId());
            t.setSupplierCode(choiceSupplierDO.getSupplierCode());
            t.setSupplierName(choiceSupplierDO.getSupplierName());
            t.setSupplierType(choiceSupplierDO.getSupplierType());
            t.setSupplierBelongTenantCode(choiceSupplierDO.getSupplierBelongTenantCode());
            t.setSupplierBelongTenantId(choiceSupplierDO.getSupplierBelongTenantId());
            t.setSupplierBelongTenantType(choiceSupplierDO.getSupplierBelongTenantType());
            t.setPrice(choiceSupplierDO.getPrice());
            t.setUpdatedTime(new Date());
            t.setUpdatedBy(currentUserTenantTypeInfo.getCurrentOperator());
        });
        assuranceOrderInfoRepository.updateOrderServiceVerifyDOInfo(orderServiceVerifyDOS);

        assuranceOrderInfoRepository.refreshServiceVerifyAmount(orderServiceIds);

        LambdaQueryWrapper<AssuranceOrderServiceVerifyDO> verifyDOLambdaQueryWrapper = Wrappers.lambdaQuery(AssuranceOrderServiceVerifyDO.class)
                .eq(AssuranceOrderServiceVerifyDO::getOrderId, assuranceOrderInfoDO.getId())
                .eq(AssuranceOrderServiceVerifyDO::getVerificationState, VerificationState.WAIT.getCode());
        Integer selectCount = assuranceOrderServiceVerifyMapper.selectCount(verifyDOLambdaQueryWrapper);
        boolean needUpdateDo=false;
        if(selectCount==0){
            assuranceOrderInfoDO.setFinishTime(new Date());
            assuranceOrderInfoDO.setOrderState(AssuranceOrderState.SERVICE_FINISH.getCode());
            assuranceOrderInfoDO.setUpdatedBy(currentUserTenantTypeInfo.getCurrentOperator());
            assuranceOrderInfoDO.setUpdatedTime(new Date());
            needUpdateDo=true;
        }
        if(assuranceOrderInfoDO.getFirstVerifyTime()==null){
            assuranceOrderInfoDO.setFirstVerifyTime(new Date());
            assuranceOrderInfoDO.setFirstVerifier(currentUserTenantTypeInfo.getCurrentOperator());
            assuranceOrderInfoDO.setUpdatedBy(currentUserTenantTypeInfo.getCurrentOperator());
            assuranceOrderInfoDO.setUpdatedTime(new Date());
            needUpdateDo=true;
        }
        if(needUpdateDo){assuranceOrderInfoRepository.updateDoInfo(assuranceOrderInfoDO);}
    }
}
