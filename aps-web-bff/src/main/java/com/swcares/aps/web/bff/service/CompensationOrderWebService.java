package com.swcares.aps.web.bff.service;

import com.swcares.aps.compensation.model.irregularflight.vo.CompensationFromDetailsVO;

import java.util.Map;

/**
 * ClassName：com.swcares.aps.compensation.bff.web.service <br>
 * Description：赔偿单服务类 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 10月28日 17:18 <br>
 * @version v1.0 <br>
 */
public interface CompensationOrderWebService {

    /**
     * Title：getCompensationDetails <br>
     * Description：赔偿单详情界面 <br>
     * author：傅欣荣 <br>
     * date：2021/10/28 17:20  <br>
     * @param orderId <br>
     * @param accidentId <br>
     * @param orderNo <br>
     * @return <br>
     */
    Map<String, Object> getCompensationDetails(Long orderId, Long accidentId,String orderNo);

    CompensationFromDetailsVO getCompensationDetailsVo(Long orderId, Long accidentId, String orderNo,String accidentType);


    /**
     * Title：getOrderEditEchoDetails <br>
     * Description：获取赔偿单编辑回显详情-详情包含：事故单信息、赔偿单信息、补偿规则、已选旅客、全部||已选旅客<br>
     * author：傅欣荣 <br>
     * date：2021/11/11 10:22 <br>
     * @param orderId <br>
     * @param accidentId <br>
     * @return <br>
     */
    Map<String, Object> getOrderEditEchoDetails(Long orderId, Long accidentId);
}
