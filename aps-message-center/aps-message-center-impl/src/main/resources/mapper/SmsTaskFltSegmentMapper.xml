<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.msg.mapper.SmsTaskFltSegmentMapper">

    <select id="getTaskSengmentByEvent" resultType="com.swcares.aps.msg.model.entity.SmsTaskFltSegmentInfo">
        SELECT mmtfs.* FROM MSG_MESSAGE_TASK_FLT_SEGMENT mmtfs
        LEFT JOIN MSG_MESSAGE_TASK mmt  ON mmt.TASK_ID = mmtfs.TASK_ID
        WHERE to_char(mmtfs.FLIGHT_DATE,'yyyy-MM-dd') = #{dto.flightDate}
        AND mmtfs.FLIGHT_NUMBER = #{dto.flightNumber}
        AND (mmtfs.ORG = #{dto.org} OR  mmtfs.DST = #{dto.dst})
        AND mmt.FLIGHT_EVENT = #{dto.event}
        AND (mmt.TASK_STATUS = '2' or mmt.TASK_STATUS = '3')
        <if test="dto.taskId != null and dto.taskId != ''  ">
            AND mmt.TASK_ID = #{dto.taskId}
        </if>
        ORDER BY mmtfs.TASK_ID
    </select>

</mapper>