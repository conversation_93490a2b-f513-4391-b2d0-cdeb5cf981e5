package com.swcares.aps.compensation.model.complaint.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "CompensationTypeVo对象", description = "补偿方式")
public class CompensationTypeVo {

    @ApiModelProperty(value = "补偿方式")
    private String compensationType;

    @ApiModelProperty(value = "传值")
    private String value;

    @ApiModelProperty(value = "true为可用，false为不可用")
    private Boolean role;

    @ApiModelProperty(value = "子类型")
    private List<CompensationTypeVo> children;
}
