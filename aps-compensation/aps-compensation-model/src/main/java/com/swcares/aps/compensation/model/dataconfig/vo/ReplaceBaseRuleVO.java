package com.swcares.aps.compensation.model.dataconfig.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.aps.compensation.model.replace.vo.ReplaceRuleVO <br>
 * Description：返回展示对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-10 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="ReplaceBaseRuleVO对象", description="代人领取规则VO,除开支付等待期外的所有RULE")
public class ReplaceBaseRuleVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否包含本人 0:不包含 1:包含",example = "1")
    private String isContainSelf;

    @ApiModelProperty(value = "是否同航班旅客 0:不是 1:是",example = "1")
    private String isSameFlight;

    @ApiModelProperty(value = "最大可申领取乘机人数",example = "10")
    private Integer maxApplyPassenger;
}
