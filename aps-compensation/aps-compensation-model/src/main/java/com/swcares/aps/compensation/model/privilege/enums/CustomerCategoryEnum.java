package com.swcares.aps.compensation.model.privilege.enums;

import com.swcares.baseframe.common.enums.JsonEnumValue;

public enum CustomerCategoryEnum{

    AIRLINE("Airline", "航空公司客户"),
    AIRPORT("Airport", "机场客户"),
    SUPPLIER("Supplier", "供应商客户");

    @JsonEnumValue
    private String code;

    private String description;

    private CustomerCategoryEnum(String name, String description) {
        this.code = name;
        this.description = description;
    }

    // Getters and setters
    public String getCode() {
        return code;
    }


    public String getDescription() {
        return description;
    }

    public static CustomerCategoryEnum getEnumByCode(String code) {
        for (CustomerCategoryEnum customerCategoryEnum : CustomerCategoryEnum.values()) {
            if (customerCategoryEnum.getCode().equals(code)) {
                return customerCategoryEnum;
            }
        }
        return null;
    }
}
