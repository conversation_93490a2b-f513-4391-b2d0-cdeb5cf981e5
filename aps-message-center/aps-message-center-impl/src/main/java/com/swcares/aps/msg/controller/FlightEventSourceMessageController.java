/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：FlightEventController <br>
 * Package：com.swcares.reaptv.msg.controller <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.controller;

import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.aps.msg.model.dto.FlightEventMessageDTO;
import com.swcares.aps.msg.model.entity.FlightEventSourceMessage;
import com.swcares.aps.msg.service.FlightEventManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/*
 * ClassName：com.swcares.reaptv.msg.controller.FlightEventController <br>
 * Description：航变事件原始消息的http访问接口 <br>
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/1 9:35 <br>
 * @version:
 */
@RestController
@RequestMapping("/msg/flightevent/message")
@Slf4j
public class FlightEventSourceMessageController {
    @Autowired
    private FlightEventManager flightEventManager;
    
    @PostMapping("/add")
    public BaseResult<String> saveNewMessage(@RequestBody FlightEventMessageDTO flightEventMessageDTO){
        log.info("接收到新的航变事件原始消息，开始处理。");
        if (flightEventMessageDTO == null || StringUtils.isBlank(flightEventMessageDTO.getData())){
            log.info("原始消息错误：消息为空。");
            throw new IllegalArgumentException("消息不能为空。");
        }
        
        FlightEventSourceMessage flightEventMessage = new FlightEventSourceMessage();
        flightEventMessage.setContent(flightEventMessageDTO.getData());
        flightEventManager.processFlightEventEventMessage(flightEventMessage);
        
        return BaseResult.ok("SUCCESS");
    }
    
}
