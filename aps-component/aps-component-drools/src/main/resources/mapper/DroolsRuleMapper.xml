<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.component.drools.impl.mapper.DroolsRuleMapper">

    <select id="page" resultType="com.swcares.aps.component.drools.model.vo.DroolsRuleVO">
        SELECT
        t.id,
        t.`code`,
        t.rule_content,
        t.remark,
        t.created_by,
        t.created_time,
        t.updated_by,
        t.updated_time
        FROM
        drools_rule t
        WHERE t.deleted != 1
        <if test="dto.code != null">
            and  t.`code` = #{dto.code}
        </if>
        order by t.updated_time desc
    </select>

    <update id="logicRemoveById">
        update drools_rule t
        set t.deleted = 1
        where t.id = #{id}
    </update>
</mapper>
