/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：FlightEventMessageService <br>
 * Package：com.swcares.reaptv.msg.service <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.msg.model.entity.FlightEventSourceMessage;

import java.util.List;

/**
 * InterfaceName：com.swcares.reaptv.msg.service.FlightEventMessageService <br>
 * Description：TODO：(这里用一句话描述这个接口的作用) <br>
 * <AUTHOR> <br>
 * date 2022/11/30 11:23 <br>
 * @version v1.0.0 <br>
 */
public interface FlightEventSourceMessageService extends IService<FlightEventSourceMessage> {
    /**   
     * Title：getMissingSourceMessage <br>
     * Description：获取漏处理的原始消息列表
     * @Return java.util.List<com.swcares.reaptv.msg.model.entity.FlightEventSourceMessage>
     * @throws 
     * @author：王翼 <br>
     * date：2022-12-25 17:12 <br>
     */
    List<FlightEventSourceMessage> getMissingSourceMessage();
    
    /**   
     * Title：markAsProcessed <br>
     * Description：将指定的航变原始消息标记为已处理
     * @param flightEventSourceMessage : 航变原始消息
     * @Return void
     * @throws 
     * @author：王翼 <br>
     * date：2022-12-25 17:13 <br>
     */  
    
    void markAsProcessed(FlightEventSourceMessage flightEventSourceMessage);
}
