package com.swcares.aps.component.drools.model.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.component.drools.model.dto.DroolsRulePageDTO <br>
 * Description：规则引擎管理DTO分页对象 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021/10/20 10:34 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="规则引擎管理DTO分页对象", description="")
public class DroolsRulePageDTO extends PagedDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "规则编码")
    private String code;
}
