package com.swcares.aps.web.bff.controller.ground;

import com.swcares.aps.ground.SupplierApi;
import com.swcares.aps.ground.enums.EntityState;
import com.swcares.aps.ground.models.supplier.dto.*;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @ClassName：SupplierCommonController
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/19 12:10
 * @version： v1.0
 */
@RestController
@RequestMapping("/supplier/command")
@Api(tags = {"服务商相关业务接口"})
@ApiVersion({"服务商相关Api"})
public class SupplierCommandController extends BaseController {
    @Autowired
    private SupplierApi supplierApi;

    @PostMapping("/save")
    @ApiOperation(value = "保存供应商信息")
    public BaseResult<Object> saveOrUpdateSupplier(@RequestBody SupplierSaveCommand command){
        return supplierApi.saveOrUpdateSupplier(command);
    }

    @GetMapping("/delete/{id}")
    @ApiOperation(value = "删除供应商信息")
    public BaseResult<Object> deleteSupplier(@PathVariable @ApiParam(value = "主键id", required = true) String id){
        return supplierApi.deleteSupplier(id);
    }

    @GetMapping("/stop/{id}")
    @ApiOperation(value = "修改供应商状态")
    public BaseResult<Object> stopSupplierStatus(@PathVariable @ApiParam(value = "主键id", required = true) String id){
        SupplierChangeStatusCommand command=new SupplierChangeStatusCommand();
        command.setSupplierId(id);
        command.setTargetStatus(EntityState.STOPPED.getCode());
        return supplierApi.changeSupplierStatus(command);
    }

    @GetMapping("/active/{id}")
    @ApiOperation(value = "修改供应商状态")
    public BaseResult<Object> activeSupplierStatus(@PathVariable @ApiParam(value = "主键id", required = true) String id){
        SupplierChangeStatusCommand command=new SupplierChangeStatusCommand();
        command.setSupplierId(id);
        command.setTargetStatus(EntityState.ACTIVE.getCode());
        return supplierApi.changeSupplierStatus(command);
    }

    @PostMapping("/service/save")
    @ApiOperation(value = "添加服务商服务")
    public BaseResult<Object> addSupplierService(@RequestBody SupplierServiceSaveCommand command){
        return supplierApi.addSupplierService(command);
    }

    @PostMapping("/service/del")
    @ApiOperation(value = "删除服务商服务")
    public BaseResult<Object> delSupplierService(@RequestBody SupplierServiceSaveCommand command){
        return supplierApi.delSupplierService(command);
    }

    @PostMapping("/service/stop")
    @ApiOperation(value = "停用服务商服务")
    public BaseResult<Object> stopServiceStatus(@RequestBody SupplierServiceStatusCommand command){
        command.setTargetStatus(EntityState.STOPPED.getCode());
        return supplierApi.changeServiceStatus(command);
    }
    @PostMapping("/service/active")
    @ApiOperation(value = "启用服务商服务")
    public BaseResult<Object> activeServiceStatus(@RequestBody SupplierServiceStatusCommand command){
        command.setTargetStatus(EntityState.ACTIVE.getCode());
        return supplierApi.changeServiceStatus(command);

    }


    @PostMapping("/changeContractAgreement")
    @ApiOperation(value = "修改供应商有效期")
    public BaseResult<Object> changeContractAgreement(@RequestBody @Valid SupplierContractAgreementCommand command){
        return supplierApi.changeContractAgreement(command);
    }

}
