package com.swcares.aps.web.bff.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.swcares.aps.component.dict.model.vo.DictCacheVO;
import com.swcares.aps.component.dict.util.DictUtil;
import com.swcares.aps.web.bff.service.SysDictionaryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.aps.compensation.bff.service.impl <br>
 * Description： <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 10月26日 9:52 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class SysDictionaryServiceImpl implements SysDictionaryService {



    @Override
    public Map<String, List<DictCacheVO>> getDictionaryDataList(List<String> dictType) {

        Map<String, List<DictCacheVO>> dicts = DictUtil.getAll(null);
        log.info("【aps-compensation-bff】数据字典查询，全部字典【{}】", JSONUtil.toJsonStr(dicts));
        if(ObjectUtil.isNotEmpty(dictType)){
            Map<String, List<DictCacheVO>> dataMap = new HashMap<>();
            dictType.forEach(s -> {
                if(!ObjectUtil.isEmpty(dicts.get(s))){
                    dataMap.put(s,dicts.get(s));
                }
            });
            log.info("【aps-compensation-bff】数据字典查询，查询类型【{}】，查询结果【{}】",JSONUtil.toJsonStr(dictType),JSONUtil.toJsonStr(dataMap));
            return dataMap;
        }
        return dicts;
    }

    @Override
    public Map<String, List<DictCacheVO>> getDictionaryDataList(String dictType) {
        List<String> list = new ArrayList<>();
        list.add(dictType);
        return this.getDictionaryDataList(list);
    }
}
