package com.swcares.aps.compensation.model.complaint.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ComplaintCompensationCheckInfoVo对象", description = "新增旅客投诉补偿单校验展示重复保障单信息")
public class ComplaintCompensationCheckInfoVo {

    @ApiModelProperty(value = "主键id")
    private String id;

    @ApiModelProperty(value = "补偿单单号")
    private String orderId;

    @ApiModelProperty(value = "补偿单状态")
    private String orderStatus;

    @ApiModelProperty(value = "航段")
    private String segment;

    @ApiModelProperty(value = "补偿总金额")
    private String totalAmount;

    @ApiModelProperty(value = "总人数")
    private String totalNumber;

    @ApiModelProperty(value = "创建人")
    private String createdUser;

    @ApiModelProperty(value = "事故单主键")
    private String accidentId;

    @ApiModelProperty(value = "事故类型")
    private String accidentType;
}
