package com.swcares.aps.compensation.model.irregularflight.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.dto.CompensationFlightInfoPagedDTO <br>
 * Description：赔偿航班信息 分页数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="CompensationFlightInfoPagedDTO分页对象", description="赔偿航班信息")
public class CompensationFlightInfoPagedDTO extends PagedDTO{

    private static final long serialVersionUID = 1L;

}
