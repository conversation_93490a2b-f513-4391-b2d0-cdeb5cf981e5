package com.swcares.aps.compensation.model.compensation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CompensationFlightExtCashVO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 31947
 * @Date： 2022/4/18 16:39
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationAbnormalFlightExtCashVO", description="现金补偿单（不正常航班）列表VO")
public class CompensationAbnormalFlightExtCashVO extends CompensationBaseExtCashVO{

    @ApiModelProperty(value = "已补偿人数")
    private Integer hasCompensate;

    @ApiModelProperty(value = "总共需要补偿的人数")
    private Integer allCompensate;
}
