package com.swcares.aps.compensation.model.compensation.vo;

import io.swagger.annotations.ApiModel;
import lombok.Getter;

import java.util.List;

/**
 * @ClassName：CompensationAuditInfoVO
 * @Description：补偿单审核人信息查询接口返回参数
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/9 16:03
 * @version： v1.0
 */
@Getter
@ApiModel(value="CompensationAuditInfoVOO对象", description="补偿单审核人信息VO")
public class CompensationAuditInfoVO {

    private String orderId;

    private List<String> userIds;

    private String taskId;

    public  CompensationAuditInfoVO (String orderId,List<String> userIds,String taskId){
        this.orderId=orderId;
        this.userIds=userIds;
        this.taskId=taskId;
    }

}
