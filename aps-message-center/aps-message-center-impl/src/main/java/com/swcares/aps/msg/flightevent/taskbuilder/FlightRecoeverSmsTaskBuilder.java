/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：FlightRecoeverSmsTaskBuilder <br>
 * Package：com.swcares.reaptv.msg.flightevent.taskbuilder <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.flightevent.taskbuilder;


import com.swcares.aps.msg.model.dto.SemiAutoSmsTaskDTO;
import com.swcares.aps.msg.model.entity.*;
import com.swcares.aps.msg.model.enums.FlightEventEnum;
import com.swcares.aps.msg.service.FlightEventPassengerService;
import com.swcares.aps.msg.service.TempleteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * ClassName：com.swcares.reaptv.msg.flightevent.taskbuilder.FlightRecoeverSmsTaskBuilder <br>
 * Description：航班恢复类的数据的短信任务构造器
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/21 20:10 <br>
 * @version:
 */
@Component
public class FlightRecoeverSmsTaskBuilder extends AbstractSemiSmsTaskBuilder{
    private static final String SUPPORTED_PROCESS_TYPE ="NEW";
    private static final String SMS_TEMPLATE_ID = "FLIGHT_RECOVER_SEMI_AUTO";

    @Autowired
    private FlightEventPassengerService flightEventPassengerService;

    @Autowired
    private TempleteService templeteService;
    
    
    @Override public boolean isSupport(FlightEvent flightEvent) {
        return SUPPORTED_PROCESS_TYPE.equalsIgnoreCase(flightEvent.getProcessType());
    }


    @Override
    public List<SemiAutoSmsTaskDTO> buildSemiAutoTask(FlightEvent flightEvent, List<FlightEventPassenger> passengers) {

        SemiAutoSmsTaskDTO semiAutoSmsTaskDTO = new SemiAutoSmsTaskDTO();
        Templete templete = templeteService.getById(SMS_TEMPLATE_ID);
        SmsTaskInfo taskInfo = createSmsTaskInfo(flightEvent, SMS_TEMPLATE_ID, templete.getTempleteContent(), passengers);
        semiAutoSmsTaskDTO.setTaskInfo(taskInfo);

        List<SmsTaskDetailInfo> taskDetailInfos = createTaskDetailInfos(flightEvent, passengers);
        semiAutoSmsTaskDTO.setTaskDetailInfoList(taskDetailInfos);

        List<SemiAutoSmsTaskDTO> results = new ArrayList<>();
        results.add(semiAutoSmsTaskDTO);
        return results;
    }


    @PostConstruct
    public void registerToFactory(){
        SemiAutoSmsTaskBuilderFactory.register(SUPPORTED_PROCESS_TYPE, this);
    }

    @Override
    FlightEventEnum getFlightEventEnum() {
        return FlightEventEnum.CANCEL_RECOVER;
    }
}
