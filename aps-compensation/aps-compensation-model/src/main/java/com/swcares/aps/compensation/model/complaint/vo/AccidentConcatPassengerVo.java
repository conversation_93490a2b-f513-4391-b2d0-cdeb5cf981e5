package com.swcares.aps.compensation.model.complaint.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "AccidentConcatPassengerVo对象", description = "旅客投诉事故单关联旅客列表")
public class AccidentConcatPassengerVo {
    @ApiModelProperty(value = "旅客姓名")
    private String passengerName;

    @ApiModelProperty(value = "证件类型")
    private String idType;

    @ApiModelProperty(value = "证件号码")
    private String idNumber;

    @ApiModelProperty(value = "航段")
    private String flightSegment;

    @ApiModelProperty(value = "票号")
    private String ticketNumber;

    @ApiModelProperty(value = "补偿金额")
    private String compensationAmount;

    @ApiModelProperty(value = "主舱位")
    private String mainCabin;

    @ApiModelProperty(value = "子舱位")
    private String carryCabin;

    @ApiModelProperty(value = "值机")
    private String checkStatus;

    @ApiModelProperty(value = "取消")
    private String isCancel;

    @ApiModelProperty(value = "取消时间")
    private String cancelTime;

    @ApiModelProperty(value = "购票日期")
    private String ticketIssueDate;

    @ApiModelProperty(value = "pnr")
    private String pnr;

    @ApiModelProperty(value = "儿童标识0否1是")
    private String isChild;

    @ApiModelProperty(value = "是否携带婴儿标识N否Y是")
    private String withBaby;
}
