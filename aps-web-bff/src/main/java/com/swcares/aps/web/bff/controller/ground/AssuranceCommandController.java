package com.swcares.aps.web.bff.controller.ground;

import com.swcares.aps.ground.AssuranceCommandApi;
import com.swcares.aps.ground.enums.AssuranceOrderState;
import com.swcares.aps.ground.enums.VerificationType;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderChangeStatusCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderReviewerSaveCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderSaveCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderVerifyCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceWorkflowAuditCommand;
import com.swcares.aps.ground.models.assurance.vo.AssuranceWorkflowAuditResultVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName：AssuranceCommandController
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/23 10:18
 * @version： v1.0
 */
@RestController
@RequestMapping("/assurance/busi/command")
@Api(tags = {"保障单业务操作接口"})
@ApiVersion({"保障单相关Api"})
@Slf4j
public class AssuranceCommandController extends BaseController {

    @Autowired
    private AssuranceCommandApi assuranceCommandApi;


    @PostMapping("/save")
    @ApiOperation(value = "保存保障单信息")
    public BaseResult<Object> saveOrUpdateAssuranceOrder(@RequestBody AssuranceOrderSaveCommand command){

       return assuranceCommandApi.saveOrUpdateAssuranceOrder(command);
    }

    @GetMapping("/submit/{id}")
    @ApiOperation(value = "提交保障单")
    public BaseResult<Object> submitAssuranceOrder(@PathVariable @ApiParam(value = "主键id", required = true) String id){
        AssuranceOrderChangeStatusCommand command=new AssuranceOrderChangeStatusCommand();
        command.setOrderId(id);
        command.setTargetStatus(AssuranceOrderState.AUDIT_ING.getCode());
        return assuranceCommandApi.changeAssuranceOrderStatus(command);
    }

    @GetMapping("/close/{id}")
    @ApiOperation(value = "关闭保障单")
    public BaseResult<Object> reSubmitAssuranceOrder(@PathVariable @ApiParam(value = "主键id", required = true) String id){
        AssuranceOrderChangeStatusCommand command=new AssuranceOrderChangeStatusCommand();
        command.setOrderId(id);
        command.setTargetStatus(AssuranceOrderState.SERVICE_CLOSE.getCode());
        return assuranceCommandApi.changeAssuranceOrderStatus(command);
    }

    @GetMapping("/delete/{id}")
    @ApiOperation(value = "删除保障单信息")
    public BaseResult<Object> deleteAssuranceOrder(@PathVariable @ApiParam(value = "主键id", required = true) String id){
        return assuranceCommandApi.deleteAssuranceOrder(id);
    }

    @PostMapping("/workflow/process")
    @ApiOperation(value = "审核保障单")
    public BaseResult<AssuranceWorkflowAuditResultVO> processAssuranceOrderWorkflow(@RequestBody AssuranceWorkflowAuditCommand command){
        return assuranceCommandApi.processAssuranceOrderWorkflow(command);
    }

    @PostMapping("/workflow/saveAuditReviewer")
    @ApiOperation(value = "审核人确认")
    public BaseResult<Object> saveAuditReviewer(@RequestBody @Validated AssuranceOrderReviewerSaveCommand command) {
        return assuranceCommandApi.processAssuranceSaveAuditReviewer(command);
    }

    @PostMapping("/verify/batch")
    @ApiOperation(value = "批量核销保障单")
    public BaseResult<Object> batchVerifyAssuranceOrder(@RequestBody AssuranceOrderVerifyCommand command){
        command.setVerificationType(VerificationType.BATCH.getCode());
        return assuranceCommandApi.verifyAssuranceOrder(command);
    }

    @PostMapping("/verify/finance")
    @ApiOperation(value = "财务核销保障单")
    public BaseResult<Object> financeVerifyAssuranceOrder(@RequestBody AssuranceOrderVerifyCommand command){
        command.setVerificationType(VerificationType.FINANCE.getCode());
        return assuranceCommandApi.verifyAssuranceOrder(command);
    }
}
