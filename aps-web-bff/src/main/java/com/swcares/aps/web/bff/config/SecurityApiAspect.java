/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：SecretApiAspect <br>
 * Package：com.swcares.reaptv.msg.config <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.web.bff.config;

import com.swcares.aps.component.com.security.RSAUtils;
import com.swcares.aps.msg.model.dto.SecretRequestDTO;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * ClassName：com.swcares.reaptv.msg.config.SecretApiAspect <br>
 * Description：TODO：(这里用一句话描述这个类的作用) <br>
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/1 10:26 <br>
 * @version:
 */
@Aspect
@Component
public class SecurityApiAspect {
    private Logger logger = LoggerFactory.getLogger(SecurityApiAspect.class);
    
    @Autowired
    private SecurityApiClientProperties secretApiProperties;
    
    @Pointcut("@within(org.springframework.web.bind.annotation.RestController)&& @annotation(com.swcares.aps.web.bff.config.SecuritytApi)")
    public void restControllerMethodPointcut(){
    }
    
    @Around("restControllerMethodPointcut()")
    public Object Interceptor(ProceedingJoinPoint joinPoint) throws Throwable {
        logger.info("进入请求解密aop");
        SecretRequestDTO requestDTO = (SecretRequestDTO)joinPoint.getArgs()[0];
        logger.info("来自{}的请求{}",requestDTO.getAppId(), joinPoint.getSignature().getName());
        checkSign(requestDTO);
        
        decryptData(requestDTO);
        return joinPoint.proceed();
    }
    
    private void decryptData(SecretRequestDTO requestDTO) {
        String appPirvateKey = secretApiProperties.getAppPirvateKey(requestDTO.getAppId());
        
        if (StringUtils.isEmpty(appPirvateKey)){
            //该用户没有配置加密参数， 抛出异常
            logger.error("对客户端{}没有配置加解密的配置，无法处理安全接口的请求。", requestDTO.getAppId());
            throw new BusinessException(MessageCenterBffErrors.UNKNOWN_APP_ID, requestDTO.getAppId());
        }
        
        String encryptedData = requestDTO.getData();
        if (StringUtils.isEmpty(encryptedData)){
            return;
        }

        try {
            String decryptedData = RSAUtils.decryptByPrivateKey(encryptedData, appPirvateKey);
            requestDTO.setData(decryptedData);
        } catch (Exception e) {
            logger.error("无法解密加密接口传递的数据",e);
            throw new BusinessException(MessageCenterBffErrors.ENCRYPTED_DATA_ERROR);
        }
    }
    
    private void checkSign(SecretRequestDTO requestDTO) {
        String appSalt = secretApiProperties.getAppSalt(requestDTO.getAppId());
        if (StringUtils.isEmpty(appSalt)){
            //该用户没有配置加密参数， 抛出异常
            throw new BusinessException(MessageCenterBffErrors.UNKNOWN_APP_ID, requestDTO.getAppId());
        }
        String data = requestDTO.getData();
        
        String signFromRequest = requestDTO.getSign();
        String calculatedSign="";
        try {
            calculatedSign = calculateSign(data, appSalt);
        } catch (Exception e) {
            logger.error("计算签名的时候发生错误，无法验证签名。");
            throw new BusinessException(MessageCenterBffErrors.API_SIGN_ERROR);
        }
        
        if (!calculatedSign.equalsIgnoreCase(signFromRequest)){
            logger.error("请求中的签名不正确");
            throw new BusinessException(MessageCenterBffErrors.API_SIGN_ERROR);
        }
    }
    
    private String calculateSign(String data, String appSalt)
            throws UnsupportedEncodingException, NoSuchAlgorithmException {
        if (data == null){
            data = "";
        }
        MessageDigest md5Digest = MessageDigest.getInstance("MD5");
        
        String signString = data + appSalt;
        byte[] signBytes = signString.getBytes("UTF-8");
        byte[] md5Bytes = md5Digest.digest(signBytes);

        return Base64.getEncoder().encodeToString(md5Bytes);
    }
}
