package com.swcares.aps.component.drools.impl.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.component.drools.model.entity.DroolsRuleDO;
import com.swcares.aps.component.drools.model.vo.DroolsRuleVO;
import com.swcares.aps.component.drools.model.dto.DroolsRulePageDTO;
import org.kie.api.runtime.KieSession;

import java.io.UnsupportedEncodingException;

/**
 * ClassName：com.swcares.component.drools.impl.service.DroolsRuleService <br>
 * Description：规则引擎服务类接口 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021/10/19 16:16 <br>
 * @version v1.0 <br>
 */
public interface DroolsRuleService  extends IService<DroolsRuleDO> {
    /**
     * Title：logicRemoveById <br>
     * Description：逻辑删除规则 <br>
     * author：王磊 <br>
     * date：2021/10/20 11:21 <br>
     * @param id <br>
     * @return <br>
     */
    boolean logicRemoveById(Long id);

    /**
     * Title：page <br>
     * Description：规则分页查询 <br>
     * author：王磊 <br>
     * date：2021/10/20 11:21 <br>
     * @param dto <br>
     * @return <br>
     */
    IPage<DroolsRuleVO> page(DroolsRulePageDTO dto);

    /**
     * Title：disposeSession <br>
     * Description：销毁规则引擎 <br>
     * author：王磊 <br>
     * date：2021/11/11 13:52 <br>
     * @param kieSession <br>
     * @return <br>
     */
    void disposeSession(KieSession kieSession);

    /**
     * Title：getKieSessionFromDrl <br>
     * Description：构建规则引擎的session<br>
     * author：王磊 <br>
     * date：2021/11/11 13:52 <br>
     * @param code <br>
     * @return <br>
     */
    KieSession getKieSessionFromDrl(String code) throws UnsupportedEncodingException;

}
