package com.swcares.aps.basic.data.remoteapi.api;

import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.PassengerBasicInfoVO;

import java.util.List;

/**
 * @ClassName：BasicDataService
 * @Description：提供给业务方使用的对外接口：旅客、基础数据获取
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/15 9:57
 * @version： v1.0
 */
public interface PassengerBasicDataService {


    /***
     * @title getPassengerInfo
     * @description 获取旅客数据
     * <AUTHOR>
     * @date 2024/5/15 9:59
     * @param dto
     * @return java.util.List<com.swcares.aps.basic.data.model.vo.PassengerBasicInfoVO>
     */
    List<PassengerBasicInfoVO> getPassengerInfo(PassengerQueryDTO dto);

}
