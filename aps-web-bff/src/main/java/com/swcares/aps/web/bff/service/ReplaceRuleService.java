package com.swcares.aps.web.bff.service;

import com.swcares.aps.compensation.model.replace.dto.ReplaceBaseRuleDTO;
import com.swcares.aps.compensation.model.replace.dto.ReplacePayPeriodRuleDTO;
import com.swcares.aps.compensation.model.replace.vo.ReplaceBaseRuleVO;
import com.swcares.aps.compensation.model.replace.vo.ReplacePayPeriodRuleVO;
import com.swcares.baseframe.common.base.BaseResult;

/**
 * ClassName：com.swcares.aps.compensation.bff.web.service.impl <br>
 * Description：代领人规则<br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 01月10日 12:22 <br>
 * @version v1.0 <br>
 */
public interface ReplaceRuleService {

    /**
     * Title： saveBaseRule<br>
     * Description： 保存规则<br>
     * author：陈明东 <br>
     * date：2022/01/10 13:04 <br>
     * @param replaceRuleDTO
     * @return
     */
    BaseResult<Object> saveBaseRule(ReplaceBaseRuleDTO replaceRuleDTO);

    /**
     * Title： getBaseRule<br>
     * Description： 获取当前配置的规则<br>
     * author：陈明东 <br>
     * date：2022/01/10 13:04 <br>
     * @param
     * @return
     */
    BaseResult<ReplaceBaseRuleVO> getBaseRule();

    /**
     * Title： savePayWaitPeriod<br>
     * Description： 保存支付等待期规则<br>
     * author：陈明东 <br>
     * date：2022/01/10 13:04 <br>
     * @param replacePayPeriodRuleDTO
     * @return
     */
    BaseResult<Object> savePayWaitPeriod(ReplacePayPeriodRuleDTO replacePayPeriodRuleDTO);

    /**
     * Title： getPayWaitPeriod<br>
     * Description： 获取当前配置的支付等待期规则<br>
     * author：陈明东 <br>
     * date：2022/01/10 13:04 <br>
     * @param
     * @return
     */
    BaseResult<ReplacePayPeriodRuleVO> getPayWaitPeriod();

}
