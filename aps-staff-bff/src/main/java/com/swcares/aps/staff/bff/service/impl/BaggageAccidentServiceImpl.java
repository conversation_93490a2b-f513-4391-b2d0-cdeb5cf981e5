package com.swcares.aps.staff.bff.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.swcares.aps.basic.data.businessimpl.model.dto.CompensationPaxSearchDTO;
import com.swcares.aps.basic.data.businessimpl.model.vo.FlightFindVO;
import com.swcares.aps.basic.data.remoteapi.model.dto.FlightBaseQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightBasicnfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.PassengerBasicInfoVO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.CheckPaxBagTagDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.PaxMaintainSearchDTO;
import com.swcares.aps.compensation.model.irregularflight.vo.AccidentFindFlightVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderPaxInfoVO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.aps.staff.bff.constant.BaggagePassengerException;
import com.swcares.aps.staff.bff.service.BaggageAccidentService;
import com.swcares.aps.staff.bff.vo.FlightAndPassengerVO;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import com.swcares.components.encrypt.FieldEncryptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * ClassName： <br>
 * Description： <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/7 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class BaggageAccidentServiceImpl implements BaggageAccidentService {

	@Autowired
	private CompensationInfoApi compensationInfoApi;

	@Autowired
	private FieldEncryptor fieldEncryptor;

	/**
	 * @title getFlightAndPassenger
	 * @description @TODO
	 * <AUTHOR>
	 * @date 2022/3/7 14:06
	 * @param dto
	 * @return com.swcares.aps.staff.bff.vo.FlightAndPassengerVO
	 */
	@Override
	public FlightAndPassengerVO getFlightAndPassenger(CompensationPaxSearchDTO dto) {
		FlightAndPassengerVO flightAndPassengerVO = new FlightAndPassengerVO();
		CompensationOrderPaxInfoVO baggagePassenger = findBaggagePassengers(dto);
		flightAndPassengerVO.setCompensationOrderPaxInfoVO(baggagePassenger);
		FlightFindVO data = compensationInfoApi.getFlight(dto.getFlightDate(), dto.getFlightNo(), baggagePassenger.getSegment()).getData();
		if(ObjectUtils.isEmpty(data)){
			throw new BusinessException(BaggagePassengerException.NOT_EXISTENCE_FLIGHT);
		}
		AccidentFindFlightVO accidentFindFlightVO = ObjectUtils.copyBean(data,AccidentFindFlightVO.class);
		//计划出发到达时间：优先级进行判断和展示，优先实际，然后预计，最后计划。
		accidentFindFlightVO.setStd(StringUtils.isNotEmpty(data.getAtd())?data.getAtd():(StringUtils.isNotEmpty(data.getEtd())?data.getEtd():data.getStd()));
		accidentFindFlightVO.setSta(StringUtils.isNotEmpty(data.getAta())?data.getAta():(StringUtils.isNotEmpty(data.getEta())?data.getEta():data.getSta()));

		flightAndPassengerVO.setAccidentFindFlightVO(accidentFindFlightVO);
		return flightAndPassengerVO;
	}

	@Override
	public BaseResult<Object> checkIsNotOtherPaxBagTag(CheckPaxBagTagDTO dto) {
		PassengerQueryDTO passengerQueryDTO = new PassengerQueryDTO();
		passengerQueryDTO.setPaxId(dto.getPaxId());
		passengerQueryDTO.setBagTag(dto.getBagTag());

		List<PassengerBasicInfoVO> flightPaxInfos=compensationInfoApi.getPassengers(passengerQueryDTO).getData();
		boolean notOtherPaxBagTagFlag=false;
		if(CollectionUtils.isNotEmpty(flightPaxInfos)){
			notOtherPaxBagTagFlag=flightPaxInfos.stream().anyMatch(t->!StringUtils.equalsIgnoreCase(t.getPaxId(),dto.getPaxId()));
		}
		if(notOtherPaxBagTagFlag){
			throw new BusinessException(BaggagePassengerException.NOT_PASSENGER_BAG);
		}
		return BaseResult.ok();
	}

	private CompensationOrderPaxInfoVO findBaggagePassengers(CompensationPaxSearchDTO dto){
		FlightBaseQueryDTO queryDTO = new FlightBaseQueryDTO();
		queryDTO.setFlightDate(dto.getFlightDate());
		queryDTO.setFlightNo(dto.getFlightNo());
		//查询是否有当日的航班
		List<FlightBasicnfoVO> flightInfos = compensationInfoApi.getFlightBasicInfo(queryDTO).getData();
		if(ObjectUtils.isEmpty(flightInfos)){
			throw new BusinessException(BaggagePassengerException.NOT_EXISTENCE_FLIGHT);
		}
		log.info("【异常行李事故单】前端传入参数【{}】", JSONUtil.toJsonStr(dto));
//		dto.setIdNo(fieldEncryptor.encrypt(dto.getIdNo()));
		PassengerQueryDTO passengerQueryDTO = new PassengerQueryDTO();
		BeanUtils.copyProperties(dto,passengerQueryDTO);

		List<PassengerBasicInfoVO> list = compensationInfoApi.getPassengers(passengerQueryDTO).getData();
		if(CollectionUtils.isEmpty(list)) {
			throw new BusinessException(BaggagePassengerException.NOT_EXIST_PASSENGER);
		}
		log.info("【异常行李事故单】旅客查询结果【{}】", JSONUtil.toJsonStr(list));
		CompensationOrderPaxInfoVO result = ObjectUtils.copyBean(list.get(0),CompensationOrderPaxInfoVO.class);
//		result.setIdNo(fieldEncryptor.decrypt(result.getIdNo()));
		if(list.size() > 1){
			StringBuffer sb = new StringBuffer();
			Set<String> tktNoSet = new HashSet();
			Set<String> bagTagList = new HashSet();
			list.forEach(baggagePaxInfoVO->{
				CompensationOrderPaxInfoVO compensationOrderPaxInfoVO = ObjectUtils.copyBean(baggagePaxInfoVO,CompensationOrderPaxInfoVO.class);
				//箱包号封装
				if (wrapBagTagList(compensationOrderPaxInfoVO)) {
					//分割行李号，封装为list,利用set去重
					bagTagList.addAll(compensationOrderPaxInfoVO.getBagTagList());
				}
				//票号封装
				if(ObjectUtils.isNotEmpty(compensationOrderPaxInfoVO.getTktNo())) {
					tktNoSet.add(compensationOrderPaxInfoVO.getTktNo());
				}
			});
			tktNoSet.forEach(t->{
				if(sb.length() > 0) sb.append("/");
				sb.append(t);
			});
			result.setBagTagList(new ArrayList<>(bagTagList));
			result.setTktNo(sb.toString());
		}
		else{
			wrapBagTagList(result);
		}
		PaxMaintainSearchDTO searchDTO = ObjectUtils.copyBean(dto,PaxMaintainSearchDTO.class);
		searchDTO.setIdNo(fieldEncryptor.encrypt(dto.getIdNo()));
		Integer number = compensationInfoApi.getBaggageAccidentNumber(searchDTO).getData();
		result.setExistAccidentNumber(number);
		//如果通过行李号查询时，只能返回当前查询用的行李号（避免多个行李时返回多个）
		if(ObjectUtils.isNotEmpty(dto.getBagTag())){
			result.setBagTag(dto.getBagTag());
		}else if(ObjectUtils.isNotEmpty(result.getBagTagList())){
			result.setBagTag(result.getBagTagList().get(0));
		}
		result.setSegmentCh(compensationInfoApi.getAlternateAndStop(dto.getFlightDate(), dto.getFlightNo()).getData());
		return result;
	}

	private boolean wrapBagTagList(CompensationOrderPaxInfoVO compensationOrderPaxInfoVO) {
		if(StringUtils.isEmpty(compensationOrderPaxInfoVO.getBagTag())){
			return false;
		}
		if(compensationOrderPaxInfoVO.getBaggageCount()>1){
			PassengerQueryDTO queryDTO = new PassengerQueryDTO();
			queryDTO.setPaxId(compensationOrderPaxInfoVO.getPaxId());
			List<PassengerBasicInfoVO> compensationOrderPaxInfoVOS = compensationInfoApi.getPassengers(queryDTO).getData();
			String collect = compensationOrderPaxInfoVOS.stream().map(PassengerBasicInfoVO::getBagTag).collect(Collectors.joining(","));
			compensationOrderPaxInfoVO.setBagTag(collect);
		}

		//分割行李号，封装为list
		String[] strings = compensationOrderPaxInfoVO.getBagTag().split(",");
		List<String> list = new ArrayList<>();
		for (String str : strings) {
			list.add(str);
		}
		compensationOrderPaxInfoVO.setBagTagList(list);

		return true;
	}

}
