package com.swcares.aps.staff.bff.controller;

import com.swcares.aps.basic.data.businessimpl.model.dto.CompensationPaxSearchDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.CheckPaxBagTagDTO;
import com.swcares.aps.staff.bff.service.BaggageAccidentService;
import com.swcares.aps.staff.bff.vo.FlightAndPassengerVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * ClassName：PassengerController <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/3 <br>
 * @version v1.0 <br>
 */

@RestController
@RequestMapping("/h5")
@Api(tags = "旅客相关接口")
@ApiVersion(value = "工作人员端v1.0")
@RefreshScope
public class PassengerController extends BaseController {

	@Autowired
	private BaggageAccidentService baggageAccidentService;

	@Autowired
	private RestTemplate restTemplate;

	@Value("${ocr.id-card.url}")
	private String OCR_ID_CARD_API_URL;

	@Value("${ocr.bank-card.url}")
	private String OCR_BANK_CARD_API_URL;

	// 身份证识别接口转发
	@PostMapping("/passenger/recognize-id-card")
	@ApiOperation(value = "身份证识别")
	public BaseResult<Map<String, Object>> recognizeIdCard(@RequestParam("file") MultipartFile file) {
		return postOcrInterface(OCR_ID_CARD_API_URL, file);
	}

	// 银行卡识别接口转发
	@PostMapping("/passenger/recognize-bank-card")
	@ApiOperation(value = "银行卡识别")
	public BaseResult<Map<String, Object>> recognizeBankCard(@RequestParam("file") MultipartFile file) {
		return postOcrInterface(OCR_BANK_CARD_API_URL, file);
	}

	private BaseResult<Map<String, Object>> postOcrInterface(String url, MultipartFile file){
		// 创建请求头
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.MULTIPART_FORM_DATA);

		// 创建请求体
		MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
		body.add("file", file.getResource());

		// 创建HttpEntity
		HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

		return BaseResult.ok(restTemplate.postForObject(url, requestEntity, Map.class));
    }

	@PostMapping("/passenger/baggage/getFlightAndPassenger")
	@ApiOperation(value = "获取航班和旅客信息")
	public BaseResult<Object> getFlightAndPassenger(@RequestBody@Validated CompensationPaxSearchDTO dto){
		FlightAndPassengerVO flightAndPassenger = baggageAccidentService.getFlightAndPassenger(dto);
		return ok(flightAndPassenger);
	}

	@PostMapping("/passenger/baggage/checkBagTag")
	@ApiOperation(value = "异常行李事故单校验行李号接口")
	public BaseResult<Object> checkBagTag(@RequestBody CheckPaxBagTagDTO dto){
		return baggageAccidentService.checkIsNotOtherPaxBagTag(dto);
	}
}
