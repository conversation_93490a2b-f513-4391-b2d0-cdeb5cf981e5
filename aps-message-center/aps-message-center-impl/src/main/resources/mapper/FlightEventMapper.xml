<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.msg.mapper.FlightEventMapper">
    <sql id="flightEventResult">
        SELECT
        ID,
        MESSAGE_ID,
        SSMID,
        ADJUST_TYPE,
        PROCESS_TYPE,
        ADJUST_REASON,
        REMARK,
        FORECAST_NUMBER,
        FORECAST_TYPE,
        FLIGHT_NUMBER,
        ORG,
        DEST,
        DEP_TIME,
        ARR_TIME,
        FREQUENCY,
        BOUND,
        PLANE_TYPE,
        NEW_PLANE_TYPE,
        LAYOUT,
        NEW_LAYOUT,
        SMS_TASK_COUNT,
        PASSENGER_COUNT,
        START_DATE,
        END_DATE,
        IMPORT_TIME,
        CREATED_TIME,
        CREATED_BY,
        UPDATED_TIME,
        UPDATED_BY
        FROM MSG_FLIGHT_EVENT MFE
    </sql>
    <sql id="searchCriteriaOracle">
        WHERE 1=1
        <if test = "dto.flightNumber != null and dto.flightNumber != ''">
            AND MFE.FLIGHT_NUMBER = #{dto.flightNumber}
        </if>
        <if test = "dto.flightDateEnd != null and dto.flightDateEnd != ''">
            AND NOT (MFE.START_DATE <![CDATA[ > ]]> to_date(#{dto.flightDateEnd},'yyyy-MM-dd') )
        </if>
        <if test = "dto.flightDateStart != null and dto.flightDateStart != ''">
            AND NOT (MFE.END_DATE <![CDATA[ < ]]> to_date(#{dto.flightDateStart}, 'yyyy-MM-dd') )
        </if>
        <if test="dto.frequency != null and dto.frequency.size() >0">
            AND
            <foreach collection="dto.frequency" index="index" item="dayOfWeek" separator=" OR " open=" ( " close=" ) ">
                MFE.FREQUENCY like '%'||#{dayOfWeek}||'%'
            </foreach>
        </if>
        <if test="dto.adjustType != null and dto.adjustType.size() >0">
            AND MFE.ADJUST_TYPE IN
            <foreach collection="dto.adjustType" index="index" item="type" separator="," open="(" close=")">
                #{type}
            </foreach>
        </if>
        <if test = "dto.adjustReason != null and dto.adjustReason != ''">
            AND MFE.ADJUST_REASON LIKE '%'||#{dto.adjustReason}||'%'
        </if>
        <if test = "dto.org != null and dto.org != ''">
            AND MFE.ORG = #{dto.org}
        </if>
        <if test = "dto.dest != null and dto.dest != ''">
            AND MFE.DEST = #{dto.dest}
        </if>
    </sql>
    
    <select id="getFlightEvents" resultType="com.swcares.aps.msg.model.entity.FlightEvent" databaseId="oracle">
        <include refid="flightEventResult"/>
        <include refid="searchCriteriaOracle"/>
        ORDER BY IMPORT_TIME DESC
    </select>
    

</mapper>
