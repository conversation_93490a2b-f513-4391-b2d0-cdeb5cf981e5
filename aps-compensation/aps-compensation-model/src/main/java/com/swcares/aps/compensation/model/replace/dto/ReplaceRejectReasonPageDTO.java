package com.swcares.aps.compensation.model.replace.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.aps.compensation.model.replace.dto.ReplaceRejectReasonPageDTO <br>
 * Description： 代人领取审核拒绝原由分页对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-12 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="ReplaceRejectReasonPageDTO分页对象", description="代人领取审核拒绝原由分页对象")
public class ReplaceRejectReasonPageDTO extends PagedDTO {

    @ApiModelProperty(value = "配置主类型，这里为固定值AUDIT_REASON")
    private String type;

    @ApiModelProperty(value = "配置子类型，这里为固定值REJECT_REASON")
    private String subType;

}
