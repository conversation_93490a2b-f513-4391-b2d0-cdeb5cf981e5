package com.swcares.aps.compensation.model.compensation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CompensationMaterialDetailMaterialVO
 * @Description：箱包补偿单详情下实物信息VO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 唐康
 * @Date： 2022/4/14 14:55
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationMaterialDetailMaterialVO对象", description="箱包补偿单详情下实物信息VO")
public class CompensationMaterialDetailMaterialVO {
    @ApiModelProperty(value = "箱包品牌")
    private String materialBrand;

    @ApiModelProperty(value = "箱包名称")
    private String materialName;

    @ApiModelProperty(value = "尺寸")
    private String materialSize;

    @ApiModelProperty(value = "单价")
    private String materialUnivalent;

    @ApiModelProperty(value = "数量")
    private String amount;

    @ApiModelProperty(value = "补偿总金额")
    private String sumMoney;

    @ApiModelProperty(value = "补偿原因")
    private String remark;

    @ApiModelProperty(value = "赔偿实物id")
    private String materialId;

    @ApiModelProperty(value = "剩余库存")
    private String stock;

}
