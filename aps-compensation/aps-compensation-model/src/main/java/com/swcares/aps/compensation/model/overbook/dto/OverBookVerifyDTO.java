package com.swcares.aps.compensation.model.overbook.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName：OverBookVerifyDTO
 * @Description：h5-新建航班旅客信息验证
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/28 10:09
 * @version： v1.0
 */
@Data
public class OverBookVerifyDTO {
    @ApiModelProperty(value = "航班号")
    @NotNull
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    @NotNull
    private String flightDate;

    @ApiModelProperty(value = "凭证类型 1证件号 2票号 ")
    @NotNull
    private String searchType;

    @ApiModelProperty(value = "凭证号")
    @NotNull
    private String searchInfo;

}
