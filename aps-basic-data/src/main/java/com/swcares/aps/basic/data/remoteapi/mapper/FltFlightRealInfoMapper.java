package com.swcares.aps.basic.data.remoteapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.aps.basic.data.remoteapi.model.dto.FlightBaseQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.dto.FlightInfoDTO;
import com.swcares.aps.basic.data.remoteapi.model.entity.FltFlightRealInfo;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightBasicnfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightUnitInfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.SegmentFindVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface FltFlightRealInfoMapper extends BaseMapper<FltFlightRealInfo> {

    /**
     * @title getSegment
     * @description 赔付-获取航段
     * <AUTHOR>
     * @date 2022/7/27 9:18
     * @param date
     * @param flightNo
     * @return java.util.List<com.swcares.aps.flightpassenger.model.flight.vo.SegmentFindVO>
     */
    @Select("select  CONCAT(get_city_name(ffi.ORG),ffi.ORG) departPortCH ,CONCAT(get_city_name(ffi.DST),ffi.DST) arrivalPortCH,ORG departPort,ffi.DST arrivalPort  " +
            "from flt_flight_real_info ffi " +
            "where TO_CHAR(FLIGHT_DATE,'yyyy-mm-dd') =#{date} and FLIGHT_NUMBER = #{flightNo}")
    List<SegmentFindVO> getSegment(String date, String flightNo);

    /**
     * 通过 航班四要素等获取航班信息
     * @return
     */
    List<FlightBasicnfoVO> getFlightInfo(@Param("dto") FlightBaseQueryDTO dto);

    /**
     * @param flightInfoDTO <br>
     * @return <br>
     * @Title：getFlightUnitInfo <br>
     * @Description：通过航班信息来获取机组信息 <br>
     * @author：王磊 <br>
     * @date：2022/3/15 11:17 <br>
     */
    List<FlightUnitInfoVO> getFlightUnitInfo(@Param("dto") FlightInfoDTO flightInfoDTO);
}
