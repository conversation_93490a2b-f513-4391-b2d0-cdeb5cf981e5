package com.swcares.aps.compensation.model.overbook.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName：OverBookVerifyDTO
 * @Description 旅客信息验证
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/28 10:09
 * @version： v1.0
 */
@Data
public class OverBookPaxSearchDTO {
    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "旅客id")
    private String paxId;

    @ApiModelProperty(value = "事故单类型- 不正常航班,异常行李,超售")
    private String accidentType;
}
