package com.swcares.aps.compensation.model.irregularflight.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.aps.compensation.model.irregularflight.vo <br>
 * Description：赔偿单审核- 选择审核人列表 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 11月24日 13:23 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="CompensationOrderAuditorVo对象", description="赔偿单审核-选择审核人")
public class CompensationOrderAuditorVo {

    @ApiModelProperty(value = "审核人id")
    private String auditorId;

    @ApiModelProperty(value = "审核人姓名（工号）")
    private String auditor;

    @ApiModelProperty(value = "机构名称")
    private String organization;

    @ApiModelProperty(value = "联系方式")
    private String telephone;


}
