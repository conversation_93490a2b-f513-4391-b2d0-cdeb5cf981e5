<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.ground.assurance.mapper.AssuranceOrderInfoMapper">
    <update id="logicRemoveById">
        update ASSURANCE_ORDER_INFO set DELETED=1 where id = #{id}
    </update>

    <select id="pageQueryOrderList" resultType="com.swcares.aps.ground.models.assurance.vo.AssuranceOrderListVO">
        select t.*,
        CASE t.ORDER_STATE
        WHEN 'DRAFT' THEN 0
        WHEN 'AUDIT_BACK' THEN 1
        WHEN 'AUDIT_ING' THEN 2
        WHEN 'SERVICE_ING' THEN 3
        WHEN 'SERVICE_FINISH' THEN 4
        WHEN 'AUDIT_REJECT' THEN 5
        WHEN 'SERVICE_OVER_TIME' THEN 6
        WHEN 'SERVICE_CLOSE' THEN 7
        else 100 end as orderIndex
        from ASSURANCE_ORDER_INFO t where DELETED=0
        <if test="dto.flightNo!=null and dto.flightNo!='' ">and FLIGHT_NO = #{dto.flightNo}</if>
        <if test="dto.flightDate!=null">and FLIGHT_DATE = #{dto.flightDate}</if>
        <if test="dto.startFlightDate!=null">and FLIGHT_DATE &gt;= #{dto.startFlightDate}</if>
        <if test="dto.endFlightDate!=null">and FLIGHT_DATE &lt;=  #{dto.endFlightDate}</if>
        <if test="dto.org!=null and dto.org!='' ">and FLIGHT_SEGMENTS like '%' || #{dto.org} || '-%' </if>
        <if test="dto.dst!=null and dto.dst!='' ">and FLIGHT_SEGMENTS like '%-' || #{dto.dst} || '%'</if>
        <if test="dto.orderNo!=null and dto.orderNo!='' ">and ORDER_NO = #{dto.orderNo}</if>
        <if test="dto.serviceAirport!=null and dto.serviceAirport!='' ">and SERVICE_AIRPORT = #{dto.serviceAirport}</if>
        <if test="dto.serviceAirports!=null and dto.serviceAirports.size()>0 ">
            and SERVICE_AIRPORT in
            <foreach collection="dto.serviceAirports" open="(" close=")" separator="," item="airport">
                #{airport}
            </foreach>
        </if>
        <if test="dto.createdBy!=null and dto.createdBy!='' ">and CREATED_BY like '%' || #{dto.createdBy} ||'%'</if>
        <if test="dto.submitBy!=null and dto.submitBy!='' ">and SUBMIT_BY like '%' || #{dto.submitBy} ||'%'</if>
        <if test="dto.startCreatedTime!=null">and CREATED_TIME &gt;= #{dto.startCreatedTime}</if>
        <if test="dto.endCreatedTime!=null">and CREATED_TIME &lt; (#{dto.endCreatedTime} + 1)</if>
        <if test="dto.startSubmitTime!=null">and LAST_SUBMIT_TIME &gt;= #{dto.startSubmitTime}</if>
        <if test="dto.endSubmitTime!=null">and LAST_SUBMIT_TIME &lt; (#{dto.endSubmitTime} + 1)</if>
        <if test="dto.orderState!=null and dto.orderState!='' ">and ORDER_STATE  = #{dto.orderState}</if>
        <if test="dto.orderStates!=null and dto.orderStates.size()>0 ">
            and ORDER_STATE in
            <foreach collection="dto.orderStates" open="(" close=")" separator="," item="state">
                #{state}
            </foreach>
        </if>
        <if test="dto.belongTenantType!=null and dto.belongTenantType!='' ">and BELONG_TENANT_TYPE  = #{dto.belongTenantType}</if>
        <if test="dto.belongTenantTypes!=null and dto.belongTenantTypes.size()>0 ">
            and BELONG_TENANT_TYPE in
            <foreach collection="dto.belongTenantType" open="(" close=")" separator="," item="bt">
                #{bt}
            </foreach>
        </if>
        <if test="dto.belongAirlineCode!=null and dto.belongAirlineCode!='' ">and BELONG_AIRLINE_CODE  = #{dto.belongAirlineCode}</if>
        <if test="dto.serviceTarget!=null and dto.serviceTarget!='' ">and SERVICE_TARGET = #{dto.serviceTarget}</if>
        <if test="dto.serviceTargets!=null and dto.serviceTargets.size()>0 ">
            and SERVICE_TARGET in
            <foreach collection="dto.belongTenantType" open="(" close=")" separator="," item="st">
                #{st}
            </foreach>
        </if>
        order by FLIGHT_DATE desc,orderIndex,LAST_SUBMIT_TIME desc,ID desc
    </select>


    <select id="selectOverTimeAssuranceOrder" resultType="com.swcares.aps.ground.models.assurance.entity.AssuranceOrderInfoDO">
        select * from ASSURANCE_ORDER_INFO where ORDER_STATE in ( 'SERVICE_ING','AUDIT_ING','AUDIT_BACK' ) and DELETED=0 and FIRST_SUBMIT_TIME &lt;=#{overTime} and BELONG_TENANT_TYPE =#{tenantType}
    </select>

    <select id="selectNeedRefreshWorkflowUserOrder" resultType="com.swcares.aps.ground.models.assurance.entity.AssuranceOrderInfoDO">
        SELECT * FROM  (SELECT
            o.*,
            (SELECT count(*) FROM WORKFLOW_AUDITOR_ID_INFO t WHERE t.BUSINESS_VALUE = o.ORDER_NO ) AS all_amount
        FROM
            ASSURANCE_ORDER_INFO o
        WHERE
            o.ORDER_STATE = 'AUDIT_ING'
            AND o.DELETED = 0 and o.BELONG_TENANT_TYPE =#{tenantType}) tmp WHERE tmp.all_amount=0
    </select>

    <select id="selectNeedFinishAssuranceOrder" resultType="com.swcares.aps.ground.models.assurance.entity.AssuranceOrderInfoDO">
        SELECT * FROM  (SELECT
            o.*,
            (SELECT count(*) FROM ASSURANCE_ORDER_SERVICE_VERIFY t WHERE t.ORDER_ID = o.ID ) AS all_amount,
            (SELECT count(*) FROM ASSURANCE_ORDER_SERVICE_VERIFY t WHERE t.ORDER_ID = o.ID AND t.VERIFICATION_STATE = 'CONFIRM') AS confirm_amount
        FROM
            ASSURANCE_ORDER_INFO o
        WHERE
            o.ORDER_STATE = 'SERVICE_ING'
            AND o.DELETED = 0 and o.BELONG_TENANT_TYPE =#{tenantType})  tmp WHERE tmp.all_amount=tmp.confirm_amount
    </select>
</mapper>