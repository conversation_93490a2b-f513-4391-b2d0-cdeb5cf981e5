package com.swcares.aps.compensation.model.dataconfig.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.aps.compensation.model.replace.entity.ReplaceRule <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-10 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("compensation_data_config")
@ApiModel(value="ReplaceConfig对象", description="")
public class DataConfigDO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "配置主类型")
    private String type;

    @ApiModelProperty(value = "机场或航司编码")
    private String airCode;

    @ApiModelProperty(value = "编码中文")
    private String codeCn;

    @ApiModelProperty(value = "配置子类型")
    private String subType;

    @ApiModelProperty(value = "配置值")
    private String value;

    @ApiModelProperty(value = "配置描述")
    private String description;

    @ApiModelProperty(value = "类型标签")
    private String typeLabel;

    @ApiModelProperty(value = "是否系统配置")
    private String isSys;

    @ApiModelProperty(value = "数据项是否是被逻辑删除的")
    private Boolean deleted;

}
