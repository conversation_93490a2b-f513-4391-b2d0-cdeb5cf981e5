package com.swcares.aps.basic.data.remoteapi.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：SegmentFindVO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/7/27 9:02
 * @version： v1.0
 */
@Data
@ApiModel(value="SegmentFindVO", description="")
public class SegmentFindVO {

    @ApiModelProperty(value = "出发航站中文拼接")
    private String departPortCH;

    @ApiModelProperty(value = "到达航站中文拼接")
    private String arrivalPortCH;

    @ApiModelProperty(value = "出发航站")
    private String departPort;

    @ApiModelProperty(value = "到达航站")
    private String arrivalPort;
}
