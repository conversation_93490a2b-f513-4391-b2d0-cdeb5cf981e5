package com.swcares.aps.basic.data.businessimpl.service;

import com.swcares.aps.basic.data.remoteapi.model.dto.FlightBaseQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.basic.data.businessimpl.model.vo.*;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightBasicnfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.PassengerBasicInfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.SegmentFindVO;

import java.util.List;

/**
 * @ClassName：CompensationBasicService
 * @Description：赔付模块需要用到的航班、旅客接口
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/14 14:00
 * @version： v1.0
 */
public interface CompensationBasicDataService {

    /**
     * @title getFlightList
     * @description 获取航班基础信息
     * <AUTHOR>
     * @date 2024/5/14 16:41
     * @param dto
     * @return java.util.List<com.swcares.aps.flightpassenger.model.base.vo.FlightBasicnfoVO>
     */
    List<FlightBasicnfoVO> getFlightBasicInfo(FlightBaseQueryDTO dto);

    /***
     * @title getPassengerInfo
     * @description 获取旅客信息
     * <AUTHOR>
     * @date 2024/5/14 17:03
     * @param dto
     * @return java.util.List<com.swcares.aps.flightpassenger.model.base.vo.PassengerBasicInfoVO>
     */
    List<PassengerBasicInfoVO> getPassengerInfo(PassengerQueryDTO dto);

    /**
     * @title findFltSegment
     * @description 获取航班航段
     * <AUTHOR>
     * @date 2024/5/14 13:12
     * @param flightDate
     * @param flightNo
     */
    List<SegmentFindVO> findFltSegment(String flightDate, String flightNo);

    /**
     * @title getFlight
     * @description 新建事故单-传入航段信息，查航班信息详情【业务】
     * <AUTHOR>
     * @date 2024/5/14 14:12
     * @param date
     * @param flightNo
     * @param choiceSegment
     * @return com.swcares.aps.flightpassenger.model.flight.vo.FlightFindVO
     */
    FlightFindVO getFlight(String date, String flightNo, String choiceSegment);

    /***
     * @title getFocFlightInfo
     * @description 新建（保存）补偿单-查询航班信息【业务】
     * <AUTHOR>
     * @date 2024/5/14 14:24
     * @param flightInfoDTO
     * @return java.util.List<com.swcares.aps.flightpassenger.model.flight.vo.FocFlightInfoVO>
     */
    List<FocFlightInfoVO> getFocFlightInfo(FocFlightInfoDTO flightInfoDTO);



}
