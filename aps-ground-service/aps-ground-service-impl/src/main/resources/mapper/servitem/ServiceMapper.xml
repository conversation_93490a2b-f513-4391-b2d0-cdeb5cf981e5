<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.ground.servitem.mapper.ServiceMapper">
    <update id="logicRemoveById">
        update GROUND_SERVICE set deleted = 1 where id =#{id}
    </update>

    <select id="pageQuery" resultType="com.swcares.aps.ground.models.servitem.vo.ServiceVO">
        SELECT
            t.ID,
            t.SERVICE_NAME,
            t.SERVICE_TYPE,
            t.SERVICE_LEVEL,
            t.SERVICE_TARGET,
            t.SERVICE_CONTENT,
            t.SERVICE_REMARK,
            t.SERVICE_STATE,
            t.TENANT_ID,
            t.TENANT_CODE,
            t.TENANT_TYPE,
            t.BELONG_TENANT_ID,
            t.BELONG_TENANT_CODE,
            t.BELONG_TENANT_TYPE,
            t.DELETED,
            t.CREATED_BY,
            t.UPDATED_BY,
            t.CREATED_TIME,
            t.UPDATED_TIME
        FROM GROUND_SERVICE t
        where t.deleted = 0
        <if test="dto.id!=null and dto.id!=''">
            and t.id =#{dto.id}
        </if>
        <if test="dto.ids!=null and dto.ids.size()>0 ">
            and t.id in
            <foreach collection="dto.ids" separator="," open="(" close=")" item="iid">
                #{iid}
            </foreach>
        </if>
        <if test="dto.serviceName!=null and dto.serviceName!=''">
            and t.SERVICE_NAME  like '%' || #{dto.serviceName} || '%'
        </if>
        <if test="dto.serviceLevel!=null and dto.serviceLevel!=''">
            and t.SERVICE_LEVEL = #{dto.serviceLevel}
        </if>
        <if test="dto.serviceTarget!=null and dto.serviceTarget!=''">
            and t.SERVICE_TARGET = #{dto.serviceTarget}
        </if>
        <if test="dto.serviceState!=null and dto.serviceState!=''">
            and t.SERVICE_STATE = #{dto.serviceState}
        </if>
        <if test="dto.belongTenantCode!=null and dto.belongTenantCode!=''">
            and t.BELONG_TENANT_CODE = #{dto.belongTenantCode}
        </if>
        <if test="dto.airlineCode!=null and dto.airlineCode!=''">
            and t.BELONG_TENANT_CODE = #{dto.airlineCode}
        </if>
        <if test="dto.belongTenantType!=null and dto.belongTenantType!=''">
            and t.BELONG_TENANT_TYPE = #{dto.belongTenantType}
        </if>
        <if test="dto.serviceTypes!=null and dto.serviceTypes.size()>0">
            and t.SERVICE_TYPE in
            <foreach collection="dto.serviceTypes" item="serviceType" separator="," open="(" close=")">
                #{serviceType}
            </foreach>
        </if>
        <if test="dto.airportCode!=null and dto.airportCode!=''">
            and t.id in (
            select SERVICE_ID from GROUND_SERVICE_APT where AIRPORT_CODE = #{dto.airportCode}
            )
        </if>
        order by SERVICE_STATE desc,UPDATED_TIME desc
    </select>
</mapper>