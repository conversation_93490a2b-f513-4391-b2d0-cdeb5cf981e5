package com.swcares.aps.web.bff.service.impl;

import com.swcares.aps.compensation.model.irregularflight.vo.FlightAccidentInfoDetailsVO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.aps.web.bff.service.FlightAccidentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * ClassName：com.swcares.aps.compensation.bff.web.service.impl <br>
 * Description：事故单BFF-service <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 10月26日 8:52 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
public class FlightAccidentServiceImpl implements FlightAccidentService {

    @Autowired
    private CompensationInfoApi compensationInfoApi;

    @Override
    public FlightAccidentInfoDetailsVO accidentDetailsById(Long id) {
        FlightAccidentInfoDetailsVO accidentInfoDetailsVO = compensationInfoApi.findById(id).getData();
        String s =compensationInfoApi.getAlternateAndStop(accidentInfoDetailsVO.getFlightDate(),accidentInfoDetailsVO.getFlightNo()).getData();
        if(ObjectUtils.isNotEmpty(s)){
            accidentInfoDetailsVO.setChoiceSegmentCh(accidentInfoDetailsVO.getChoiceSegmentCh()+","+s);
        }
        accidentInfoDetailsVO.setCompensationOrderVoList(compensationInfoApi.findCompensationOrderById(id).getData());
        return accidentInfoDetailsVO;
    }
}
