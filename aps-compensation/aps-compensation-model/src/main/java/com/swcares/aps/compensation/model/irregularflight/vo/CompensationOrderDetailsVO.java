package com.swcares.aps.compensation.model.irregularflight.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.aps.compensating.mode.irregularflight.vo <br>
 * Description：赔偿单信息 详情 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 10月28日 16:52 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="CompensationOrderDetailsVO对象", description="赔偿单信息详情")
public class CompensationOrderDetailsVO {

    @ApiModelProperty(value = "赔偿单主键id")
    private String id;

    @ApiModelProperty(value = "补偿单号")
    private String orderNo;

    @ApiModelProperty(value = "服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "所选航段中文")
    private String choiceSegmentCh;

    @ApiModelProperty(value = "0草稿 1驳回 2审核中 3审核通过 4生效 5关闭 6逾期 7审核不通过 - 取字典表")
    private String status;

    @ApiModelProperty(value = "保障服务（0不存在，1存在） ")
    private String ensureType;

    @ApiModelProperty(value = "补偿方式 1现金，2虚拟，3实物 - 取字典表")
    private String compensateType;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "合计金额")
    private String sumMoney;

    @ApiModelProperty(value = "已选旅客数量")
    private String choiceNum;

    @ApiModelProperty(value = "成人数量")
    private String adultNum;

    @ApiModelProperty(value = "补偿标准")
    private String compensateStandard;

    @ApiModelProperty(value = "儿童数量")
    private String childrenNum;

    @ApiModelProperty(value = "婴儿数量")
    private String babyNum;

    @ApiModelProperty(value = "是否可以进行审核，Y是 N否")
    private String toExamine;

    @ApiModelProperty(value = "审核节点id")
    private String taskId;

    @ApiModelProperty(value = "补偿单来源")
    private String source;

    @ApiModelProperty(value = "归属航司")
    private String belongAirline;

    @ApiModelProperty(value = "补偿单状态==事故类型：1不正常航班2异常行李3超售4旅客投诉")
    private String accidentType;

    private String compensateSubType;

}
