<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.component.pay.pay.service.chinapay.mapper.ChinaPayConfigMapper">
	<select id="selectOneConfig"
			resultType="com.swcares.aps.component.pay.pay.bean.chinapay.entity.ChinaPayConfigDO" databaseId="oracle">
		SELECT
			id,
			tenant_id,
			account_name,
			responsible_person,
			sign_flag,
			private_mer_id,
			public_mer_id,
			public_key,
			private_key,
			business_type,
			remark,
			status,
			pay_type,
			capital_pool_id,
			verify_file,
			sign_file,
			created_by,
			created_time,
			updated_by,
			updated_time
		FROM
			COMPENSATION_CHINAPAY_CONFIG
		WHERE
			private_mer_id = #{privateMerId} and tenant_Id = #{tenantId} and BUSINESS_TYPE = #{businessType}

	</select>
</mapper>
