package com.swcares.aps.compensation.model.compensation.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * @ClassName：CompensationOrderCommandsController
 * @Description：箱包补偿单列表查询信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 唐康
 * @Date： 2022/4/13 15:36
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationMaterialQueriesListVO对象", description="箱包补偿单列表查询信息VO")
public class CompensationMaterialQueriesListVO {
    @ApiModelProperty(value = "实物补偿单id")
    private String id;

    @ApiModelProperty(value = "补偿单状态 0草稿,1审核中,2审核不通过,3驳回,4审核通过,5生效,6关闭,7逾期")
    private String status;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "航段")
    private String paxSegment;

    @ApiModelProperty(value = "服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "补偿单号")
    private String orderNo;

    @ApiModelProperty(value = "补偿方式 1现金，2虚拟，3实物")
    private String compensateType;

    @ApiModelProperty(value = "补偿数量")
    private String amount;

    @ApiModelProperty(value = "补偿总金额")
    private String sumMoney;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "是否可以进行审核，Y是 N否")
    private String toExamine;

    @ApiModelProperty(value = "审核节点id")
    private String taskId;

    @ApiModelProperty(value = "是否为发起人")
    private Boolean isSponsor;

    @ApiModelProperty(value = "创建人id,仅用于判断是否为发起人",hidden = true)
    @JsonIgnore
    private String createdId;
}
