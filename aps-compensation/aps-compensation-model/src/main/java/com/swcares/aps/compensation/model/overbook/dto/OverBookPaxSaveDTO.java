package com.swcares.aps.compensation.model.overbook.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：OverBookSaveDTO
 * @Description：超售事故单-保存对象
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/28 11:13
 * @version： v1.0
 */
@Data
public class OverBookPaxSaveDTO {
    @ApiModelProperty(value = "旅客id")
    private String paxId;

    @ApiModelProperty(value = "手机号码")
    private String telephone;


}
