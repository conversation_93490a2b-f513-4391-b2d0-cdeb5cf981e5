package com.swcares.aps.compensation.model.overbook.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：OverBookPcSearchDTO
 * @Description：PC-列表筛选
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/29 9:27
 * @version： v1.0
 */
@Data
public class OverBookPcSearchDTO  extends PagedDTO {

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班起始日期")
    private String flightStartDate;

    @ApiModelProperty(value = "航班结束日期")
    private String flightEndDate;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "起始航站")
    private String orgCity;

    @ApiModelProperty(value = "到达航站")
    private String dstCity;

    @ApiModelProperty(value = "事故来源【单选,选择全部传null】 1机场/2航司")
    private String accidentSource;

    @ApiModelProperty(value = "归属航司【多选,选择全部传null】")
    private List<String> belongAirline;

    @ApiModelProperty(value = "事故类型（多选,选择全部传null）：1改签，2退票")
    private List<String> type;

    @ApiModelProperty(value = "事故单状态（多选,选择全部传null）（0草稿、1待处理、2处理中、3已结案、4作废）")
    private List<String> accidentStatus;
}

