package com.swcares.aps.web.bff.controller;

import com.swcares.aps.compensation.model.complaint.dto.ComplaintCompensationCreateDto;
import com.swcares.aps.compensation.model.complaint.dto.FastCreateComplaintInfoDto;
import com.swcares.aps.compensation.model.complaint.dto.PassengerSelectInfoDto;
import com.swcares.aps.compensation.model.complaint.vo.CompensationCountListVo;
import com.swcares.aps.compensation.model.complaint.vo.CompensationTypeVo;
import com.swcares.aps.compensation.model.complaint.vo.ComplaintCompensationCreateVo;
import com.swcares.aps.compensation.model.complaint.vo.ComplaintPassengerListVo;
import com.swcares.aps.compensation.remote.api.irregularflight.ComplaintAccidentApi;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/complaint/compensation")
@Api(tags = "旅客投诉补偿单统一接口")
@ApiVersion(value = "旅客投诉补偿单统一接口 v1.0.1")
public class ComplaintCompensationBffController {

    @Autowired
    private ComplaintAccidentApi complaintAccidentApi;

    @PostMapping("/select")
    @ApiOperation(value = "投诉旅客选择查询")
    public BaseResult<List<ComplaintPassengerListVo>> select(@RequestBody @Valid PassengerSelectInfoDto passengerSelectInfoDto){
        return complaintAccidentApi.select(passengerSelectInfoDto);
    }

    @PostMapping("/countList")
    @ApiOperation(value = "点击补偿次数用于查询旅客补偿总次数列表")
    public BaseResult<CompensationCountListVo> countList(@RequestBody @Valid PassengerSelectInfoDto passengerSelectInfoDto){
        return complaintAccidentApi.countList(passengerSelectInfoDto);
    }

    @PostMapping("/create")
    @ApiOperation(value = "保存补偿单草稿/生成补偿单/包含补偿单下一步")
    public BaseResult<Long> create(@RequestBody @Valid ComplaintCompensationCreateDto complaintCompensationCreateDto){
        return complaintAccidentApi.createCompensation(complaintCompensationCreateDto);
    }

    @ApiImplicitParam(name = "belongAirline",value = "归属航司",required = true)
    @GetMapping("/getCompensationType")
    @ApiOperation(value = "获取补偿方式")
    public BaseResult<List<CompensationTypeVo>> getCompensationType(String belongAirline,String accidentType){
        return complaintAccidentApi.getCompensationType(belongAirline,accidentType);
    }

    @ApiImplicitParam(name = "belongAirline",value = "归属航司",required = true)
    @GetMapping("/getCompensationAmount")
    @ApiOperation(value = "标准补偿金额")
    public BaseResult<String> getCompensationAmount(String belongAirline){
        return complaintAccidentApi.getCompensationAmount(belongAirline);
    }

    @PostMapping("/fast/check")
    @ApiOperation(value = "补偿单校验")
    BaseResult<ComplaintCompensationCreateVo> fastCheck(@RequestBody @Valid FastCreateComplaintInfoDto fastCreateComplaintInfoDto){
        return complaintAccidentApi.fastCheck(fastCreateComplaintInfoDto);
    }

    @PostMapping("/check")
    @ApiOperation(value = "补偿单校验")
    BaseResult<ComplaintCompensationCreateVo> check(@RequestBody @Valid ComplaintCompensationCreateDto complaintCompensationCreateDto){
        return complaintAccidentApi.check(complaintCompensationCreateDto);
    }

    @PostMapping("/fast/create")
    @ApiOperation(value = "保存补偿单草稿/生成补偿单")
    BaseResult<Long> fastCreate(@RequestBody @Valid FastCreateComplaintInfoDto fastCreateComplaintInfoDto){
        return complaintAccidentApi.fastCreate(fastCreateComplaintInfoDto);
    }
}
