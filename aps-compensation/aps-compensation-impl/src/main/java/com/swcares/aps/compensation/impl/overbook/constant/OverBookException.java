package com.swcares.aps.compensation.impl.overbook.constant;

/**
 * @title
 * @description超售异常code
 * @date 2024/5/30 15:29
 * @return
 */
public class OverBookException {
    //暂未查询到该旅客信息，请核实后重新输入
    public static final int NOT_FOUND_PAX = 60100;

    //更新数据失败
    public static final int UPDATE_DATA_ERROR = 60101;

    //数据不存在
    public static final int NOT_FIND_DATA_ERROR = 60102;

//    该购票证件号在系统中已有 x 单航班超售事故单，是否继续创建
    public static final int TIPS_ALIKE_ACCIDENT = 60103;

    //   该购票证件号在系统中已有 x 单航班超售补偿单
    public static final int TIPS_ALIKE_ORDER = 60104;

    //赔偿金额计算错误！
    public static final int CHECK_AMOUNT_ERROR = 60105;
    //选择的补偿规则，数据配置不存在
    public static final int SELECTED_RULE_RULE_NOT_FIND = 60106;
    //改签时长不满足补偿规则
    public static final int CHECK_TIME_MISMATCH_RULE_ERROR = 60107;
}
