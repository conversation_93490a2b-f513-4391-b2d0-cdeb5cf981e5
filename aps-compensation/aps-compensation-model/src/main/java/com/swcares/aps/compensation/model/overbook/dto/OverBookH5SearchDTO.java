package com.swcares.aps.compensation.model.overbook.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：OverBookH5SearchDTO
 * @Description：H5超售列表筛选
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/27 15:04
 * @version： v1.0
 */
@Data
public class OverBookH5SearchDTO extends PagedDTO {

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "凭证类型 1证件号 2票号 ")
    private String searchType;

    @ApiModelProperty(value = "凭证号")
    private String searchInfo;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班起始日期")
    private String flightStartDate;

    @ApiModelProperty(value = "航班结束日期")
    private String flightEndDate;

    @ApiModelProperty(value = "事故类型【逗号分隔】（多选,选择全部传null）：1改签，2退票")
    private String type;

    @ApiModelProperty(value = "事故单状态【逗号分隔】（多选,选择全部传null）（0草稿、1待处理、2处理中、3已结案、4作废）")
    private String accidentStatus;

    @ApiModelProperty(value = "事故类型（多选,选择全部传null）：1改签，2退票",hidden = true)
    private List<String> typeList;

    @ApiModelProperty(value = "事故单状态（多选,选择全部传null）（0草稿、1待处理、2处理中、3已结案、4作废）",hidden = true)
    private List<String> accidentStatusList;

}
