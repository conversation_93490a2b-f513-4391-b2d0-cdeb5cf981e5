package com.swcares.aps.compensation.model.compensation.vo;

import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CompensationBaseExtInfoVO
 * @Description：补偿单扩展基础信息，不同类型补偿单最基本的信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/9 17:20
 * @version： v1.0
 */
@Data
public abstract class CompensationBaseExtInfoVO extends CompensationOrderInfoDO {
    @ApiModelProperty(value = "是否可以进行审核，Y是 N否")
    private String toExamine;

    @ApiModelProperty(value = "审核节点id")
    private String taskId;
}
