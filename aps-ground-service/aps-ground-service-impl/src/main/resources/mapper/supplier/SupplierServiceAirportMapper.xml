<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.ground.supplier.mapper.SupplierServiceAirportMapper">

    <select id="getSupplierServiceAirportList" resultType="java.lang.String">
        select DISTINCT gsa.AIRPORT_CODE from GROUND_SUPPLIER_INFO gsi
        JOIN GROUND_SUPPLIER_SERVICE gss on GSI."ID" = GSS.SUPPLIER_ID
        JOIN GROUND_SUPPLIER_SERVICE_APT gsa on gsa.SUPPLIER_SERVICE_ID = gss.id
        where gsi.DELETED = 0
        and gss.DELETED = 0
        ORDER BY gsa.AIRPORT_CODE asc
    </select>
</mapper>