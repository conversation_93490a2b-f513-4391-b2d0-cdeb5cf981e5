/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：ReaptvDictApi <br>
 * Package：com.swcares.reaptv.usercenter.api.uc <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.usercenter.remote.api.uc;

import com.swcares.aps.component.dict.model.vo.DictCacheVO;
import com.swcares.aps.component.dict.model.vo.DictDataVO;
import com.swcares.baseframe.common.base.BaseResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * InterfaceName：com.swcares.reaptv.usercenter.api.uc.ReaptvDictApi <br>
 * Description：TODO：(这里用一句话描述这个接口的作用) <br>
 * <AUTHOR> <br>
 * date 2022/10/25 10:17 <br>
 * @version v1.0.0 <br>
 */
@FeignClient(name = "aps-user-center", path = "/api/aps-user-center", contextId = "ReaptvDictApi")
public interface ReaptvDictApi {

    @GetMapping({"/aps/common/dict/get"})
    public BaseResult<List<DictCacheVO>> getByDictType(
            @RequestParam String dictType);

    @GetMapping({"/aps/common/dict/get_all"})
    public BaseResult<Map<String, List<DictCacheVO>>> getAll();

    @GetMapping({"/aps/common/dict/get_dicts"})
    public BaseResult<Map<String, List<DictCacheVO>>> getDict(@RequestParam String... dictTypes) ;

    @GetMapping({"/aps/common/dict/get_dict"})
    public BaseResult<DictDataVO> findByValueAndType(@RequestParam String dictType, @RequestParam String dictItem) ;

}
