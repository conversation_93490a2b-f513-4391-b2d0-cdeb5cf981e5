/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：CustomerSmsController <br>
 * Package：com.swcares.reaptv.user.center.bff.controller.msg.controller <br> 
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.web.bff.controller;

import com.swcares.aps.msg.api.PassengerSmsRecordApi;
import com.swcares.aps.msg.model.dto.PassengerSmsSearchDTO;
import com.swcares.aps.msg.model.vo.MessageStatusStaticsVO;
import com.swcares.aps.msg.model.vo.PassengerSmsRecordVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import feign.Response;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.IOUtils;
import org.apache.ibatis.logging.Log;
import org.apache.ibatis.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Collection;
import java.util.List;

/**
 * ClassName：com.swcares.reaptv.user.center.bff.controller.msg.controller.CustomerSmsController <br>
 * Description：旅客短信发送记录前端接口
 * <AUTHOR> <br>
 * date 2022/9/14 5:44 <br>
 * @version v1.0.0 <br>
 */
@RestController
@RequestMapping({"/msg/sms/passenger"})
@Api(tags = {"旅客短信功能"})
@ApiVersion({"Sms Api v1.0"})
public class PassengerSmsController {
    @Autowired PassengerSmsRecordApi passengerSmsRecordApi;

    protected Log log = LogFactory.getLog(getClass());

    @PostMapping("/list")
    @ApiOperation("旅客短信发送记录分页查询")
    public PagedResult<List<PassengerSmsRecordVO>> searchPassengerSms(@RequestBody PassengerSmsSearchDTO searchDto){
        return passengerSmsRecordApi.getPassengerSmsRecord(searchDto);
    }

    @PostMapping("/statics")
    @ApiOperation("获取满足指定的条件的信息发送状态统计")
    public BaseResult<MessageStatusStaticsVO> getMessageStatusStatics(@RequestBody
            PassengerSmsSearchDTO searchDto){
        return passengerSmsRecordApi.getMessageStatusStatics(searchDto);
    }

    @GetMapping("/detail")
    @ApiOperation("获取指定记录的详情")
    public BaseResult<PassengerSmsRecordVO> getDetail(@RequestParam(name="id", required = true) Long id){
        return passengerSmsRecordApi.getDetail(id);
    }

    @PostMapping("/download")
    @ApiOperation("按照查询条件导出(下载)旅客短信发送记录")
    public void downloadPassengerSms(@RequestBody PassengerSmsSearchDTO searchDto, HttpServletResponse response,
            HttpServletRequest request){
        Response feignResponse = passengerSmsRecordApi.download(searchDto);
        downloadByFeign(response, feignResponse);
    }

    @PostMapping("/downloadByIds")
    @ApiOperation("按照指定的id列表导出(下载)旅客短信发送记录")
    public void downloadPassengerSms(@RequestBody List<Long> idList, HttpServletResponse response,
            HttpServletRequest request){
        Response feignResponse = passengerSmsRecordApi.downloadById(idList);
        downloadByFeign(response, feignResponse);
    }

    private void downloadByFeign(HttpServletResponse response, Response feignResponse) {
        response.setContentType("application/x-download;charset=UTF8");
        Collection<String> contentDispositionHeader =
                feignResponse.headers().get("Content-Disposition");
        response.setHeader("Content-Disposition", contentDispositionHeader.isEmpty() ?
                "" :
                contentDispositionHeader.iterator().next());

        Response.Body feignBody = feignResponse.body();
        try (OutputStream outputStream = response.getOutputStream();
                InputStream inputStream = feignBody.asInputStream()) {
            IOUtils.copy(inputStream, outputStream);
            outputStream.flush();
        } catch (IOException e) {
            log.error("导出旅客短信发送记录出现异常", e);
        }
    }
}
