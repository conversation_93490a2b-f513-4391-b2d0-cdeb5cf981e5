package com.swcares.aps.compensation.impl.apply.controller;

import com.swcares.aps.compensation.impl.apply.constants.ApplyErrors;
import com.swcares.aps.compensation.impl.apply.workflow.ApplyWorkflowService;
import com.swcares.aps.compensation.model.apply.dto.ApplyAuditDTO;
import com.swcares.aps.component.workflow.NodeNoticeProcessProxy;
import com.swcares.aps.component.workflow.dto.NodeNoticeDTO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * ClassName：ApplyAuditController <br>
 * Description：申领单审核 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/1/18 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/apply/order")
@Api(tags = "航延补偿申领单审核相关接口")
@ApiVersion(value = "申领单接口 v1.0")
@Slf4j
public class ApplyAuditController extends BaseController {

    @Autowired
    private ApplyWorkflowService applyWorkflowService;

    @Autowired
    private NodeNoticeProcessProxy nodeNoticeProcessProxy;

    @PostMapping("/audit")
    @ApiOperation(value = "新建航延补偿申领单信息表记录")
    public BaseResult<Object> audit(@RequestBody @Valid ApplyAuditDTO applyAudit) {
        if(StringUtils.isEmpty(applyAudit.getAuditorUserId())){
           String userId = String.valueOf(UserContext.getUserId());
            applyAudit.setAuditorUserId(userId);
        }
        try {
            applyWorkflowService.auditWorkflowProcess(applyAudit);
        }catch (Exception e){
            e.printStackTrace();
            if(e instanceof BusinessException){
                throw (BusinessException)e;
            }
            throw new BusinessException(ApplyErrors.AUDIT_ERROR);
        }
        return ok();
    }

    @PostMapping("/nextNodeNotice")
    @ApiOperation(value = "下一节点回调通知，CPC调用")
    public BaseResult<Object> nextNodeNotice(@RequestBody NodeNoticeDTO nodeNoticeDTO) {
        try {
            nodeNoticeProcessProxy.process(nodeNoticeDTO);
        }catch (Exception e){
            e.printStackTrace();
            if(e instanceof BusinessException){
                throw (BusinessException)e;
            }
            throw new BusinessException(ApplyErrors.AUDIT_ERROR);
        }
        return ok();
    }


    @GetMapping("/startWorkflow")
    @ApiOperation(value = "启动审核流程，调试测试使用")
    public BaseResult<Object> startWorkflow(String applyOrderId,String operatorUserId ) {

        try {
            applyWorkflowService.startWorkflow(applyOrderId,operatorUserId);
        }catch (Exception e){
            e.printStackTrace();
            if(e instanceof BusinessException){
                throw (BusinessException)e;
            }
            throw new BusinessException(ApplyErrors.AUDIT_ERROR);
        }
        return ok();
    }
}
