package com.swcares.aps.compensation.model.complaint.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(value="ComplaintAccidentCreateDto对象", description="新增旅客投诉事故单请求参数")
public class ComplaintAccidentCreateDto implements BaseDTO {

    @ApiModelProperty(value = "事故单id")
    private Long id;

    @ApiModelProperty(value = "投诉旅客信息")
    @NotNull(message = "投诉旅客信息不能为空")
    @Valid
    List<PassengerComplaintDto> passengerComplaintInfoList;

    @ApiModelProperty(value = "投诉旅客选择信息")
    @NotNull(message = "投诉旅客选择信息不能为空")
    @Valid
    PassengerSelectInfoDto passengerSelectInfo;

    @ApiModelProperty(value = "填写事故单信息")
    @NotNull(message = "填写事故单信息不能为空")
    @Valid
    PassengerCompensationInfoDto passengerCompensationInfoDto;

    @ApiModelProperty(value = "操作标识：0：草稿，1：生成事故单/下一步",example = "0：草稿，1：生成事故单/下一步" ,required = true)
    @NotBlank(message = "操作标识不能为空")
    private String flag;
}
