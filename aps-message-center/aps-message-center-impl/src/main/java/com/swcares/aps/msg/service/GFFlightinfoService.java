/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：GFFlightinofService <br>
 * Package：com.swcares.reaptv.msg.service <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.msg.model.entity.GFFlightInfo;
import com.swcares.aps.msg.model.vo.Segment;

import java.util.List;

/**
 * InterfaceName：com.swcares.reaptv.msg.service.GFFlightinofService <br>
 * Description：TODO：(这里用一句话描述这个接口的作用) <br>
 * <AUTHOR> <br>
 * date 2022/12/6 9:42 <br>
 * @version v1.0.0 <br>
 */
public interface GFFlightinfoService extends IService<GFFlightInfo> {
    public void mergeFlightInfo(GFFlightInfo flightInfo);

    List<Segment> getSegments(String flightNumber, String flightDate);

}
