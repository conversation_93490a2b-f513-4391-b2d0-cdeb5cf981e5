package com.swcares.aps.web.bff.controller.ground;

import com.alibaba.fastjson.JSONObject;
import com.swcares.aps.ground.GroundServiceApi;
import com.swcares.aps.ground.enums.EntityState;
import com.swcares.aps.ground.models.servitem.dto.ServiceQueryRequest;
import com.swcares.aps.ground.models.servitem.dto.ServiceSaveCommand;
import com.swcares.aps.ground.models.servitem.dto.ServiceStatusCommand;
import com.swcares.aps.ground.models.servitem.vo.ServiceVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName：ServiceItemController
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/16 11:22
 * @version： v1.0
 */
@RestController
@RequestMapping("/ground/service")
@Api(tags = {"服务项基础接口"})
@ApiVersion({"服务项相关Api"})
@Slf4j
public class ServiceController extends BaseController {

    @Autowired
    private GroundServiceApi groundServiceApi;

    @PostMapping("/save")
    @ApiOperation(value = "保存服务项信息")
    public BaseResult<Object> save(@RequestBody @Valid ServiceSaveCommand itemSaveDTO){
        log.info("保存服务项信息,{}", JSONObject.toJSONString(itemSaveDTO));
        return groundServiceApi.saveGroundService(itemSaveDTO);
    }

    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询服务项基础信息")
    public PagedResult<List<ServiceVO>> pageQuery(@RequestBody ServiceQueryRequest request){
        return groundServiceApi.pageQueryGroundService(request);
    }

    @GetMapping("/get/{id}")
    @ApiOperation(value = "根据ID查询服务项基础信息")
    public BaseResult<ServiceVO> detail(@PathVariable @ApiParam(value = "主键id", required = true) String id){
        ServiceQueryRequest request=new ServiceQueryRequest();
        request.setId(id);
        List<ServiceVO> data = groundServiceApi.pageQueryGroundService(request).getData();
        if(CollectionUtils.isNotEmpty(data)){
            return BaseResult.ok(data.get(0));
        }
        return BaseResult.ok();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除服务项信息")
    public BaseResult<Object> delete(@RequestBody List<String> ids){
        log.info("删除服务项信息,{}",ids);
        return groundServiceApi.deleteGroundService(ids);
    }

    @PostMapping("/stop")
    @ApiOperation(value = "停用服务项基础信息")
    public BaseResult<Object> stop(@RequestBody List<String> ids){
        log.info("停用服务项基础信息,{}",ids);
        List<ServiceStatusCommand> collect = ids.stream().map(id -> {
            ServiceStatusCommand commandDTO = new ServiceStatusCommand();
            commandDTO.setId(id);
            commandDTO.setTargetServiceState(EntityState.STOPPED.getCode());
            return commandDTO;
        }).collect(Collectors.toList());

        return groundServiceApi.changeGroundServiceStatus(collect);
    }
    @PostMapping("/active")
    @ApiOperation(value = "启用服务项基础信息")
    public BaseResult<Object> active(@RequestBody List<String> ids){
        log.info("启用服务项基础信息,{}",ids);

        List<ServiceStatusCommand> collect = ids.stream().map(id -> {
            ServiceStatusCommand commandDTO = new ServiceStatusCommand();
            commandDTO.setId(id);
            commandDTO.setTargetServiceState(EntityState.ACTIVE.getCode());
            return commandDTO;
        }).collect(Collectors.toList());
        return groundServiceApi.changeGroundServiceStatus(collect);
    }
}
