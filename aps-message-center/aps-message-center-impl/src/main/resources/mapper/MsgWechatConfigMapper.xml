<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.msg.mapper.MsgWechatConfigMapper">

    <!-- 根据clientId获取相应的wechat配置信息 -->
    <select id="getWechatConfigByClient" resultType="com.swcares.aps.msg.model.entity.MsgWechatConfig">
        SELECT id, client_id, app_id, app_secret, created_by,created_time, updated_by, updated_time FROM msg_wechat_config
        <where>
            client_id = #{clientId}
        </where>
    </select>
</mapper>
