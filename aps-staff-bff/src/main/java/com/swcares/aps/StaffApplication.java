package com.swcares.aps;

import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import com.travelsky.component.permissions.column.core.annotation.EnableColumnPermissions;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@EnableFeignClients
@EnableCreateCacheAnnotation
@EnableColumnPermissions
@ComponentScan(basePackages = {"com.swcares.*"})
@MapperScan("com.swcares.**.mapper")
@EnableDiscoveryClient
public class StaffApplication {
    public static void main(String[] args) {
        SpringApplication.run(StaffApplication.class, args);
    }
}
