/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：EmployeeSnsRecordService <br>
 * Package：com.swcares.reaptv.msg.service <br> 
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.msg.model.dto.EmployeeSnsSearchDTO;
import com.swcares.aps.msg.model.dto.EmployeeWechatTemplateMsgSendDTO;
import com.swcares.aps.msg.model.dto.MessageStatusStaticsDTO;
import com.swcares.aps.msg.model.dto.MultiMsgSendResult;
import com.swcares.aps.msg.model.entity.EmployeeSnsRecord;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * InterfaceName：com.swcares.reaptv.msg.service.EmployeeSnsRecordService <br>
 * Description：对员工发送的社交软件消息记录的服务
 * <AUTHOR> <br>
 * date 2022/10/8 15:24 <br>
 * @version v1.0.0 <br>
 */
public interface EmployeeSnsRecordService extends IService<EmployeeSnsRecord> {

    IPage<EmployeeSnsRecord> getEmployeeSnsRecordPage(EmployeeSnsSearchDTO dto);

    List<EmployeeSnsRecord> getByIds(List<Long> ids);

    void download(EmployeeSnsSearchDTO dto, HttpServletRequest request, HttpServletResponse response);

    void downLoad(List<Long> ids, HttpServletRequest request, HttpServletResponse response);

    MultiMsgSendResult sendTemplateWechatMessage(EmployeeWechatTemplateMsgSendDTO dto);

    boolean logicRemoveById(Long id);

    boolean batchDelete(List<Long> ids);

    List<MessageStatusStaticsDTO> getMessagesStatusStatics(EmployeeSnsSearchDTO dto);
}
