package com.swcares.aps.compensation.model.compensation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @ClassName：CompensationSMSDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 谭睿
 * @Date： 2022/9/1 14:19
 * @version： v1.0
 */
@Data
@ApiModel(value = "发送短信DTO",description = "发送短信所需参数DTO")
public class CompensationSMSDTO {
    @ApiModelProperty(value = "赔偿单单号")
    private String orderNo;

    @ApiModelProperty(value = "合计金额")
    private BigDecimal sumMoney;

    @ApiModelProperty(value = "补偿子类型-1延误，2取消，3备降，4返航，5补班; 21破损,22少收,23多收,24内件缺失,25丢失")
    private String accidentSubType;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "电话号码")
    @NotNull
    private String phone;

    @ApiModelProperty(value = "用户主键id")
    private String userId;

    @ApiModelProperty(value = "发送对象的用户名")
    private String userName;

    @ApiModelProperty(value = "验证码")
    private String authCode;

    @ApiModelProperty(value = "网页端跳转链接")
    private String webUrl;

    @ApiModelProperty(value = "小程序端跳转链接")
    private String h5Url;

//    @ApiModelProperty(value = "跳转链接")
//    private String url;
//

}
