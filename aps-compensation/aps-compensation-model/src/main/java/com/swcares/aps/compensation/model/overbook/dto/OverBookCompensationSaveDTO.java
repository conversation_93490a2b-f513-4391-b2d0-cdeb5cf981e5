package com.swcares.aps.compensation.model.overbook.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：OverBookCompensationSaveDTO
 * @Description：H5超售-补偿单信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/28 13:31
 * @version： v1.0
 */
@Data
public class OverBookCompensationSaveDTO {
    @ApiModelProperty(value = "事故单ID【补偿单编辑必传】")
    private Long accidentId;

    @ApiModelProperty(value = "赔偿单id【补偿单编辑必传】")
    private Long orderId;

    @ApiModelProperty(value = "赔偿单单号【补偿单编辑必传】")
    private String orderNo;

    @ApiModelProperty(value = "补偿单状态【保存草稿=0】 0草稿,1审核中,2审核不通过,3驳回,4审核通过,5生效,6关闭,7逾期")
    private String status;

    @ApiModelProperty(value = "补偿方式(补偿方式 1现金，2虚拟，3实物)")
    private String compensateType;

    @ApiModelProperty(value = "补偿子方式(补偿方式 1微信，2银联，3数字人民币)")
    private String compensateSubType;

    @ApiModelProperty(value = "补偿总金额")
    private String sumMoney;

    @ApiModelProperty(value = "1标准补偿金额 2自定义")
    private String ruleType;

   @ApiModelProperty(value = "标准补偿选择类型 1百分比 2固定金额")
    private String selectType;

/*   @ApiModelProperty(value = "超售规则id")
   private String overBookRuleId;*/

    @ApiModelProperty(value = "改签时长 2.2")
    private String overBookTime;

    @ApiModelProperty(value = "补偿航站")
    private String serviceCity;

    @ApiModelProperty(value = "补偿原因")
    private String remark;

    @ApiModelProperty(value = "原航班-计划起飞")
    private String std;

    @ApiModelProperty(value = "改签航班计划起飞")
    private String overBookStd;

}
