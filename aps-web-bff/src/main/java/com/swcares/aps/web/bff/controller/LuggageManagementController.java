package com.swcares.aps.web.bff.controller;

import com.swcares.aps.compensation.model.baggage.accident.dto.FindLuggageStockInfoDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageInfoDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageInfoPageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageStockInfoDTO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.FindLuggageStockInfoVO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageInfoVO;
import com.swcares.aps.compensation.model.irregularflight.dto.FlightAccidentIdDTO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @title: LuggageManagementController
 * @projectName aps
 * @description: 箱包信息以及库存管理bff接口
 * @date 2022/2/8 13:11
 */
@RestController
@RequestMapping("/luggage")
@Api(tags = "箱包信息以及库存管理")
@ApiVersion(value = "箱包管理v1.0")
public class LuggageManagementController extends BaseController {

    @Autowired
    private CompensationInfoApi compensationInfoApi;

    @GetMapping("/findSegment")
    @ApiOperation(value = "通过三字码查询航站信息")
    public BaseResult<Object> findSegment(@RequestParam(value = "code") String code){
        return compensationInfoApi.findSegment(code);
    }

    @PostMapping("/findLuggage")
    @ApiOperation(value = "箱包信息列表查询")
    public PagedResult<List<LuggageInfoVO>> findLuggageList(@RequestBody LuggageInfoPageDTO dto){
        return compensationInfoApi.findLuggageList(dto);
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增箱包信息")
    public BaseResult<Object> saveLuggage(@RequestBody @Validated LuggageInfoDTO dto){
        return compensationInfoApi.saveLuggage(dto);
    }

    @DeleteMapping("/remove")
    @ApiOperation(value = "通过id软删除箱包信息")
    public BaseResult<Object> removeLuggage(@RequestBody @ApiParam(value = "箱包id",required = true) FlightAccidentIdDTO idDto){
        return compensationInfoApi.removeLuggage(idDto.getId());
    }

    @PutMapping("/update")
    @ApiOperation(value = "通过id修改箱包信息")
    public BaseResult<Object> updateLuggage(@RequestBody @Validated LuggageInfoDTO dto){
        return compensationInfoApi.updateLuggage(dto);
    }

//    @PostMapping("/stock/save")
//    @ApiOperation(value = "新建库存耗存明细")
//    public BaseResult<Object> saveLuggageStockDetailed(@RequestBody LuggageStockInfoDTO dto){
//        return compensationInfoApi.saveLuggageStockDetailed(dto);
//    }

    //唐康
    @PostMapping("/stock/detailed")
    @ApiOperation(value = "通过id查询箱包库存明细")
    public PagedResult<List<FindLuggageStockInfoVO>> findLuggageStockDetailed(@RequestBody FindLuggageStockInfoDTO dto){
        return compensationInfoApi.findLuggageStockDetailed(dto);
    }

    @PutMapping("/stock/update")
    @ApiOperation(value = "箱包库存数量修改-增加/减少")
    public BaseResult<Object> updateLuggageStock(@RequestBody LuggageStockInfoDTO dto){
        return compensationInfoApi.updateLuggageStock(dto);
    }

}
