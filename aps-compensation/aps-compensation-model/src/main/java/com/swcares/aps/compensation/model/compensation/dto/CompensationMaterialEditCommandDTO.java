package com.swcares.aps.compensation.model.compensation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName：CompensationMaterialEditCommandDTO
 * @Description：实物补偿单修改
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 15:53
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationMaterialEditCommandDTO对象", description="实物补偿单修改参数")
public class CompensationMaterialEditCommandDTO extends CompensationMaterialAddCommandDTO implements CompensationEditCommand{

    /**
     * 补偿单ID
     */
    @ApiModelProperty(value = "补偿单ID")
    @NotNull
    private String compensationId;

}
