/*
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：MessageCenterApplication <br>
 * Package：com.swcares.reaptv.msg <br> 
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps;

import com.travelsky.component.permissions.column.core.annotation.EnableColumnPermissions;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * ClassName：com.swcares.reaptv.msg.MessageCenterApplication <br>
<<<<<<<<< Temporary merge branch 1
 * Description：TODO：(这里用一句话描述这个类的作用) <br>
=========
 * Description：消息中心的启动程序
>>>>>>>>> Temporary merge branch 2
 * <AUTHOR> <br>
 * date 2022/9/18 20:08 <br>
 * @version v1.0.0 <br>
 */
@SpringBootApplication
@EnableFeignClients(basePackages = {"com.swcares.*" })
@ComponentScan(basePackages = {"com.swcares.*"})
@EnableColumnPermissions
@MapperScan("com.swcares.**.mapper")
@EnableDiscoveryClient
@EnableAsync
@EnableTransactionManagement
@EnableConfigurationProperties
@EnableScheduling
public class MessageCenterApplication {
    public static void main(String[] args) {
        SpringApplication.run(MessageCenterApplication.class, args);
        SecurityContextHolder.setStrategyName(SecurityContextHolder.MODE_INHERITABLETHREADLOCAL);
    }
}
