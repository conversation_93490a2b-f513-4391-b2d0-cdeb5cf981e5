<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.msg.mapper.GFPassengerInfoMapper">
    <sql id="gfPassengerInfoResult">
        SELECT
            ID,
            FLIGHT_NUMBER,
            FLIGHT_DATE,
            ORG,
            DEST,
            NAME,
            TICKET_NUMBER,
            PNR,
            SEQUENCE_NUMBER,
            TELE_PHONE,
            VIP,
            GROUP_MEMBER,
            STANDBY,
            CREATED_TIME,
            CREATED_BY,
            UPDATED_TIME,
            UPDATED_BY
        FROM MSG_PASSENGER_INFO_GF MPIG
    </sql>
    <sql id="passengerCriteria">
        WHERE 1=1
        <if test="dto.flightNumber != null and dto.flightNumber != ''">
            AND MPIG.FLIGHT_NUMBER = #{dto.flightNumber}
        </if>
        <if test="dto.flightDate != null and dto.flightDate != ''">
            AND MPIG.FLIGHT_DATE = to_date(#{dto.flightDate}, 'yyyy-MM-dd')
        </if>
        <if test="dto.segments != null and dto.segments.size()>0">
            AND 
            <foreach collection="dto.segments" index="index" item="segment" separator=" OR " open="(" close=")">
                ( MPIG.ORG = #{segment.orgAirport} AND MPIG.DEST = #{segment.destAirport} )
            </foreach>
        </if>
        <if test="dto.pnr != null and dto.pnr != ''">
            AND MPIG.PNR = #{dto.pnr}
        </if>
        <if test="dto.passengerName != null and dto.passengerName != ''">
            AND MPIG.NAME = #{dto.passengerName}
        </if>
        <if test="dto.phoneNumber != null and dto.phoneNumber != ''">
            AND MPIG.TELE_PHONE like '%'||#{dto.phoneNumber}||'%'
        </if>
        <if test="dto.ticketNumber != null and dto.ticketNumber != ''">
            AND MPIG.TICKET_NUMBER = #{dto.ticketNumber}
        </if>
        <if test="dto.passengerType != null and dto.passengerType == '1'.toString()">
            AND MPIG.VIP='1'
        </if>
        <if test="dto.passengerType != null and dto.passengerType == '2'.toString()">
            AND MPIG.VIP='0'
        </if>
        <if test="dto.org != null and dto.org != ''">
           AND MPIG.ORG IN
                <foreach item="item" index="index" collection="dto.org.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
        </if>
        <if test="dto.dest != null and dto.dest != ''">
            AND MPIG.DEST IN
            <foreach item="item" index="index" collection="dto.dest.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>
    <select id="getPassengerInfoPage" resultType="com.swcares.aps.msg.model.entity.GFPassengerInfo">
        <include refid="gfPassengerInfoResult"/>
        <include refid="passengerCriteria"></include>
        ORDER BY VIP DESC, ORG ASC, DEST ASC, PNR ASC
    </select>
</mapper>
