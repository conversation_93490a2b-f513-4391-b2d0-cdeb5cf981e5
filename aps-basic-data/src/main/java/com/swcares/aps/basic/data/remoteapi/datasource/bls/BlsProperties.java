package com.swcares.aps.basic.data.remoteapi.datasource.bls;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * @ClassName：BlsProperties
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2025/3/19 14:17
 * @version： v1.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "thirdapi.bls")
public class BlsProperties {

/*yml配置 如
    thirdapi:
    bls:
    url: xxx.xxxaip
    tenants:
            - tenantid: 1897165836333715456
    secretkey:base-flight-info!123
    enable: true
            - tenantid: 456
    secretkey: 456@222
    enable: false
*/
    //对账通接口地址
    private String apiUrl;
    private String secretKey;
    //机场租户配置信息 每个机场对应一个租户
    private List<BlsTenantConfig> tenants;



    @Data
    public static class BlsTenantConfig {
        private String tenantId;
        private boolean enable;
    }
}
