package com.swcares.aps.compensation.model.compensation.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName：AddFlightCompensationDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 16:07
 * @version： v1.0
 */
@Data
public class AddFlightCompensationDTO {

    @ApiModelProperty(value = "所选航段")
    @NotNull
    private String choiceSegment;

    @ApiModelProperty(value = "航班号")
    @NotNull
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    @NotNull
    private String flightDate;

}
