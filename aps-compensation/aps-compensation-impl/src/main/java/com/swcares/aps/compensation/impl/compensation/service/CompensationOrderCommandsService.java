package com.swcares.aps.compensation.impl.compensation.service;

import com.swcares.aps.compensation.model.compensation.dto.CompensationCashAddCommandDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationCashEditCommandDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialAddCommandDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialEditCommandDTO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditOperationVO;

/**
 * @ClassName：CompensationOrderCommandsService
 * @Description：补偿单创建编辑service
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 16:20
 * @version： v1.0
 */
public interface CompensationOrderCommandsService {

    /**
     * @title addLuggageCompensation
     * @description 创建实物补偿单
     * <AUTHOR>
     * @date 2022/3/11 9:40
     * @param request
     * @return
     */
    String addMaterialCompensation(CompensationMaterialAddCommandDTO request);

    /**
     * @title editLuggageCompensation
     * @description 编辑实物补偿单
     * <AUTHOR>
     * @date 2022/3/11 9:41
     *  @param request
     * @return 
     */
    String editMaterialCompensation(CompensationMaterialEditCommandDTO request);

    /**
     * @title reSubmitMaterialCompensation
     * @description 重新提交实物补偿单
     * <AUTHOR>
     * @date 2022/3/11 9:41
     *  @param request
     * @return
     */
    CompensationAuditOperationVO reSubmitMaterialCompensation(CompensationMaterialEditCommandDTO request);

    /**
     * @title addCashCompensation
     * @description 添加现金补偿单
     * <AUTHOR>
     * @date 2022/3/11 9:50
     * @param request
     * @return
     */
    String addCashCompensation(CompensationCashAddCommandDTO request);

    /**
     * @title editCashCompensation
     * @description 编辑现金补偿单
     * <AUTHOR>
     * @date 2022/3/11 9:50
     * @param request
     * @return
     */
    String editCashCompensation(CompensationCashEditCommandDTO request);

    /**
     * @title reSubmitCashCompensation
     * @description 重新提交现金补偿单
     * <AUTHOR>
     * @date 2022/3/11 9:50
     * @param request
     * @return
     */
    CompensationAuditOperationVO reSubmitCashCompensation(CompensationCashEditCommandDTO request);
}
