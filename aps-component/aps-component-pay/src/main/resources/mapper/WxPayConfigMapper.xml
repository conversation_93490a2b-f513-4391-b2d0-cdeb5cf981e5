<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.component.pay.pay.service.wx.mapper.WxPayConfigMapper">
	<select id="selectOneConfig" resultType="com.swcares.aps.component.pay.pay.bean.wxpay.entity.WxPayConfigDO" databaseId="oracle">
		SELECT
			*
		FROM
			COMPENSATION_WXPAY_CONFIG
		WHERE
			mch_ID = #{mchID}  and tenant_Id = #{tenantId} and BUSINESS_TYPE = #{businessType}
	</select>
</mapper>
