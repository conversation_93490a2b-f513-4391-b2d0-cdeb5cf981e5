package com.swcares.aps.compensation.model.irregularflight.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * ClassName：com.swcares.irregularflight.vo.DpFlightAccidentInfoVO <br>
 * Description：详情 返回展示对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-14 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="FlightAccidentInfoVO对象", description="")
public class FlightAccidentInfoDetailsVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "事故单id")
    private String id;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "事故单状态（0草稿、1待处理、2处理中、3已结案、4作废）")
    private String accidentStatus;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "航段")
    private String segment;

    @ApiModelProperty(value = "航段-中文格式")
    private String choiceSegmentCh;

    @ApiModelProperty(value = "事故单类型- 1.不正常航班 2异常行李 3超售")
    private String accidentType;

    @ApiModelProperty(value = "航变类型（1延误，2取消，3备降，4返航，5补班）")
    private String fcType;

    @ApiModelProperty(value = "航变类型原因归属-字典枚举值fc_type_owner")
    private String fcTypeOwner;

    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    @ApiModelProperty(value = "预计起飞时间")
    private String etd;

    @ApiModelProperty(value = "实际起飞时间")
    private String atd;

    @ApiModelProperty(value = "延误区间（4小时<=8小时）")
    private String delayInterval;

    @ApiModelProperty(value = "航班延误原因")
    private String lateReason;

    @ApiModelProperty(value = "创建人ID")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "作废人ID")
    private String tovoidId;

    @ApiModelProperty(value = "作废时间")
    private LocalDateTime tovoidTime;

    @ApiModelProperty(value = "修改人ID")
    private String updatedBy;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updatedTime;

//=======================自定义======================
    @ApiModelProperty(value = "提交人姓名")
    private String createName;
    @ApiModelProperty(value = "作废人姓名")
    private String toVoidName;

    @ApiModelProperty(value = "归属航司")
    private String belongAirline;

    @ApiModelProperty(value = "事故来源")
    private String accidentSource;

    @ApiModelProperty(value = "事故单下关联赔偿单列表")
    List<AccidentCompensationOrderVO> compensationOrderVoList;
}
