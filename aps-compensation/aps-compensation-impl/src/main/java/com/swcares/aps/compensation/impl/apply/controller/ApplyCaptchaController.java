package com.swcares.aps.compensation.impl.apply.controller;

import com.swcares.aps.compensation.impl.apply.constants.ApplyErrors;
import com.swcares.aps.compensation.impl.apply.service.ApplyCaptchaService;
import com.swcares.aps.compensation.model.apply.dto.ApplyCaptchaDTO;
import com.swcares.aps.compensation.model.apply.dto.ApplyRecordCaptchaDTO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @ClassName：ApplyCaptchaController
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2025/2/27 13:11
 * @version： v1.0
 */
@RestController
@RequestMapping("/apply/captcha")
@Api(tags = "旅客申领验证码接口")
@Slf4j
@ApiVersion(value = "申领单接口 v1.0")
public class ApplyCaptchaController  extends BaseController {

    @Autowired
    private ApplyCaptchaService applyCaptchaService;


    @GetMapping("/getCaptchaConfig")
    @ApiOperation(value = "获取当前租户的短信验证码配置信息")
    public BaseResult<Object> getCaptchaConfigByTenant() {
        return applyCaptchaService.getCaptchaConfigByTenant();
    }


    @PostMapping("/getCaptcha")
    @ApiOperation(value = "申领-获取图形验证码")
    public BaseResult<Object> getCaptchaByApply() {
        return applyCaptchaService.getCaptchaByApply(new ApplyCaptchaDTO());
    }


    @PostMapping("/validateCaptcha")
    @ApiOperation(value = "申领-验证图形验证码")
    public BaseResult<Object> validateCaptchaByApply(@RequestBody @Valid ApplyCaptchaDTO dto) {
        if(StringUtils.isEmpty(dto.getCaptchaVerification()) || StringUtils.isEmpty(dto.getToken())){
            throw new BusinessException(ApplyErrors.APPLY_PARAM_ERROR);
        }
        return applyCaptchaService.validateCaptchaByApply(dto);
    }

}
