package com.swcares.aps.compensation.model.baggage.accident.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @title: FindBaggageDO
 * @projectName aps
 * @description: TODO
 * @date 2022/3/4 9:51
 */
@Data
@SecretInfoEntity
@TableName("baggage_accident_info")
@ApiModel(value = "箱包事故单DO对象",description = "箱包事故单DO对象")
public class FindBaggageDO extends BaseEntity {
    @ApiModelProperty(value = "主键id")
    private Long id;

    private Long tenantId;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "事故单状态")
    private String accidentStatus;

    @ApiModelProperty(value = "事故原因")
    private String accidentReason;

    @ApiModelProperty(value = "异常行李类型")
    private String type;

    @ApiModelProperty(value = "服务航站")
    private String serveSegment;

    @ApiModelProperty(value = "旅客航班号")
    private String paxFlightNo;

    @ApiModelProperty(value = "旅客航班日期")
    private String paxFlightDate;

    @ApiModelProperty(value = "旅客航班航段")
    private String paxSegment;

    @ApiModelProperty(value = "到达航站")
    private String poa;

    @ApiModelProperty(value = "起飞航站")
    private String pod;

    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    @ApiModelProperty(value = "预计起飞时间")
    private String etd;

    @ApiModelProperty(value = "行李号")
    private String baggageNo;

    @ApiModelProperty(value = "行李航班号")
    private String baggageFlightNo;

    @ApiModelProperty(value = "行李航班日期")
    private String baggageFlightDate;

    @ApiModelProperty(value = "行李航线")
    private String baggageSegment;

    @ApiModelProperty(value = "经停航站")
    private String stopoverSegment;

    @ApiModelProperty(value = "行李规格")
    private String baggageType;

    @ApiModelProperty(value = "逾重行李号")
    private String overweightNo;

    @ApiModelProperty(value = "破损类型")
    private String damageType;

    @ApiModelProperty(value = "破损程度")
    private String damageDegree;

    @ApiModelProperty(value = "破损部位")
    private String damagePart;

    @ApiModelProperty(value = "行李品牌")
    private String baggageBrand;

    @ApiModelProperty(value = "丢失重量")
    private Integer lostWeight;

    @ApiModelProperty(value = "少收类型")
    private Integer lostType;

    @ApiModelProperty(value = "事故提醒时间设置-自然日")
    private Integer reminderTime;

    @ApiModelProperty(value = "丢失数量（件）")
    private Integer lostAmount;

    @ApiModelProperty(value = "丢失申报类型（1全部申报,2部分申报,3未申报）")
    private String declarationType;

    @ApiModelProperty(value = "旅客相关图片")
    private String paxImg;

    @ApiModelProperty(value = "单据相关图片")
    private String voucherImg;

    @ApiModelProperty(value = "行李相关图片")
    private String baggageImg;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "旅客证件类型")
    private String idType;

    @ApiModelProperty(value = "旅客证件号")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "旅客票号")
    private String tktNo;

    @ApiModelProperty(value = "旅客手机号")
    @SecretValue
    private String phone;

    @ApiModelProperty(value = "创建人ID")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "作废人")
    private String tovoidBy;

    @ApiModelProperty(value = "作废时间")
    private String tovoidTime;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updatedTime;


}
