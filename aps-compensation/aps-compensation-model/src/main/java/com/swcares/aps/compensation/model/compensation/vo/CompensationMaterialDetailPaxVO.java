package com.swcares.aps.compensation.model.compensation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CompensationMaterialDetailPaxVO
 * @Description：箱包补偿单详情下补偿旅客的信息VO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 唐康
 * @Date： 2022/4/14 14:55
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationMaterialDetailPaxVO对象", description="箱包补偿单详情下补偿旅客的信息VO")
public class CompensationMaterialDetailPaxVO {
    @ApiModelProperty(value = "姓名")
    private String paxName;

    @ApiModelProperty(value = "证件类型")
    private String idType;

    @ApiModelProperty(value = "证件号码")
    private String idNo;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "行李号")
    private String baggageNo;

    @ApiModelProperty(value = "旅客ID")
    private String paxId;

    @ApiModelProperty(value = "领取状态(0未领取,1已领取,2处理中3已逾期)")
    private String receiveStatus;
}
