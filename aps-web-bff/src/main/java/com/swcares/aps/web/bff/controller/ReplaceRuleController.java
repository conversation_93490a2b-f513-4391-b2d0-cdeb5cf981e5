package com.swcares.aps.web.bff.controller;

import com.swcares.aps.compensation.model.replace.dto.ReplaceBaseRuleDTO;
import com.swcares.aps.compensation.model.replace.dto.ReplacePayPeriodRuleDTO;
import com.swcares.aps.compensation.model.replace.vo.ReplaceBaseRuleVO;
import com.swcares.aps.compensation.model.replace.vo.ReplacePayPeriodRuleVO;
import com.swcares.aps.web.bff.service.ReplaceRuleService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.security.UserContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * ClassName：com.swcares.aps.compensation.impl.replace.controller.ReplaceRuleController <br>
 * Description： 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-10 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/compensation/replace/rule")
@Api(tags = "代领人领取规则接口")
@ApiVersion(value = "代领设置 v1.0")
public class ReplaceRuleController extends BaseController {

    @Autowired
    private ReplaceRuleService replaceRuleService;

    @PostMapping("/saveBaseRule")
    @ApiOperation(value = "新建或者修改代领人领取基本规则接口")
    public BaseResult<Object> saveBaseRule(@RequestBody ReplaceBaseRuleDTO dto) {
        Long userId = UserContext.getUserId();
        dto.setCreatedBy(String.valueOf(userId));

        return replaceRuleService.saveBaseRule(dto);
    }

    @GetMapping("/getBaseRule")
    @ApiOperation(value = "获取当前配置的代领人领取基本规则")
    public BaseResult<ReplaceBaseRuleVO> getBaseRule() {
        return replaceRuleService.getBaseRule();
    }

    @PostMapping("/savePayWaitPeriod")
    @ApiOperation(value = "新建或者修改代领人领取支付等待期规则接口")
    public BaseResult<Object> savePayWaitPeriod(@RequestBody ReplacePayPeriodRuleDTO dto) {
        Long userId = UserContext.getUserId();
        dto.setCreatedBy(String.valueOf(userId));

        return replaceRuleService.savePayWaitPeriod(dto);
    }

    @GetMapping("/getPayWaitPeriod")
    @ApiOperation(value = "获取当前配置的代领人领取支付等待期规则")
    public BaseResult<ReplacePayPeriodRuleVO> getPayWaitPeriod() {
        return replaceRuleService.getPayWaitPeriod();
    }

}
