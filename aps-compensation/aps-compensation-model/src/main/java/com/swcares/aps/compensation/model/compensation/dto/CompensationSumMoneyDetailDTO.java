package com.swcares.aps.compensation.model.compensation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName：CompensationSumMoneyDetailDTO
 * @Description：现金补偿单详情下的补偿总金额详情
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 唐康
 * @Date： 2022/4/21 15:36
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationSumMoneyDetailDTO", description="现金补偿单详情下补偿总金额信息")
public class CompensationSumMoneyDetailDTO {
    @ApiModelProperty(value = "总计补偿数")
    private Integer allCompensate;

    @ApiModelProperty(value = "成人数")
    private Integer adultNum;

    @ApiModelProperty(value = "儿童数")
    private Integer childNum;

    @ApiModelProperty(value = "婴儿数")
    private Integer babyNum;

    @ApiModelProperty(value = "总计补偿金额")
    private BigDecimal sumMoney;
}
