package com.swcares.aps.compensation.model.dataconfig.dto;

import com.swcares.aps.compensation.model.dataconfig.vo.ReplaceBaseRuleVO;
import com.swcares.aps.compensation.model.dataconfig.vo.ReplacePayPeriodRuleVO;
import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value="RuleManageDTO", description="代领规则统一返回")
public class RuleManageDTO implements BaseDTO {

    private ReplacePayPeriodRuleVO replacePayPeriodRuleVO;

    private ReplaceBaseRuleVO replaceBaseRuleVO;

    private List<ReplaceRejectReasonDTO> reasonDTOList;
}
