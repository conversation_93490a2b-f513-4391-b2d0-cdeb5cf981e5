package com.swcares.aps.compensation.model.overbook.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * @ClassName：OverBookAccidentInfoDO
 * @Description：超售事故单信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/30 10:16
 * @version： v1.0
 */
@SecretInfoEntity
@Data
@TableName("over_book_accident_info")
@ApiModel(value="超售事故单信息DO", description="超售事故单信息DO")
public class OverBookAccidentInfoDO{

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    private Long tenantId;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "事故单状态（0草稿,1待处理,2处理中,3已结案,4作废）")
    private String accidentStatus;

    @ApiModelProperty(value = "事故原因")
    private String accidentReason;

    @ApiModelProperty(value = "事故来源 1机场/2航司")
    private String accidentSource;

    @ApiModelProperty(value = "事故类型：1改签，2退票")
    private String type;

    @ApiModelProperty(value = "原-航班id")
    private String flightId;

    @ApiModelProperty(value = "原-航班号")
    private String flightNo;

    @ApiModelProperty(value = "原-航班日期")
    private String flightDate;

    @ApiModelProperty(value = "原-计划起飞时间")
    private String std;

    @ApiModelProperty(value = "原航班航段（三字码）")
    private String segment;

    @ApiModelProperty(value = "原航班航段（三字码+中文）")
    private String segmentCh;

    @ApiModelProperty(value = "出发航站")
    private String orgCityAirp;

    @ApiModelProperty(value = "到达航站")
    private String dstCityAirp;

    @ApiModelProperty(value = "舱位全价 1000.00")
    private String fullEconomyFare;

    @ApiModelProperty(value = "改签-航班号")
    private String overBookFlightNo;

    @ApiModelProperty(value = "改签-航班日期")
    private String overBookFlightDate;

    @ApiModelProperty(value = "改签计划起飞时间")
    private String overBookStd;

    @ApiModelProperty(value = "作废人ID")
    private String toVoidBy;

    @ApiModelProperty(value = "作废时间")
    private LocalDateTime toVoidTime;

    @ApiModelProperty(value = "附件图片")
    private String imgUrl;



    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "旅客id")
    private String paxId;

    @ApiModelProperty(value = "旅客证件类型")
    private String idType;

    @ApiModelProperty(value = "旅客证件号")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "旅客手机号")
    @SecretValue
    private String telephone;

    @ApiModelProperty(value = "性别 M(男) F(女)C(儿童)")
    private String sex;

    @ApiModelProperty(value = "旅客状态")
    private String paxStatus;

    @ApiModelProperty(value = "是否取消0否1是")
    private String isCancel;

    @ApiModelProperty(value = "主仓位")
    private String mainClass;

    @ApiModelProperty(value = "子仓位")
    private String subClass;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "是否携带婴儿0否1是")
    private String withBaby;

    @ApiModelProperty(value = "携带婴儿此字段必填")
    private String babyPaxName;

    @ApiModelProperty(value = "购票时间")
    private LocalDateTime tktIssueDate;

    @ApiModelProperty(value = "儿童旅客标识0否1是")
    private String isChild;

    @ApiModelProperty(value = "PNR票号")
    private String pnr;

    @ApiModelProperty(value = "取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date cancelTime;

    @ApiModelProperty(value = "归属航司")
    private String belongAirline;

    @ApiModelProperty(value = "归属航司-中文简称")
    private String belongAirlineAbbr;

    @ApiModelProperty(value = "创建者")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新者")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;
}
