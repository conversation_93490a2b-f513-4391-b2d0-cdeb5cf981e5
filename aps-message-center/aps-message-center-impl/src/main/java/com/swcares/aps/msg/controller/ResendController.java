package com.swcares.aps.msg.controller;

import com.swcares.aps.msg.model.dto.ReSendByPnrOrTelPhoneDTO;
import com.swcares.aps.msg.service.SendSmsService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.components.quartz.exception.TaskException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import java.text.ParseException;
import java.util.List;

import static com.swcares.baseframe.common.base.BaseResult.back;
import static com.swcares.baseframe.common.base.BaseResult.ok;

/**
 * <AUTHOR> <br>
 * @version v1.0 <br>
 * @ClassName：com.swcares.reaptv.msg.controller.ResendController <br>
 * @Description：重新发送短信接口 <br>
 * @Copyright 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * @Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * @Date 2022/11/29 9:30 <br>
 */
@RestController
@RequestMapping({"/msg/sms/resend"})
@Api(tags = {"重新发送短信接口"})
@ApiVersion({"Message Center Api v1.0"})
public class ResendController {

    @Autowired
    SendSmsService sendSmsService;

    @PostMapping("/reSendByPnrOrTelPhone")
    @ApiOperation("通过pnr或者手机号或者票号给旅客发送短信")
    public BaseResult<Object> reSendByPnrOrTelPhone(@RequestBody @Valid List<ReSendByPnrOrTelPhoneDTO> dto) throws SchedulerException, TaskException, ParseException {
        sendSmsService.resendFailedSms(dto);
        return back(BaseResult.OK_CODE,"");
    }

}
