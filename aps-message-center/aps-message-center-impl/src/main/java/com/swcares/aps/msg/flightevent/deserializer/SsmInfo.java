/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：SsmInfo <br>
 * Package：com.swcares.reaptv.msg.flighteventanalyzer <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.flightevent.deserializer;

import lombok.Data;

/**
 * ClassName：com.swcares.reaptv.msg.flightevent.deserializer.SsmInfo <br>
 * Description：广分发送的航班变更事件中的航变信息
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/3 20:57 <br>
 * @version:
 */
@Data
public class SsmInfo {
    //数据在广分侧的id
    private String ssmId;

    //报文的预报编号
    private String forecastNo;

    //报文导入时间，视为调整时间
    private String importTime;
    
    //调整类型
    private String adjustType;

    //处理类型
    private String processType;

    //调整原因
    private String adjustReason;

    //备注
    private String remark;

    //预报类型
    private String forecastType;

    //航班号
    private String flightNumber;

    //航段
    private String segment;

    //开始时间，用于调整一段时间内的所有同航班号的航班
    private String startDate;

    //结束实际,用于调整一段时间内的所有同航班号的航班
    private String endDate;

    //调整后预计起飞时间
    private String depTime;

    //调整后的预计到达时间
    private String desTime;

    //调整前的预计起飞时间
    private String origdepTime;

    //调整前的预计到达时间
    private String origdesTime;

    //班期
    private String frequency;

    //是否联程航班
    private String isBound;

    //调整前的飞机机型
    private String planeType;

    //调整后的飞机机型
    private String newplaneType;

    //调整前的布局
    private String layout;

    //调整后的布局
    private String newLayout;
    
    //是否是后段取消。长航段后段取消的时候该值设置为Y
    private String cancelLastSeg;
    
    //长航段的经停点，在长航段后段取消的时候有值。
    private String transitPoint;
    
}
