/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：FlightEventDeserializer <br>
 * Package：com.swcares.reaptv.msg.flighteventanalyzer <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.flightevent.deserializer;

import com.alibaba.fastjson.JSON;
import com.google.gson.JsonSyntaxException;
import com.swcares.aps.msg.config.MessageCenterErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.lang.StringUtils;

/**
 * ClassName：com.swcares.reaptv.msg.flightevent.deserializer.FlightEventDeserializer <br>
 * Description：TODO：(这里用一句话描述这个类的作用) <br>
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/3 21:57 <br>
 * @version:
 */
public class FlightEventDeserializer{
    public static FlightEventMessageWrapper deserialize(String message){
        checkMessage(message);
        
        try {
            return JSON.parseObject(message, FlightEventMessageWrapper.class);
        } catch (JsonSyntaxException e) {
            throw new BusinessException(MessageCenterErrors.INVALID_FLIGHT_EVENT_MESSAGE, e.getMessage());
        }
        
    }

    private static void checkMessage(String message) {
        if (StringUtils.isBlank(message)){
            throw new BusinessException(MessageCenterErrors.INVALID_FLIGHT_EVENT_MESSAGE, "报文为空");
        }
        
    }
}
