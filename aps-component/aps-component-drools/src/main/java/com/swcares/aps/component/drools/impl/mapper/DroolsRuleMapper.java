package com.swcares.aps.component.drools.impl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.component.drools.model.entity.DroolsRuleDO;
import com.swcares.aps.component.drools.model.vo.DroolsRuleVO;
import com.swcares.aps.component.drools.model.dto.DroolsRulePageDTO;
import org.apache.ibatis.annotations.Mapper;

/**
 * ClassName：com.swcares.component.drools.impl.mapper.DroolsRuleMapper <br>
 * Description：规则引擎查询数据库mapper <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021/10/19 16:17 <br>
 * @version v1.0 <br>
 */
@Mapper
public interface DroolsRuleMapper  extends BaseMapper<DroolsRuleDO> {

    /**
     * Title：page <br>
     * Description：规则分页查询 <br>
     * author：王磊 <br>
     * date：2021/10/20 11:22 <br>
     * @param dto
     * @param page <br>
     * @return <br>
     */
    IPage<DroolsRuleVO> page(DroolsRulePageDTO dto, Page<Object> page);

    /**
     * Title：logicRemoveById <br>
     * Description：逻辑删除规则 <br>
     * author：王磊 <br>
     * date：2021/10/21 15:53 <br>
     * @param id <br>
     * @return <br>
     */
    Integer logicRemoveById(Long id);
}
