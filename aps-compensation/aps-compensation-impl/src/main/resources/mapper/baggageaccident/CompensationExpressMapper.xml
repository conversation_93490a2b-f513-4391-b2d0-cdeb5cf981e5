<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.swcares.aps.compensation.impl.baggage.accident.mapper.CompensationExpressMapper">
    <select id="selectExpressListByAccidentId" resultType="com.swcares.aps.compensation.model.baggage.accident.vo.BaggageExpressVO" databaseId="oracle">
        SELECT
        cei.ADDRESSEE ,
        cei.ADDRESS ,
        cei.PHONE ,
        cei.EXPRESS_TYPE ,
        cei.EXPRESS_NO ,
        cei.EXPRESS_COMPANY ,
        cei.EXPRESS_GOODS ,
        cei.ORDER_NO,
        (SELECT CONCAT(ue.NAME,ue.JOB_NUMBER) FROM UC_USER uu
        left join UC_EMPLOYEE ue ON uu.EMPLOYEE_ID = ue.ID
         WHERE TO_CHAR(uu.ID) = cei.CREATED_BY )  created_by,
        cei.CREATED_TIME
        FROM COMPENSATION_EXPRESS_INFO cei
        WHERE cei.ACCIDENT_ID  = #{accidentId}
    </select>

    <select id="selectExpressListByAccidentId" resultType="com.swcares.aps.compensation.model.baggage.accident.vo.BaggageExpressVO" databaseId="mysql">
        SELECT
            cei.ADDRESSEE ,
            cei.ADDRESS ,
            cei.PHONE ,
            cei.EXPRESS_TYPE ,
            cei.EXPRESS_NO ,
            cei.EXPRESS_COMPANY ,
            cei.EXPRESS_GOODS ,
            cei.ORDER_NO,
            (SELECT CONCAT(ue.NAME,ue.JOB_NUMBER) FROM UC_USER uu
             left join UC_EMPLOYEE ue ON uu.EMPLOYEE_ID = ue.ID
             WHERE uu.ID = cei.CREATED_BY )  created_by,
            cei.CREATED_TIME
        FROM COMPENSATION_EXPRESS_INFO cei
        WHERE cei.ACCIDENT_ID  = #{accidentId}
    </select>
</mapper>