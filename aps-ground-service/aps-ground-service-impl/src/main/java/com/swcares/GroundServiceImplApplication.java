package com.swcares;

import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * @ClassName：GroundServiceImplApplication
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/15 14:20
 * @version： v1.0
 */
@SpringBootApplication(exclude = RabbitAutoConfiguration.class, scanBasePackages = {GroundServiceImplApplication.DEFAULT_PACKAGE})
@EnableCreateCacheAnnotation
@MapperScan(GroundServiceImplApplication.DEFAULT_PACKAGE+".**.mapper")
@EnableDiscoveryClient
@EnableFeignClients
@EnableScheduling
@EnableAspectJAutoProxy(exposeProxy = true)
public class GroundServiceImplApplication {
    public static final String DEFAULT_PACKAGE = "com.swcares";
    public static void main(String[] args) {
        SpringApplication.run(GroundServiceImplApplication.class, args);
        SecurityContextHolder.setStrategyName(SecurityContextHolder.MODE_INHERITABLETHREADLOCAL);
    }
}
