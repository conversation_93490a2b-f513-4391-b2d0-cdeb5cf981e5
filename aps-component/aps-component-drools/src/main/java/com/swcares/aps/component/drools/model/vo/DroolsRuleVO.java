package com.swcares.aps.component.drools.model.vo;

import com.swcares.aps.component.drools.model.entity.DroolsRuleDO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.component.drools.model.vo.DroolsRuleVO <br>
 * Description：规则引擎管理VO对象 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021/10/20 10:34 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="规则引擎管理VO对象", description="规则引擎管理VO对象")
public class DroolsRuleVO extends DroolsRuleDO {

    private static final long serialVersionUID = 1L;
}
