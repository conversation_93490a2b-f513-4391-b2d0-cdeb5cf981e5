package com.swcares.aps.usercenter.remote.api.passengerCategory;

import com.swcares.aps.usercenter.model.passengerCategory.dto.PassengerCategoryChangeDto;
import com.swcares.aps.usercenter.model.passengerCategory.dto.PassengerCategorySentPageDto;
import com.swcares.aps.usercenter.model.passengerCategory.dto.PassengerCategoryStateDto;
import com.swcares.aps.usercenter.model.passengerCategory.entity.PassengerCategoryConfigureDepository;
import com.swcares.aps.usercenter.model.passengerCategory.vo.PassengerCategorySentPageVo;
import com.swcares.aps.usercenter.model.passengerCategory.vo.PassengerCategoryVo;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * ClassName：com.swcares.reaptv.usercenter.impl.com.swcares.reaptv.usercenter.api.passengerCategory.PassengerCategoryApi <br>;
 * Description：旅客类别配置api <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/5/25 10:08 <br>;
 * @version v1.0 <br>;
 */
@FeignClient(name = "aps-user-center", path = "/api/aps-user-center", contextId = "passengerCategoryApi")
public interface PassengerCategoryApi {

    @RequestMapping(value = "/bd/passCategory/send_pass_category_page", method = RequestMethod.GET)
    @ApiOperation("旅客类别配置分页列表查询")
    PagedResult<List<PassengerCategorySentPageVo>> getPassengerCategoryConfigurepage(@SpringQueryMap PassengerCategorySentPageDto dto);

    @RequestMapping(value = {"/bd/passCategory/save_pass_category"}, method = RequestMethod.POST)
    @ApiOperation("新增旅客类别配置")
    BaseResult<String> savePassengerCategoryConfigure(@RequestBody PassengerCategoryChangeDto dto);

    @RequestMapping(value = "/bd/passCategory/update_pass_category", method = RequestMethod.POST)
    @ApiOperation("更新旅客类别配置")
    BaseResult<String> updatePassengerCategoryConfigure(@RequestBody PassengerCategoryChangeDto dto);

    @RequestMapping(value = "/bd/passCategory/update_pass_category_state", method = RequestMethod.POST)
    @ApiOperation("批量更新旅客类别配置状态")
    BaseResult<String> updatePassengerCategoryConfigureState(@RequestBody PassengerCategoryStateDto dto);

    @PostMapping("/bd/passCategory/passengerCategory")
    @ApiOperation(value = "旅客类别下拉框")
    BaseResult<List<PassengerCategoryVo>> passengerCategory(@RequestBody PassengerCategoryChangeDto dto);

    @PostMapping({"/bd/passCategory/list_pass_category"})
    @ApiOperation("旅客类别配置列表查询")
    BaseResult<List<PassengerCategoryConfigureDepository>> listPassengerCategoryConfigure(@RequestBody List<String> codes);
}
