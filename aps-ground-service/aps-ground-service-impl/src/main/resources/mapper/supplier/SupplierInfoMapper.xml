<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.ground.supplier.mapper.SupplierInfoMapper">
    <update id="logicRemoveById">
        update GROUND_SUPPLIER_INFO set deleted = 1 where id =#{id}
    </update>

    <select id="pageQuerySupplierList" resultType="com.swcares.aps.ground.models.supplier.vo.SupplierListVO">
        select DISTINCT GSI."ID",
        gsi.SUPPLIER_NAME,GSI.SUPPLIER_CODE,GSI.SUPPLIER_TYPE,GSI.CONTRACT_AGREEMENT_BEGIN,
        GSI.CONTRACT_AGREEMENT_END,GSI.SUPPLIER_STATE,GSI.CAN_USE,GSI.BELONG_TENANT_TYPE,GSI.BELONG_TENANT_CODE,
        (select count(1) from GROUND_SUPPLIER_SERVICE where SUP<PERSON>IER_ID = GSI."ID" and DELETED = 0) supplierServiceNum
        from GROUND_SUPPLIER_INFO gsi
        LEFT JOIN GROUND_SUPPLIER_SERVICE gss on gss.SUPPLIER_ID = GSI."ID" and gss.DELETED = 0
        LEFT JOIN GROUND_SUPPLIER_SERVICE_APT gsa on gss.id = gsa.SUPPLIER_SERVICE_ID
        where gsi.DELETED = 0
        <if test="dto.supplierName != null and dto.supplierName != ''">
            and gsi.SUPPLIER_NAME  like concat(concat('%',#{dto.supplierName}),'%')
        </if>

        <if test="dto.supplierCode != null and dto.supplierCode != ''">
            and GSI.SUPPLIER_CODE = #{dto.supplierCode}
        </if>
        <if test="dto.supplierType != null and dto.supplierType != ''">
            and GSI.SUPPLIER_TYPE = #{dto.supplierType}
        </if>
        <if test="dto.supplierState != null">
            and GSI.SUPPLIER_STATE = #{dto.supplierState}
        </if>
        <if test="dto.airlineCode != null and dto.airlineCode != ''">
            and gss.BELONG_AIRLINE_CODE = #{dto.airlineCode}
        </if>
        <if test="dto.airportCode != null and dto.airportCode != ''">
            and GSA.AIRPORT_CODE = #{dto.airportCode}
        </if>

        ORDER BY GSI.SUPPLIER_STATE desc ,GSI.CONTRACT_AGREEMENT_END asc
    </select>


    <select id="refreshCanUseList" resultType="com.swcares.aps.ground.models.supplier.entity.SupplierInfoDO">
        select *

        FROM GROUND_SUPPLIER_INFO
        WHERE
         DELETED = 0
        and BELONG_TENANT_TYPE = #{belongTenantType}
        and
        (SUPPLIER_STATE = 1 and TRUNC(SYSDATE) BETWEEN TRUNC(CONTRACT_AGREEMENT_BEGIN) AND TRUNC(CONTRACT_AGREEMENT_END) and CAN_USE =0)
        OR
        (
        (SUPPLIER_STATE = 0 or TRUNC(SYSDATE) &lt; TRUNC(CONTRACT_AGREEMENT_BEGIN) OR SYSDATE > TRUNC(CONTRACT_AGREEMENT_END)) and  CAN_USE =1
        )
    </select>
</mapper>