package com.swcares.aps.staff.bff.vo;

import com.swcares.aps.compensation.model.dataconfig.vo.CompensationConfigVO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName：CompensationDataConfigVO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/23 16:06
 * @version： v1.0
 */
public class CompensationDataConfigVO extends HashMap{

    public static Map<String,CompensationDataConfigVO> of(List<CompensationConfigVO> compensationConfigVOS) {
        Map<String, List<CompensationConfigVO>> collect = compensationConfigVOS.stream().collect(Collectors.groupingBy(CompensationConfigVO::getType));

        Map<String,CompensationDataConfigVO> result=new HashMap<>();
        Set<String> keys = collect.keySet();

        for(String key:keys){
            CompensationDataConfigVO configVO=new CompensationDataConfigVO();

            List<CompensationConfigVO> vos = collect.get(key);
            Map<String, List<CompensationConfigVO>> subTypeMaps = vos.stream().collect(Collectors.groupingBy(CompensationConfigVO::getSubType));

            Set<String> subTypeKeys = subTypeMaps.keySet();
            for(String subTypeKey:subTypeKeys){
                List<String> value = subTypeMaps.get(subTypeKey).stream().map(CompensationConfigVO::getValue).collect(Collectors.toList());
                configVO.put(subTypeKey,value);
            }

            result.put(key,configVO);
        }
        return result;
    }
}
