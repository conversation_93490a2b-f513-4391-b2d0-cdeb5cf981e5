/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：EmployeeSmsRecordService <br>
 * Package：com.swcares.reaptv.msg.service <br> 
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.msg.model.dto.EmployeeSmsSearchDTO;
import com.swcares.aps.msg.model.dto.EmployeeSmsSendDTO;
import com.swcares.aps.msg.model.dto.MessageStatusStaticsDTO;
import com.swcares.aps.msg.model.entity.EmployeeSmsRecord;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * InterfaceName：com.swcares.reaptv.msg.service.EmployeeSmsRecordService <br>
 * Description：对员工发送的短消息的发送记录的服务接口
 * <AUTHOR> <br>
 * date 2022/9/19 6:27 <br>
 * @version v1.0.0 <br>
 */
public interface EmployeeSmsRecordService extends IService<EmployeeSmsRecord> {
    IPage<EmployeeSmsRecord> getEmployeeSmsRecordPage(EmployeeSmsSearchDTO dto);

    List<EmployeeSmsRecord> getByIds(List<Long> ids);

    void download(EmployeeSmsSearchDTO dto, HttpServletRequest request, HttpServletResponse response);

    void downLoad(List<Long> ids, HttpServletRequest request, HttpServletResponse response);

    EmployeeSmsRecord sendSms(EmployeeSmsSendDTO dto);

    List<EmployeeSmsRecord> sendSms(List<EmployeeSmsSendDTO> dtos);

    boolean batchDelete(List<Long> id);

    List<MessageStatusStaticsDTO> getMessagesStatusStatics(EmployeeSmsSearchDTO dto);

}
