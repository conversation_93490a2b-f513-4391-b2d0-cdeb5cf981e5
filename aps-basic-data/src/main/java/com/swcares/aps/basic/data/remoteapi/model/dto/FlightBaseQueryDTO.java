package com.swcares.aps.basic.data.remoteapi.model.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：FlightBaseQueryDto
 * @Description：航班信息查询参数
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/14 11:50
 * @version： v1.0
 */
@Data
@ApiModel(value="FlightBaseQueryDto", description="航班信息查询信息")
public class FlightBaseQueryDTO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "航班ID")
    private String flightId;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "起始三字码")
    private String org;

    @ApiModelProperty(value = "到达三字码")
    private String dst;
}
