package com.swcares.aps.msg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.msg.model.dto.SentSmsStatisticsSearchDTO;
import com.swcares.aps.msg.model.dto.SmsPersonTimeStatisticsSearchDTO;
import com.swcares.aps.msg.model.vo.SentSmsStatisticsResultVO;
import com.swcares.aps.msg.model.vo.SmsPersonTimeStatisticsResultVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @ClassName：SentSmsReportService
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/11/25 10:36
 * @version： v1.0
 */
public interface SentSmsReportService{


    public SentSmsStatisticsResultVO getSentSmsStatistics(SentSmsStatisticsSearchDTO searchDTO);

    public void downLoad(SentSmsStatisticsSearchDTO dto, HttpServletRequest request, HttpServletResponse response);

    public IPage<SmsPersonTimeStatisticsResultVO> getSmsPersonTimeStatistics(SmsPersonTimeStatisticsSearchDTO searchDTO);

    public void downLoad(SmsPersonTimeStatisticsSearchDTO dto, HttpServletRequest request, HttpServletResponse response);

}