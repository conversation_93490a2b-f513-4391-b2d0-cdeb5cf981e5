package com.swcares.aps.compensation.model.irregularflight.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.aps.compensation.model.irregularflight.dto <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 01月25日 11:26 <br>
 * @version v1.0 <br>
 */

@Data
@ApiModel(value = "PaxOrderInfoQueryDTO对象", description = "旅客补偿次数查询对象")
public class PaxOrderInfoQueryDTO {


    @ApiModelProperty(value = "旅客姓名")
    private String paxName;
    @ApiModelProperty(value = "证件号")
    private String idNo;
    @ApiModelProperty(value = "旅客paxId")
    private String paxId;
    @ApiModelProperty(value = "航班号")
    private String flightNo;
    @ApiModelProperty(value = "航班日期")
    private String flightDate;
    @ApiModelProperty(value = "补偿单类型")
    private String compensateType;

}
