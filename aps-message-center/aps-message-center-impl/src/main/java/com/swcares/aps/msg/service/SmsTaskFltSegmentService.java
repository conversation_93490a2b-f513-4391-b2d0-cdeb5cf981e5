package com.swcares.aps.msg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.msg.model.dto.TaskSengmentByEventDTO;
import com.swcares.aps.msg.model.entity.SmsTaskFltSegmentInfo;

import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/
 * ClassName：com.swcares.reaptv.msg.service.SmsTaskFltSegmentService
 * Description：(用一句话描述这个类或者接口表示什么)
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2022-12-2 10:04
 */
public interface SmsTaskFltSegmentService extends IService<SmsTaskFltSegmentInfo> {
    public List<SmsTaskFltSegmentInfo> getFltSegmentsOfTask(String taskId);

    public List<SmsTaskFltSegmentInfo> getFltSegmentsByEvent(TaskSengmentByEventDTO eventDto);
}

