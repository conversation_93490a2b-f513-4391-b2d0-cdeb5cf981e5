package com.swcares.aps.compensation.model.compensation.vo;

import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CompensationBaseInfoVO
 * @Description：补偿单最基本的信息VO，所有类型补偿单都有的信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/9 12:39
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationBaseInfoVO对象", description="补偿单最基本的信息VO")
public class CompensationBaseInfoVO extends CompensationOrderInfoDO {
    @ApiModelProperty(value = "是否可以进行审核，Y是 N否")
    private String toExamine;

    @ApiModelProperty(value = "审核节点id")
    private String taskId;

}
