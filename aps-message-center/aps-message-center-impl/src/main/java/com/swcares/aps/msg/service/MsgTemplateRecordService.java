package com.swcares.aps.msg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.msg.model.entity.MsgTemplateRecord;

/**
 * @ClassName：MsgTemplateRecordService
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/11/30 16:12
 * @version： v1.0
 */
public interface MsgTemplateRecordService  extends IService<MsgTemplateRecord> {

    /****
     * @title smsRecordGroupCount
     * @description 每天定时-拉取短信发送记录表数据，分组统计发送条数，发送次数
     * <AUTHOR>
     * @date 2022/11/30 16:22

     * @return void
     */
    public void smsRecordGroupCount();
}
