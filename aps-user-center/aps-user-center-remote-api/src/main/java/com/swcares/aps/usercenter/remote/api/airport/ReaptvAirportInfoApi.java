package com.swcares.aps.usercenter.remote.api.airport;

import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.components.bd.entity.AirportInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * ClassName：com.swcares.reaptv.usercenter.impl.com.swcares.reaptv.usercenter.api.airport.ReaptvAirportInfoController <br>;
 * Description： <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/11/4 12:14 <br>;
 * @version v1.0 <br>;
 */
@FeignClient(name = "aps-user-center", path = "/api/aps-user-center", contextId = "reaptvAirportInfoApi")
public interface ReaptvAirportInfoApi {

    @GetMapping("/reaptv/airport/get_all")
    @ApiOperation("获取全部航站信息")
    BaseResult<List<AirportInfo>> getReaptvAirportInfo();
}
