package com.swcares.aps.compensation.model.complaint.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "AccidentConcatCompensationInfoVo对象", description = "关联补偿单信息")
public class AccidentConcatCompensationInfoVo {

    @ApiModelProperty(value = "补偿方式")
    private String compensateType;

    @ApiModelProperty(value = "补偿单主健id")
    private String id;

    @ApiModelProperty(value = "补偿单号")
    private String orderNo;

    @ApiModelProperty(value = "补偿状态")
    private String status;

    @ApiModelProperty(value = "服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "计划执行人数")
    private String planCarryOutNum;

    @ApiModelProperty(value = "实际执行人数")
    private String actualCarryOutNum;

    @ApiModelProperty(value = "计划执行现金价值（元）")
    private String planCurrentAmount;

    @ApiModelProperty(value = "实际执行现金价值（元）")
    private String trueCurrentAmount;

    @ApiModelProperty(value = "创建人")
    private String createdUser;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;
}
