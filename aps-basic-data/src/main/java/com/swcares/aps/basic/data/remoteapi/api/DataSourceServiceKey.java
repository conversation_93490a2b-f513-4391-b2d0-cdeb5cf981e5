package com.swcares.aps.basic.data.remoteapi.api;

import com.swcares.aps.basic.data.remoteapi.enums.DataSourceServiceKeyEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface DataSourceServiceKey {
    DataSourceServiceKeyEnum value(); // 数据源key
}
