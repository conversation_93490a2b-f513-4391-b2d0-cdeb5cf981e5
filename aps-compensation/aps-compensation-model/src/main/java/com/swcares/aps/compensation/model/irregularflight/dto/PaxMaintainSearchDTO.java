package com.swcares.aps.compensation.model.irregularflight.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.aps.compensating.mode.irregularflight.dto.PaxMaintainSearchDto <br>
 * Description：旅客维护查询条件DTO <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021/10/28 15:24 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "PaxMaintainSearchDTO对象", description = "旅客维护查询条件DTO")
public class PaxMaintainSearchDTO {
    @ApiModelProperty(value = "航班号")
    private String flightNo;
    @ApiModelProperty(value = "航班日期")
    private String flightDate;
    @ApiModelProperty(value = "旅客姓名")
    private String paxName;
    @ApiModelProperty(value = "姓名证件号")
    private String idNo;
    @ApiModelProperty(value = "票号")
    private String tktNo;
    @ApiModelProperty(value = "行李号")
    private String bagTag;
    @ApiModelProperty(value = "所选航段")
    private String choiceSegment;
    @ApiModelProperty(value = "包含取消旅客1包含,0不包含")
    private String containsCancel;
    @ApiModelProperty(value = "包含N舱1包含,0不包含")
    private String containsN;
    @ApiModelProperty(value = "购票时间开始")
    private String tktEndDateStart;
    @ApiModelProperty(value = "包购票时间结束")
    private String tktEndDateEnd;
    @ApiModelProperty(value = "值机状态 全部，PT（出票），NA（未值机），AC（值机），XR（值机取消），CL（订座取消），SB（候补），DL（拉下）")
    private String checkStatus;

}
