/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：TimeChangeSmsTaskBuilder <br>
 * Package：com.swcares.reaptv.msg.flightevent.taskbuilder <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.flightevent.taskbuilder;


import com.swcares.aps.msg.model.dto.SemiAutoSmsTaskDTO;
import com.swcares.aps.msg.model.entity.*;
import com.swcares.aps.msg.model.enums.FlightEventEnum;
import com.swcares.aps.msg.service.FlightEventPassengerService;
import com.swcares.aps.msg.service.TempleteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * ClassName：com.swcares.reaptv.msg.flightevent.taskbuilder.TimeChangeSmsTaskBuilder <br>
 * Description：时间调整类型的短信任务构造器
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/19 10:44 <br>
 * @version:
 */
@Component
public class TimeChangeSmsTaskBuilder extends  AbstractSemiSmsTaskBuilder{
    private static final String SUPPORTED_PROCESS_TYPE = "TIM";
    private static final String SMS_TEMPLATE_DEFAULT = "TIME_ADJUST_SEMI_AUTO";//默认的时刻调整短信模板的id

    private static final String SMS_TEMPLATE_TIME_LASTLEG = "TIME_ADJUST_SEMI_LASTLEG"; //后段取消的短信模板的id
    
    
    @Autowired
    private FlightEventPassengerService flightEventPassengerService;
    
    @Autowired
    private TempleteService templeteService;
    
    
    @Override public boolean isSupport(FlightEvent flightEvent) {
        return SUPPORTED_PROCESS_TYPE.equalsIgnoreCase(flightEvent.getProcessType());
    }

    @Override
    public List<SemiAutoSmsTaskDTO> buildSemiAutoTask(FlightEvent flightEvent, List<FlightEventPassenger> passengers) {

        SemiAutoSmsTaskDTO semiAutoSmsTaskDTO = new SemiAutoSmsTaskDTO();
        Templete templete = templeteService.getById(getTemplate(flightEvent));
        SmsTaskInfo taskInfo = createSmsTaskInfo(flightEvent, templete.getId(), templete.getTempleteContent(), passengers);
        semiAutoSmsTaskDTO.setTaskInfo(taskInfo);
        
        List<SmsTaskDetailInfo> taskDetailInfos = createTaskDetailInfos(flightEvent, passengers);
        semiAutoSmsTaskDTO.setTaskDetailInfoList(taskDetailInfos);

        List<SemiAutoSmsTaskDTO> results = new ArrayList<>();
        results.add(semiAutoSmsTaskDTO);
        return results;
    }

    private String getTemplate(FlightEvent flightEvent){
        if (flightEvent.isCancelLastSeg()){
            return SMS_TEMPLATE_TIME_LASTLEG;
        }

        return SMS_TEMPLATE_DEFAULT;
    }

    
    @PostConstruct
    public void registerToFactory(){
        SemiAutoSmsTaskBuilderFactory.register(SUPPORTED_PROCESS_TYPE, this);
    }

    @Override
    FlightEventEnum getFlightEventEnum() {
        return FlightEventEnum.TIME_ADJUST;
    }
}
