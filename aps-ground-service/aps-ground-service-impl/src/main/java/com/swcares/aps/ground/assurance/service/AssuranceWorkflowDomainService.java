package com.swcares.aps.ground.assurance.service;

import com.swcares.aps.component.workflow.dto.AuditorInfoDTO;
import com.swcares.aps.ground.models.assurance.domain.AssuranceOrderDomain;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderReviewerSaveCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceWorkflowAuditCommand;
import com.swcares.aps.ground.models.assurance.vo.AssuranceWorkflowAuditHistoryVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceWorkflowAuditResultVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceWorkflowAuditorOrderVO;
import com.swcares.aps.workflow.dto.StartSyncWorkflowInfoDTO;

import java.util.List;

/**
 * @ClassName：AssuranceWorkflowDomainService
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/22 10:29
 * @version： v1.0
 */
public interface AssuranceWorkflowDomainService {

    /**
     * @title start
     * @description 发起流程
     * <AUTHOR>
     * @date 2024/9/25 14:03
     * @param assuranceOrderDomain
     * @param startSyncWorkflowInfoDTO 关联的流程
     * @return
     */
    AssuranceWorkflowAuditResultVO start(AssuranceOrderDomain assuranceOrderDomain, StartSyncWorkflowInfoDTO startSyncWorkflowInfoDTO);


    /**
     * @title submit
     * @description 提交
     * <AUTHOR>
     * @date 2024/9/25 14:03
     * @param assuranceOrderDomain
     * @return
     */
    AssuranceWorkflowAuditResultVO submit(AssuranceOrderDomain assuranceOrderDomain);

    /**
     * @title 审核操作（审核通过、审核驳回、审核拒绝）
     * @description @TODO
     * <AUTHOR>
     * @date 2024/8/23 9:54
     * @param command
     * @param orderDomain
     * @return
     */
    AssuranceWorkflowAuditResultVO process(AssuranceOrderDomain orderDomain,AssuranceWorkflowAuditCommand command);

    /**
     * @title
     * @description @TODO
     * <AUTHOR>
     * @date 2024/9/25 17:03
     * @param orderDomain
     * @param command
     * @return
     */
    List<AuditorInfoDTO> saveReviewer(AssuranceOrderDomain orderDomain, AssuranceOrderReviewerSaveCommand command);

    /**
     * @title getCurrentUserAuthOrders
     * @description 获取当前用户是否有保障单的审核权限
     * <AUTHOR>
     * @date 2024/9/26 10:10
     * @param orderIds
     * @param userId
     * @return
     */
    List<AssuranceWorkflowAuditorOrderVO> getOrderAuthUsers(List<String> orderIds, String userId);

    /**
     * @title findReviewer
     * @description 获取审核人信息
     * <AUTHOR>
     * @date 2024/9/26 10:59
     * @param taskId
     * @param orderId
     * @return
     */
    List<AuditorInfoDTO> findReviewer(String taskId, String orderId);

    /**
     * @title findAuditHistory
     * @description 查看审核记录
     * <AUTHOR>
     * @date 2024/9/27 14:45
     * @param orderId
     * @return
     */
    List<AssuranceWorkflowAuditHistoryVO> findAuditHistory(String orderId);


    /**
     * @title findAuditorList
     * @description 根据用户ID获取用户基础信息
     * <AUTHOR>
     * @date 2024/10/31 11:11
     * @param userIds
     * @return
     */
    List<AuditorInfoDTO> findAuditorList(List<String> userIds);
}
