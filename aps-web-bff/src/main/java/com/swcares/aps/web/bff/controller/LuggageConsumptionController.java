package com.swcares.aps.web.bff.controller;

import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageConsumptionDetailPageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageConsumptionDetailVO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.PagedResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @title: LuggageConsumptionController
 * @projectName aps
 * @description: 箱包消耗明细管理bff接口
 * @date 2022/2/8 15:01
 */
@RestController
@RequestMapping("/luggage/consumption")
@Api(tags = "箱包消耗明细管理")
@ApiVersion(value = "箱包管理v1.0")
public class LuggageConsumptionController {

    @Autowired
    private CompensationInfoApi compensationInfoApi;

    //谭睿
    @PostMapping("/page")
    @ApiOperation(value = "查询箱包消耗列表")
    public PagedResult<List<LuggageConsumptionDetailVO>> findLuggageConsumptions(@RequestBody LuggageConsumptionDetailPageDTO dto){
        return compensationInfoApi.findLuggageConsumptions(dto);
    }
}
