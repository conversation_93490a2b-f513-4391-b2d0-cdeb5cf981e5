package com.swcares.aps.compensation.model.baggage.accident.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName： ExpressVO
 * Description： @DOTO
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * Date 2022/3/23 15:25
 * @version v1.0
 */
@Data
@ApiModel(value="异常行李事故单详情信息下的行李信息VO", description="异常行李事故单详情信息下的快递信息VO")
public class BaggageDetailBaggageVO {
    //------------------行李信息-----------------------
    @ApiModelProperty(value = "行李号")
    private String baggageNo;

    @ApiModelProperty(value = "行李航班日期")
    private String baggageFlightDate;

    @ApiModelProperty(value = "行李航班号")
    private String baggageFlightNo;

    @ApiModelProperty(value = "行李航线")
    private String baggageSegment;

    @ApiModelProperty(value = "行李经停站")
    private String stopoverSegment;

    @ApiModelProperty(value = "行李备降站")
    private String alternateTerminal;

    @ApiModelProperty(value = "行李规格")
    private String baggageType;

    @ApiModelProperty(value = "逾重行李号")
    private String overweightNo;
}
