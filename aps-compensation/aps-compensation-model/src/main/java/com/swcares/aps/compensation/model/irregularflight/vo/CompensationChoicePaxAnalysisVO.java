package com.swcares.aps.compensation.model.irregularflight.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.aps.compensating.mode.irregularflight.vo <br>
 * Description：赔偿单已选择旅客分析统计Vo <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 10月29日 10:09 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="CompensationChoicePaxAnalysisVO对象", description="赔偿单已选择旅客统计信息")
public class CompensationChoicePaxAnalysisVO {

    @ApiModelProperty(value = "已选旅客数量")
    private String choiceNum;

    @ApiModelProperty(value = "成人数量")
    private String adultNum;

    @ApiModelProperty(value = "儿童数量")
    private String childrenNum;

    @ApiModelProperty(value = "婴儿数量")
    private String babyNum;
}
