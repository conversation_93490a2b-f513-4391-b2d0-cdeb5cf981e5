package com.swcares.aps.web.bff.service;

import com.swcares.aps.component.dict.model.vo.DictCacheVO;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.aps.compensation.bff.service <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 10月26日 9:52 <br>
 * @version v1.0 <br>
 */
public interface SysDictionaryService {


    /**
     * Title： getDictionaryDataList<br>
     * Description： 查所有数据字段<br>
     * author：傅欣荣 <br>
     * date：2021/10/28 11:04 <br>
     * @param dictType
     * @return
     */
    Map<String, List<DictCacheVO>> getDictionaryDataList(List<String> dictType);

    /**
     * Title： getDictionaryDataList<br>
     * Description： 查所有数据字段<br>
     * author：傅欣荣 <br>
     * date：2021/10/28 11:04 <br>
     * @param dictType
     * @return
     */
    Map<String, List<DictCacheVO>> getDictionaryDataList(String dictType);
}
