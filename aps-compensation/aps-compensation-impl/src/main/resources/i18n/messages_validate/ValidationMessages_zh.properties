javax.validation.constraints.NotNull.message = 不能为空
javax.validation.constraints.Pattern.message = 参数错误

# 业务属性名称配置，此配置应用于实体中注解message上，例如：@NotNull(message={uc.user.username})
Account.username=用户名
Account.password=密码

# 人员
Employee.name=人员姓名
Employee.phone=电话
Employee.employeeCode=工号
Employee.emailAddress=邮箱
Employee.graduateFrom=院校

#机构
OrganizationPagedDTO.id=机构id
OrganizationBatchAuthDTO.sysFeatureResourceIds=机构关联的资源集合
OrganizationBatchAuthDTO.orgId=机构ID集合
Organization.serialNo=机构代码
Organization.name=机构名称
Organization.shortName=机构简称
Organization.nameEn=机构英文名称
Organization.type=机构类型
Organization.code=机构编码
Organization.personInCharge=机构负责人
Organization.phone=机构负责人电话
Organization.address=机构地址
Organization.introduction=机构简介

# 租户
TenantDTO.name=客户名
TenantDTO.companyName=客户公司姓名
TenantDTO.contactor=客户联系人
TenantDTO.contactorPhone=客户联系电话
TenantDTO.contactorEmail=客户联系邮箱
TenantDTO.validFrom=客户有效起始时间
TenantDTO.validTo=客户有效终止时间
TenantDTO.status=客户状态
TenantDTO.userName=客户帐号用户名
TenantDTO.dbUrl=客户数据源url
TenantDTO.dbUserName=客户数据源用户名
TenantDTO.dbPassword=客户数据源密码
TenantDTO.dbDriver=客户数据源驱动
TenantDTO.domainName=客户域名
TenantChangeStatusDTO.ids=客户id列表
TenantChangeStatusDTO.status=客户状态
TenantUpdateDTO.id=客户id

ReplaceBaseRuleDTO.isSameFlight=是否同航班旅客
ReplaceBaseRuleDTO.isContainSelf=是否包含本人
ReplaceBaseRuleDTO.maxApplyPassenger=最大可申领取乘机人数

ReplacePayPeriodRuleDTO.paymentWaitingPeriod=代人领取支付等待期