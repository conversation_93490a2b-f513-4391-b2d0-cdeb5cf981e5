package com.swcares.aps.compensation.model.irregularflight.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * ClassName：com.swcares.aps.compensating.mode.irregularflight.dto <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 11月09日 15:34 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="FreezeOrderPaxDTO对象", description="赔偿单-冻结旅客")
public class FreezeOrderPaxDTO {
    private List<String> paxIds;
    private String status;
    private String orderId;
}
