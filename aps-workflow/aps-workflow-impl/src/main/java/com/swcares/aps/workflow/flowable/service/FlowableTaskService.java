package com.swcares.aps.workflow.flowable.service;

import com.swcares.aps.workflow.dto.CompleteProcessParamsDTO;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.task.api.Task;

import java.util.List;

/**
 * ClassName：FlowableTaskService <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/2/10 <br>
 * @version v1.0 <br>
 */
public interface FlowableTaskService {

    /**
     * 完成普通任务
     * @param task
     * @param params
     */
    void completeTask(Task task, CompleteProcessParamsDTO params);


    /**
     * 完成自动任务
     * @param params
     * @param automaticTaskIds
     * @return
     */
    void completeAutomaticTask(CompleteProcessParamsDTO params, List<String> automaticTaskIds);

    /**
     * @title completePushTask
     * @description @TODO
     * <AUTHOR>
     * @date 2025/4/21 14:36
     * @return
     */
    void completePushTask(CompleteProcessParamsDTO params, List<String> pushTaskIds);
    /**
     * 获取审核人列表
     * @param
     * @return
     */
    List<String> getAuditorPosition(String taskId);

    /**
     * @title getLastAssignee
     * @description 获取上一次该节点的处理人（驳回流程时有值）
     * <AUTHOR>
     * @date 2022/3/15 14:12
     * @param task
     * @param historicProcessInstance
     * @return
     */
    String getLastAssignee(Task task, HistoricProcessInstance historicProcessInstance);

    /**
     * 获取当前待审核得task
     * @param processInstanceId
     * @return
     */
    List<Task> getCurrentProcessTask(String processInstanceId);


}
