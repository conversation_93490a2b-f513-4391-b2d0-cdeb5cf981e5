package com.swcares.aps.compensation.model.complaint.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
@ApiModel(value="ComplaintAccidentQueryDto对象", description="事故单列表查询参数")
public class ComplaintAccidentQueryDto extends PagedDTO {

    @ApiModelProperty(value = "航班号",example = "CA8888")
    private String flightNo;

    @ApiModelProperty(value = "航班开始日期",example = "2024-01-01")
    private String flightStartDate;

    @ApiModelProperty(value = "航班结束日期",example = "2024-01-02")
    private String flightEndDate;

    @ApiModelProperty(value = "出发航站",example = "PEK")
    private String org;

    @ApiModelProperty(value = "到达航站",example = "CTU")
    private String dst;

    @ApiModelProperty(value = "旅客投诉事故单号")
    private String accidentId;

    @ApiModelProperty(value = "事故单来源",example = "全部：0，机场：1，航司：2")
    private String accidentSource;

    @ApiModelProperty(value = "归属航司")
    private List<String> belongAirline;

    @ApiModelProperty(value = "事故单状态")
    private List<String> accidentStatus;

    @ApiModelProperty(value = "原因类型")
    private String reasonType;

    @ApiModelProperty(value = "事故类型")
    private List<String> accidentSubType;

}
