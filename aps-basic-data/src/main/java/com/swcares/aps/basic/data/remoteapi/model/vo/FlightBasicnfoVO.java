package com.swcares.aps.basic.data.remoteapi.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName：FlightBaseInfoVo
 * @Description：航班基本信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/14 11:55
 * @version： v1.0
 */
@Data
@ApiModel(value="FlightBaseInfoVo", description="航班基本信息")
public class FlightBasicnfoVO {

    @ApiModelProperty(value = "航班ID")
    private String flightId;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "航班类型")
    private String flightType;

    @ApiModelProperty(value = "起飞三字码")
    private String pod;

    @ApiModelProperty(value = "到达三字码")
    private String poa;

    @ApiModelProperty(value = "起飞机场 中文")
    private String departPort;

    @ApiModelProperty(value = "到达机场 中文")
    private String arrivalPort;

    @ApiModelProperty(value = "航段三字码")
    private String segment;

    @ApiModelProperty(value = "航段中文")
    private String segmentCh;

    @ApiModelProperty(value = "备降机场1")
    private String alternateAirport;

    @ApiModelProperty(value = "备降机场2")
    private String alternateAirport2;

    @ApiModelProperty(value = "备降机场1-中文")
    private String alternateAirportCh;

    @ApiModelProperty(value = "备降机场2-中文")
    private String alternateAirport2Ch;

    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    @ApiModelProperty(value = "预计起飞时间")
    private String etd;

    @ApiModelProperty(value = "实际起飞时间")
    private String atd;

    @ApiModelProperty(value = "机号")
    private String acReg;

    @ApiModelProperty(value = "机型")
    private String acType;

    @ApiModelProperty(value = "实际到达时间")
    private String ata;

    @ApiModelProperty(value = "计划到达时间")
    private String sta;

    @ApiModelProperty(value = "预计到达时间")
    private String eta;

    @ApiModelProperty(value = "订座数")
    private String bookSeat;

    @ApiModelProperty(value = "总座数")
    private String seatNum;

    @ApiModelProperty(value = "航班状态")
    private String flightStatus;

    @ApiModelProperty(value = "航班状态 D-延误|C-取消")
    private String flightRunState;

    @ApiModelProperty(value = "备降/返航标志,RC 返航 VC 备降")
    private String flgVr;

    @ApiModelProperty(value = "不正常对内原因")
    private String innerReason;

    @ApiModelProperty(value = "不正常对外原因")
    private String exterReason;



}
