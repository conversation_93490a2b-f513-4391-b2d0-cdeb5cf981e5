package com.swcares.aps.basic.data.remoteapi.model.dto;

import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName：PassengerQueryDto
 * @Description：航班旅客-查询参数
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/14 15:01
 * @version： v1.0
 */
@Data
@ApiModel(value="PassengerQueryDto", description="航班旅客-查询参数")
@SecretInfoEntity
public class PassengerQueryDTO {

    @ApiModelProperty(value = "旅客证件号、姓名、票号查询")
    private String keySearch;

    @ApiModelProperty(value = "旅客证件号、姓名、票号查询")
    private String encryptionKeySearch;

    @ApiModelProperty(value = "旅客id")
    private String paxId;
    @ApiModelProperty(value = "航班号")
    private String flightNo;
    @ApiModelProperty(value = "航班日期")
    private String flightDate;
    @ApiModelProperty(value = "旅客姓名")
    private String paxName;
    @ApiModelProperty(value = "姓名证件号")
    @SecretValue
    private String idNo;
    @ApiModelProperty(value = "票号")
    private String tktNo;
    @ApiModelProperty(value = "行李号 【从藏航数据库查询：行李号是单独一个查询sql，其他字段配合航班时间航班号是另一个sql。】")
    private String bagTag;
    @ApiModelProperty(value = "所选航段")
    private String choiceSegment;
    @ApiModelProperty(value = "包含取消旅客1包含,0不包含")
    private String containsCancel;
    @ApiModelProperty(value = "包含N舱1包含,0不包含")
    private String containsN;
    @ApiModelProperty(value = "购票时间开始")
    private String tktEndDateStart;
    @ApiModelProperty(value = "包购票时间结束")
    private String tktEndDateEnd;
    @ApiModelProperty(value = "取消开始时间开始")
    private String cancelDateStart;
    @ApiModelProperty(value = "取消开始时间结束")
    private String cancelDateEnd;
    @ApiModelProperty(value = "值机状态 全部，PT（出票），NA（未值机），AC（值机），XR（值机取消），CL（订座取消），SB（候补），DL（拉下）")
    private String checkStatus;
    private String compensateType;
    @ApiModelProperty(value = "舱位")
    private List<String> cabin;

    @ApiModelProperty(value = "出发地")
    private String org;

    @ApiModelProperty(value = "值机序号")
    private BigDecimal boardingNumber;
    /**
     * 旅客IDS
     */
    @ApiModelProperty(value = "旅客IDS")
    private List<String> paxIds;

    @ApiModelProperty(value = "旅客类别")
    private List<String> paxType;
}
