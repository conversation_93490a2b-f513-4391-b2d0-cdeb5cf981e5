/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：AbstractSemiSmsTaskBuilder <br>
 * Package：com.swcares.reaptv.msg.flightevent.taskbuilder <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.flightevent.taskbuilder;

import com.swcares.aps.component.dict.model.vo.DictCacheVO;
import com.swcares.aps.msg.model.entity.FlightEvent;
import com.swcares.aps.msg.model.entity.FlightEventPassenger;
import com.swcares.aps.msg.model.entity.SmsTaskDetailInfo;
import com.swcares.aps.msg.model.entity.SmsTaskInfo;
import com.swcares.aps.msg.model.enums.FlightEventEnum;
import com.swcares.aps.msg.model.enums.SmsChannelEnum;
import com.swcares.baseframe.utils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * ClassName：com.swcares.reaptv.msg.flightevent.taskbuilder.AbstractSemiSmsTaskBuilder <br>
 * Description：抽象的半自动短信任务构建器
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/19 11:06 <br>
 * @version:
 */
@Slf4j
public abstract class AbstractSemiSmsTaskBuilder implements SemiAutoSmsTaskBuilder{
    private static final String TASK_NAME_SUFFIX = "航变短信";
    private static final DateTimeFormatter tasknameDateFormatter = DateTimeFormatter.ofPattern("M.d");
    private static final DateTimeFormatter timeWithoutSecondFormatter = DateTimeFormatter.ofPattern("HH:mm");
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final String CABIN_CLASS_C = "公务舱";
    private static final String CABIN_CLASS_Y = "经济舱";
    private static final String NO_SUPPLEMENT_FLIGHT_REMARK = "补班待定";
    
    private static final int DEFAULT_OFFSET_HOURS = 8;
    String createTaskName(FlightEvent flightEvent){
        StringBuilder name = new StringBuilder();
        name.append(flightEvent.getFlightNumber()).append(flightEvent.getOrg())
                .append(flightEvent.getDest());
        if (flightEvent.getAdjustType() != null){
            name.append(flightEvent.getAdjustType().getCode());
        }
        return name.toString();
    }
    
    Date toDate(LocalDate localDate){
        return toDate(localDate, DEFAULT_OFFSET_HOURS);
    }
    
    Date toDate(LocalDate localDate, int offsetHours){
        if (localDate == null){
            return null;
        }
        return Date.from(localDate.atStartOfDay(ZoneOffset.ofHours(offsetHours)).toInstant());
    }
    
    Date toDate (LocalDateTime localDateTime){
        return toDate(localDateTime, DEFAULT_OFFSET_HOURS);
    }
    
    Date toDate(LocalDateTime localDateTime, int offsetHours){
        if (localDateTime == null){
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneOffset.ofHours(offsetHours)).toInstant());
    }
    
    Date toDate(LocalDate localDate, LocalTime localTime){
        return toDate(localDate, localTime, DEFAULT_OFFSET_HOURS);
    }

    Date toDate(LocalDate localDate, LocalTime localTime, int offsetHours){
        if (localDate == null || localTime == null){
            return null;
        }
        LocalDateTime localDateTime =LocalDateTime.of(localDate, localTime);
        return toDate(localDateTime, offsetHours);
    }
    
    String formateTimeWithoutSeconds(LocalTime time){
        return timeWithoutSecondFormatter.format(time);
    }
    
    String formateTimeWithoutSeconds(LocalDateTime dateTime){
        return timeWithoutSecondFormatter.format(dateTime);
    }
    
    String formateDate(LocalDate date){
        return dateFormatter.format(date);
    }
    
    String formateDate(LocalDateTime dateTime){
        return dateFormatter.format(dateTime);
    }
    
    Date parseFliehgtEventTime(String date){
        try {
            SimpleDateFormat df = new SimpleDateFormat();
            df.applyPattern("yyyyMMddHHmm");
            return df.parse(date);
        } catch (ParseException e) {
            return null;
        }
    }

    List<SmsTaskDetailInfo> createTaskDetailInfos(FlightEvent flightEvent, List<FlightEventPassenger> passengers){
        List<SmsTaskDetailInfo> results = new ArrayList<>();
        for (FlightEventPassenger passenger : passengers){
            if (StringUtils.isNotBlank(passenger.getTelePhone())){
                Arrays.stream(passenger.getTelePhone().split(",")).forEach(phoneNumber->{
                    results.add(createSingleSmsTaskDetailInfo(flightEvent, passenger, phoneNumber));
                });
            } else {
                results.add(createSingleSmsTaskDetailInfo(flightEvent, passenger, null));
            }
        }

        return results;
    }
    
    private SmsTaskDetailInfo createSingleSmsTaskDetailInfo(FlightEvent flightEvent, FlightEventPassenger passenger, String phoneNumber) {
        SmsTaskDetailInfo detailInfo = new SmsTaskDetailInfo();
        detailInfo.setPnr(passenger.getPnrCode());
        detailInfo.setPhoneNumber(phoneNumber);
        detailInfo.setTicketNumber(passenger.getTicketNumber());
        detailInfo.setPassengerName(passenger.getPassengerName());
        detailInfo.setVipType(passenger.isVip()?"1":"0");
        detailInfo.setFlightNumber(passenger.getFlightNumber());
        detailInfo.setOrg(passenger.getOrg());
        detailInfo.setDst(passenger.getDest());
        detailInfo.setEtd(toDate(passenger.getDepTime()));
        detailInfo.setEta(toDate(passenger.getArrTime()));
        detailInfo.setFlightDate(toDate(passenger.getFlightDate()));
        detailInfo.setTargetFlightNumber(passenger.getTargetFlightNumber());
        detailInfo.setTargetEta(passenger.getTargetArrTime());
        detailInfo.setTargetEtd(passenger.getTargetDepTime());
        detailInfo.setMiddleAp(flightEvent.getTransitAirport());
        detailInfo.setTargetFlightDate(passenger.getTargetSegmentDate());
        return detailInfo;
    }

    private Date getDepartTime(Date flightDate, String departTime){
        if (flightDate== null || StringUtils.isBlank( departTime )){
            return null;
        }
        try {
            return DateUtils.ymdhms2Date(DateUtils.formatDate(flightDate, DateUtils.DEF_PTN_YMD) + " " + departTime + ":00");
        } catch (Exception e) {
            log.warn("时间格式{}无效", departTime);
            return null;
        }
    }

    private String appendSecondValue(String timeString){
        if (timeString == null){
            return null;
        }

        if (timeString.split(":").length == 2){
            return timeString + ":00";
        }

        return timeString;
    }

    SmsTaskInfo createSmsTaskInfo(FlightEvent flightEvent, String templateId, String templateContent, List<FlightEventPassenger> passengers){
        SmsTaskInfo taskInfo = new SmsTaskInfo();
        taskInfo.setTaskName(createTaskName(flightEvent));
        taskInfo.setTemplateId(templateId);
        taskInfo.setSendPhone(SmsChannelEnum.CUSTOMER.getKey());
        taskInfo.setSmsContent(templateContent);
        taskInfo.setFlightStartDate(toDate(flightEvent.getStartDate()));
        taskInfo.setFlightEndDate(toDate(flightEvent.getEndDate()));
        taskInfo.setFlightSchedule(formatFlightSchedule(flightEvent.getFrequency()));
        taskInfo.setAdjustReason(flightEvent.getAdjustReason());
        taskInfo.setAdjustType(flightEvent.getAdjustType());
        taskInfo.setFlightEventId(flightEvent.getId());
        taskInfo.setDepTime(getDepTimeString(flightEvent));
        taskInfo.setDesTime(getDesTimeString(flightEvent));
        taskInfo.setOrigDepTime(flightEvent.getOrigDepTime());
        taskInfo.setOrigDesTime(flightEvent.getOrigDesTime());
        taskInfo.setPlaneType(flightEvent.getPlaneType());
        taskInfo.setNewPlaneType(flightEvent.getNewPlaneType());
        taskInfo.setPeopleCount(getDistinctTickets(passengers));
        taskInfo.setPnrCount(getDistinctPnrs(passengers));

        return taskInfo;
    }

    private int getDistinctTickets(List<FlightEventPassenger> passengers){
        Set<String> distinctTickets = new HashSet<>();
        AtomicInteger ticketCount = new AtomicInteger(0);

        if (passengers == null || passengers.isEmpty()){
            return 0;
        }

        for(FlightEventPassenger passenger : passengers){
            if (StringUtils.isBlank(passenger.getTicketNumber())){
                ticketCount.incrementAndGet();
                continue;
            }

            if (!distinctTickets.contains(passenger.getTicketNumber())){
                ticketCount.incrementAndGet();
                distinctTickets.add(passenger.getTicketNumber());
            }
        }

        return ticketCount.get();
    }


    private int getDistinctPnrs(List<FlightEventPassenger> passengers){
        Set<String> distinctPnrs = new HashSet<>();

        if (passengers == null || passengers.isEmpty()){
            return 0;
        }
        for(FlightEventPassenger passenger : passengers){
            String pnr = passenger.getPnrCode();
            if (StringUtils.isBlank(pnr)){
                pnr = StringUtils.EMPTY;  //所有没有pnr的在pnr数量中计为一个
            }
            distinctPnrs.add(passenger.getPnrCode());
        }

        return distinctPnrs.size();
    }


    private String getDepTimeString(FlightEvent flightEvent){
        if (flightEvent.getDepTime() == null){
            return null;
        }

        String result = formateTimeWithoutSeconds(flightEvent.getDepTime());
        if (flightEvent.isAcrossDay()){
            result = result + "+1";
        }

        return result;
    }

    private String getDesTimeString(FlightEvent flightEvent){
        if (flightEvent.getArrTime() == null){
            return null;
        }

        String result = formateTimeWithoutSeconds(flightEvent.getArrTime());
        if (flightEvent.isFlightOverNight()){
            result = result + "+1";
        }

        return result;

    }

    abstract FlightEventEnum getFlightEventEnum();

    private String formatFlightSchedule(String schedule){
        if (StringUtils.isEmpty(schedule)){
            return schedule;
        }

        StringBuilder formatedScheduler = new StringBuilder();
        String seperator = ",";
        for (int i=0;i<schedule.length(); i++){
            if (i>0){
                formatedScheduler.append(seperator);
            }
            formatedScheduler.append(schedule.charAt(i));
        }

        return formatedScheduler.toString();

    }


    boolean isCabinDegraded(FlightEventPassenger passenger, List<DictCacheVO> cabinClassConfig) {
        if (com.swcares.baseframe.utils.lang.StringUtils
                .isBlank(passenger.getCabinType()) || com.swcares.baseframe.utils.lang.StringUtils.isBlank(passenger.getNewCabinType())){
            return false;
        }

        String oldCabinClass = getCabinClass(passenger.getCabinType(), cabinClassConfig);
        String newCabinClass = getCabinClass(passenger.getNewCabinType(), cabinClassConfig);

        //只有从公务舱变成经济舱的时候，才认为是降舱
        if (CABIN_CLASS_C.equals(oldCabinClass) && CABIN_CLASS_Y.equals(newCabinClass)){
            return true;
        }

        return false;

    }

    String getCabinClass(String cabinType, List<DictCacheVO> cabinClassConfig){
        if (cabinType == null){
            return null;
        }

        for(DictCacheVO cabin : cabinClassConfig){
            if (cabin.getDictItem().equalsIgnoreCase(cabinType)){
                return cabin.getItemValue();
            }
        }

        return null;
    }

    boolean isProtected(FlightEventPassenger passenger){
        if (StringUtils.isBlank( passenger.getTargetFlightNumber() )
                || passenger.getTargetSegmentDate() == null
                || StringUtils.isBlank(passenger.getTargetSegment())
                || passenger.getTargetDepTime() == null
                || passenger.getTargetArrTime() == null ){
            return false;  //模板航班的航班号、航班日期、航段、起飞时间、到达时间中任何一个为空，都认为是没有保护
        }
        
        return true;
    }
    
    boolean hasSupplementFlight(FlightEvent flightEvent){
        if (NO_SUPPLEMENT_FLIGHT_REMARK.equals(flightEvent.getRemark())){
            return false;
        }
        
        return true;
    }
}
