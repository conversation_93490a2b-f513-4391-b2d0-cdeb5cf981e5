package com.swcares.aps.compensation.model.irregularflight.dto;

import com.swcares.aps.compensation.model.irregularflight.entity.AccidentRecordDO;
import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.dto.AccidentRecordDTO <br>
 * Description：事故单将操作记录表 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="AccidentRecordDTO对象", description="事故单将操作记录表")
public class AccidentRecordDTO extends AccidentRecordDO implements BaseDTO{

    private static final long serialVersionUID = 1L;

}
