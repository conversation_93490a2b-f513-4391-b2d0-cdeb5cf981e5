package com.swcares.aps.compensation.model.privilege.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> <PERSON> Yi
 * @Classname SwitchStatusRequestDTO
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/12 08:57
 * @Version 1.0
 */
@Data
@ApiModel(value="SwitchBusinessPrivilegeStatusRequestDTO", description="切换授权的状态的请求DTO,用于启用和停用对机场的授权")
public class SwitchBusinessPrivilegeStatusRequestDTO {
    @ApiModelProperty(value = "被授权方的代码，例如机场的三字码")
    private List<String> recipientCodes;

    @ApiModelProperty(value = "是否禁用")
    private boolean disabled;
}
