package com.swcares.aps.compensation.model.privilege.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Classname DataPrivilege
 * @Description 数据授权配置数据
 * @Version 1.0.0
 * @Date 2024/5/13 10:21
 * @Created by <PERSON> Yi
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("business_privilege")
public class AirlineBusinessPrivilege extends BaseEntity {

//    //业务类型
//    @TableField(value = "business_type_code")
//    private String businessTypeCode;

    //授权方客户编码
    @TableField("grantor_code")
    private String grantorCode;

    //授权方客户类型
    @TableField("grantor_category")
    private String grantorCategory;

    //授权方客户名称
    @TableField("grantor_name")
    private String grantorName;

    //被授权方客户编码
    @TableField("recipient_code")
    private String recipientCode;

    //被授权方客户类型
    @TableField("recipient_category")
    private String recipientCategory;

    //被授权方客户名称
    @TableField("recipient_name")
    private String recipientName;

    //停用标识（1：停用；0：启用）
    @TableField(value = "disabled", fill = FieldFill.INSERT)
    private boolean disabled;

    @TableField(value = "updater_employee_name")
    private String updaterEmployeeName;

    @TableField(value="updater_job_number")
    private String updaterJobNumber;

//    //是否资金授权
//    @TableField(value = "grant_bankroll")
//    private boolean grantBankroll;

    @TableField(value = "remark")
    private String remark;

    @TableField(value = "tenant_id")
    private Long tenantId;
}
