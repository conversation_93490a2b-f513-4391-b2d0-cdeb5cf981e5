package com.swcares.aps.compensation.model.irregularflight.dto;

import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.dto.CompensationOrderInfoDTO <br>
 * Description：赔偿单信息 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="CompensationOrderInfoDTO对象", description="赔偿单信息")
public class CompensationOrderInfoDTO extends CompensationOrderInfoDO implements BaseDTO{
    @ApiModelProperty(value = "成人领取金额")
    private BigDecimal currentAmount;

    private static final long serialVersionUID = 1L;

}
