package com.swcares.aps.compensation.model.complaint.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(value = "PassengerComplaintDto对象", description = "投诉旅客信息卡片")
public class PassengerComplaintDto {

    @ApiModelProperty(value = "旅客姓名",required = true)
    @NotBlank(message = "旅客姓名不能为空")
    private String passengerName;

    @ApiModelProperty(value = "证件号码",required = true)
    @NotBlank(message = "证件号码不能为空")
    private String idNumber;

    @ApiModelProperty(value = "票号",required = true)
    @NotBlank(message = "票号不能为空")
    private String ticketNumber;

    @ApiModelProperty(value = "证件类型",required = true)
    @NotBlank(message = "证件类型不能为空")
    private String idType;

}
