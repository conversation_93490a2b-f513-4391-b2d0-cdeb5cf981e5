package com.swcares.aps.compensation.model.overbook.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：OverBookH5CompensationDetailsVO
 * @Description：H5超售详情-补偿单信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/27 15:57
 * @version： v1.0
 */
@Data
@ApiModel(value="补偿单信息VO对象", description="补偿单信息VO对象")
public class OverBookH5CompensationDetailsVO {

    @ApiModelProperty(value = "补偿单id")
    private String orderId;

    @ApiModelProperty(value = "补偿单号")
    private String orderNo;

    @ApiModelProperty(value = "补偿方式(补偿方式 1现金，2虚拟，3实物)")
    private String compensateType;

    @ApiModelProperty(value = "补偿子方式(补偿方式 1微信，2银联，3数字人民币)")
    private String compensateSubType;

    @ApiModelProperty(value = "补偿总金额")
    private String sumMoney;

    @ApiModelProperty(value = "补偿单状态(0草稿,1审核中,2审核不通过,3驳回,4审核通过,5生效,6关闭,7逾期)")
    private String status;

}
