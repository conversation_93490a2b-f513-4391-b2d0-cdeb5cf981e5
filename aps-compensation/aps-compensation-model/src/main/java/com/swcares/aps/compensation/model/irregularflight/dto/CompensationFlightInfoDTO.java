package com.swcares.aps.compensation.model.irregularflight.dto;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationFlightInfoDO;
import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.dto.CompensationFlightInfoDTO <br>
 * Description：赔偿航班信息 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="CompensationFlightInfoDTO对象", description="赔偿航班信息")
public class CompensationFlightInfoDTO extends CompensationFlightInfoDO implements BaseDTO{

    private static final long serialVersionUID = 1L;

}
