<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.msg.mapper.SmsTaskDetailMapper">
    <select id="getSmsTaskSendDetailByPnr" resultType="com.swcares.aps.msg.model.vo.SmsTaskSendDetailVO">
        SELECT *
        from (
                 SELECT Listagg(a.ID, ',') within group (order by a.ID ) id,
                        a.TASK_ID,
                        a.PNR                 PNR,
                        a.FLIGHT_NUMBER       flightNumber,
                        a.FLIGHT_DATE         flightDate,
                        a.ORG || '-' || a.DST flightSegment,
                        max(a.VIP_TYPE) passengerType,
                        a.SENDER_NAME || a.SENDER_JOB_NUMBER sendUser,
                        b.VIEW_MODE           viewMode,
                        max(a.SEND_TIME)         finishTime,
                        Listagg(a.SEND_STATUS, ',') within group (order by a.SEND_STATUS ) status,
                        Listagg(a.remark, ',') within group (order by a.remark) remark
                 FROM
                     MSG_MESSAGE_TASK_DETAIL a
                     LEFT JOIN MSG_MESSAGE_TASK b
                 ON a.TASK_ID = b.TASK_ID
                 where a.TASK_ID = #{dto.taskId}
                 <if test="dto.detailIds!=null">
                     AND a.id IN
                     <foreach collection="dto.detailIds" index="index" item="detailId" separator="," open="(" close=")">
                         #{detailId}
                     </foreach>
                 </if>
                GROUP BY a.TASK_ID, a.PNR, a.FLIGHT_NUMBER, a.FLIGHT_DATE, a.ORG, a.DST, a.SENDER_NAME, a.SENDER_JOB_NUMBER, b.CREATE_USER, b.VIEW_MODE
             )t
        <where>
            <if test="dto.flightNumber!=null and dto.flightNumber!=''">
                AND t.flightNumber = #{dto.flightNumber}
            </if>
            <if test="dto.flightBeginDate!=null and dto.flightBeginDate!=''">
                AND t.flightDate &gt;=to_DATE(#{dto.flightBeginDate},'yyyy-MM-dd')
            </if>
            <if test="dto.flightEndDate!=null and dto.flightEndDate!=''">
                AND t.flightDate &lt;=to_DATE(#{dto.flightEndDate},'yyyy-MM-dd')
            </if>
            <if test="dto.pnr!=null and dto.pnr!=''">
                AND t.PNR = #{dto.pnr}
            </if>
            <if test="dto.mobile!=null and dto.mobile!=''">
                AND t.phoneNumber  = #{dto.mobile}
            </if>
        </where>
        ORDER BY flightDate
    </select>

    <select id="getSmsTaskSendDetailByTktNo" resultType="com.swcares.aps.msg.model.vo.SmsTaskSendDetailVO">
        SELECT *
        from (
                 SELECT
            Listagg(a.ID, ',')   within group (order by a.ID ) id,
            a.TASK_ID,
            Listagg(a.PHONE_NUMBER, ',')   within group (order by a.PHONE_NUMBER) phoneNumber,
            a.TICKET_NUMBER ticketNumber,
            a.FLIGHT_NUMBER flightNumber,
            a.FLIGHT_DATE flightDate,
            a.ORG||'-'||a.DST flightSegment,
            max(a.VIP_TYPE) passengerType,
            a.SENDER_NAME || a.SENDER_JOB_NUMBER sendUser,
            b.VIEW_MODE viewMode,
            max(a.SEND_TIME) finishTime,
            Listagg(a.SEND_STATUS ,',') within group (order by a.SEND_STATUS ) status,
            Listagg(a.remark, ',') within group (order by a.remark) remark
                 FROM
                     MSG_MESSAGE_TASK_DETAIL a
                     LEFT JOIN MSG_MESSAGE_TASK b
                 ON a.TASK_ID = b.TASK_ID
                 where a.TASK_ID = #{dto.taskId}
                <if test="dto.detailIds!=null">
                    AND a.id IN
                    <foreach collection="dto.detailIds" index="index" item="detailId" separator="," open="(" close=")">
                        #{detailId}
                    </foreach>
                </if>
                GROUP BY a.TASK_ID, a.TICKET_NUMBER, a.FLIGHT_NUMBER, a.FLIGHT_DATE, a.ORG, a.DST, a.VIP_TYPE, a.SENDER_NAME, a.SENDER_JOB_NUMBER,  b.CREATE_USER, b.VIEW_MODE
             )t
        <where>
            <if test="dto.flightNumber!=null and dto.flightNumber!=''">
                AND t.flightNumber = #{dto.flightNumber}
            </if>
            <if test="dto.flightBeginDate!=null and dto.flightBeginDate!=''">
                AND t.flightDate &gt;=to_DATE(#{dto.flightBeginDate},'yyyy-MM-dd')
            </if>
            <if test="dto.flightEndDate!=null and dto.flightEndDate!=''">
                AND t.flightDate &lt;=to_DATE(#{dto.flightEndDate},'yyyy-MM-dd')
            </if>
            <if test="dto.tktNo!=null and dto.tktNo!=''">
                AND t.ticketNumber = #{dto.tktNo}
            </if>
            <if test="dto.mobile!=null and dto.mobile!=''">
                AND t.phoneNumber LIKE  '%'||#{dto.mobile}||'%'
            </if>
        </where>
        ORDER BY flightDate
    </select>

    <select id="getIssueOrdinarySmsTaskDetailInfo" resultType="com.swcares.aps.msg.model.entity.SmsTaskDetailInfo">
        SELECT mmtd.* FROM MSG_MESSAGE_TASK_DETAIL mmtd
        LEFT JOIN MSG_MESSAGE_TASK mmt ON mmt.TASK_ID = mmtd.TASK_ID
        WHERE 1=1
        AND mmt.FLIGHT_EVENT = #{dto.event}
        AND mmtd.FLIGHT_NUMBER =#{dto.flightNumber}
        AND TO_CHAR(mmtd.FLIGHT_DATE,'yyyy-MM-dd') =#{dto.flightDate}
        AND mmtd.ORG =#{dto.org}
        AND mmtd.DST =#{dto.dst}
        AND mmtd.PNR  =#{dto.pnr}
        AND mmtd.TICKET_NUMBER =#{dto.tktNumber}
        AND mmtd.SEND_STATUS = 3
        AND mmt.RECIVER_TYPE != 0
    </select>

    <select id="getSmsTaskSendDetailByMobile" resultType="com.swcares.aps.msg.model.vo.SmsTaskSendDetailVO">
        SELECT *
        from (
                 SELECT a.ID                  id,
                        a.TASK_ID,
                        a.PHONE_NUMBER        phoneNumber,
                        a.TICKET_NUMBER       ticketNumber,
                        a.FLIGHT_NUMBER       flightNumber,
                        a.FLIGHT_DATE         flightDate,
                        a.ORG || '-' || a.DST flightSegment,
                        a.VIP_TYPE passengerType,
                        a.SENDER_NAME || a.SENDER_JOB_NUMBER sendUser,
                        b.VIEW_MODE           viewMode,
                        a.SEND_TIME         finishTime,
                        a.SEND_STATUS         status,
                        a.remark            remark
                 FROM MSG_MESSAGE_TASK_DETAIL a
                          LEFT JOIN MSG_MESSAGE_TASK b
                                    ON a.TASK_ID = b.TASK_ID
                 where a.TASK_ID = #{dto.taskId}
                <if test="dto.detailIds!=null">
                    AND a.id IN
                    <foreach collection="dto.detailIds" index="index" item="detailId" separator="," open="(" close=")">
                        #{detailId}
                    </foreach>
                </if>
             )t
        <where>
            <if test="dto.flightNumber!=null and dto.flightNumber!=''">
                AND t.flightNumber = #{dto.flightNumber}
            </if>
            <if test="dto.flightBeginDate!=null and dto.flightBeginDate!=''">
                AND t.flightDate &gt;=to_DATE(#{dto.flightBeginDate},'yyyy-MM-dd')
            </if>
            <if test="dto.flightEndDate!=null and dto.flightEndDate!=''">
                AND t.flightDate &lt;=to_DATE(#{dto.flightEndDate},'yyyy-MM-dd')
            </if>
            <if test="dto.tktNo!=null and dto.tktNo!=''">
                AND t.ticketNumber = #{dto.tktNo}
            </if>
            <if test="dto.mobile!=null and dto.mobile!=''">
                AND t.phoneNumber  = #{dto.mobile}
            </if>
        </where>
        ORDER BY flightDate
    </select>
    <select id="getArriveSmsTaskDetailInfo" resultType="com.swcares.aps.msg.model.entity.SmsTaskDetailInfo">
        SELECT mmtd.* FROM MSG_MESSAGE_TASK_DETAIL mmtd
        LEFT JOIN MSG_MESSAGE_TASK mmt ON mmt.TASK_ID = mmtd.TASK_ID
        WHERE 1=1
        AND mmt.FLIGHT_EVENT = #{dto.event}
        AND mmtd.FLIGHT_NUMBER =#{dto.flightNumber}
        AND TO_CHAR(mmtd.FLIGHT_DATE,'yyyy-MM-dd') = #{dto.flightDateStr}
        AND mmtd.ORG =#{dto.org}
        AND mmtd.DST =#{dto.dst}
        AND mmtd.SEND_STATUS = 3
    </select>

    <select id="getAuditPassenger" resultType="com.swcares.aps.msg.model.vo.MessagePassengerVo">
        SELECT *
        from (
        SELECT Listagg(a.ID, ',') within group (order by a.ID ) id,
        a.FLIGHT_DATE flightDate,
        a.FLIGHT_NUMBER flightNumber,
        a.org || ',' || a.dst segment,
        a.ETD etd,
        a.ETA eta,
        a.PASSENGER_NAME name,
        Listagg(a.PHONE_NUMBER, ',') within group (order by a.PHONE_NUMBER ) mobile,
        a.PNR pnr,
        a.TICKET_NUMBER tktNo,
        a.VIP_TYPE type
        FROM MSG_MESSAGE_TASK_DETAIL a
        WHERE a.TASK_ID =#{dto.taskId}
        GROUP BY a.FLIGHT_DATE, a.FLIGHT_NUMBER, a.ETD ,a.ETA ,a.PASSENGER_NAME ,a.PNR ,a.TICKET_NUMBER, a.VIP_TYPE, a.org, a.dst
        )t
        <where>
            <if test="dto.name!=null and dto.name!=''">
                AND t.name = #{dto.name}
            </if>
            <if test="dto.mobile!=null and dto.mobile!=''">
                AND t.mobile  LIKE  '%'||#{dto.mobile}||'%'
            </if>
            <if test="dto.pnr!=null and dto.pnr!=''">
                AND t.pnr = #{dto.pnr}
            </if>
            <if test="dto.tktNo!=null and dto.tktNo!=''">
                AND t.tktNo = #{dto.tktNo}
            </if>
            <if test="dto.type!=null and dto.type!=''">
                AND t.type = #{dto.type}
            </if>
            <if test="dto.flightBeginDate!=null and dto.flightBeginDate!=''">
                AND t.flightDate &gt;=to_DATE(#{dto.flightBeginDate},'yyyy-MM-dd')
            </if>
            <if test="dto.flightEndDate!=null and dto.flightEndDate!=''">
                AND t.flightDate &lt;=to_DATE(#{dto.flightEndDate},'yyyy-MM-dd')
            </if>
        </where>
        order by DECODE(type,'1',1) ,flightDate,pnr
    </select>
    <select id="getSmsTaskList" resultType="com.swcares.aps.msg.model.vo.SmsTaskVO">
        SELECT * from
        ( SELECT a.ID id,
        a.TASK_ID taskId,
        a.TASK_NAME taskName,
        a.TASK_STATUS taskStatus,
        a.TASK_TYPE taskType,
        a.SMS_TYPE smsType,
        a.BUSINESS_TYPE businessType,
        a.FLIGHT_START_DATE flightBeginDate,
        a.FLIGHT_END_DATE flightEndDate,
        a.FLIGHT_SCHEDULE flightSchedule,
        a.PEOPLE_COUNT peopleCount,
        a.PNR_COUNT pnrCount,
        a.CREATE_USER|| a.CREATE_USER_JOB_NUMBER createUser,
        a.CREATE_TIME creatTime,
        a.FLIGHT_EVENT_ID flightEventId,
        DECODE(a.TASK_STATUS,'5',a.STOP_TIME,a.FINISH_TIME) finishTime,
        CASE a.RECIVER_TYPE
        WHEN '3' THEN '3'
        WHEN '2' THEN '2'
        WHEN '5' THEN '2'
        ELSE '1'
        END reSendType,
        a.EMERGENCY_DEGREE degree,
        (SELECT count(*) from MSG_MESSAGE_TASK_DETAIL b where TASK_ID = a.TASK_ID AND (b.SEND_STATUS = '2' or b.SEND_STATUS='5') ) failCount
        FROM  MSG_MESSAGE_TASK a
        <where>
            <if test="dto.createUser!=null and dto.createUser!=''">
                a.CREATE_USER = #{dto.createUser} OR a.CREATE_USER_JOB_NUMBER = #{dto.createUser}
            </if>
        </where>
        )t

        <where>
        <if test="dto.taskStatus!=null and dto.taskStatus!=''">
          AND INSTR(#{dto.taskStatus}, t.taskStatus) > 0
        </if>
        <if test="dto.taskType!=null and dto.taskType!=''">
            AND INSTR(#{dto.taskType}, t.taskType) > 0
        </if>
        <if test="dto.smsType!=null and dto.smsType!=''">
            AND INSTR(#{dto.smsType}, t.smsType) > 0
        </if>
        <if test="dto.businessType!=null and dto.businessType!=''">
            AND INSTR(#{dto.businessType}, t.businessType) > 0
        </if>
        <if test="dto.taskName!=null and dto.taskName!=''">
           AND t.taskName like '%'||#{dto.taskName}||'%'
        </if>
        <if test="dto.flightEndDate!=null and dto.flightEndDate!=''">
            AND t.flightBeginDate &lt;= to_DATE(#{dto.flightEndDate},'yyyy-MM-dd')
        </if>
        <if test="dto.flightBeginDate!=null and dto.flightBeginDate!=''">
            AND t.flightEndDate &gt;= to_DATE(#{dto.flightBeginDate},'yyyy-MM-dd')
        </if>
        <if test="schedules!=null">
            AND
            <foreach collection="schedules" item="item" index="index" open="(" close=")" separator=" OR">
                t.flightSchedule like '%'||#{item}||'%'
            </foreach>
        </if>
        <if test="dto.finishBeginTime!=null and dto.finishBeginTime!=''">
            AND t.finishTime &gt;= to_DATE(#{dto.finishBeginTime},'yyyy-MM-dd')
        </if>
        <if test="dto.finishEndTime!=null and dto.finishEndTime!=''">
            AND t.finishTime &lt;= to_DATE(#{dto.finishEndTime},'yyyy-MM-dd')+1
        </if>
        <if test="dto.creatBeginTime!=null and dto.creatBeginTime!=''">
                AND t.creatTime &gt;= to_DATE(#{dto.creatBeginTime},'yyyy-MM-dd')
        </if>
        <if test="dto.creatEndTime!=null and dto.creatEndTime!=''">
                AND t.creatTime &lt;= to_DATE(#{dto.creatEndTime},'yyyy-MM-dd')+1
        </if>
        <if test="dto.degree!=null and dto.degree!=''">
            AND INSTR(#{dto.degree}, t.degree) > 0
        </if>
            <if test="dto.flightEventId!=null and dto.flightEventId!=''">
                AND t.flightEventId = #{dto.flightEventId}
            </if>
        </where>
        ORDER BY DECODE(taskStatus,'0',1,'2',2,'3',3,'4',4,'1',5,'5',6),DECODE(degree,'0',1,'1',2),creatTime
    </select>

    <select id="getSmsTaskDegree" resultType="java.lang.String">
        SELECT
        a.EMERGENCY_DEGREE
        FROM  MSG_MESSAGE_TASK a
        WHERE a.TASK_ID = #{taskId}
    </select>

    <update id="updateSmsTaskDetailState">
        UPDATE MSG_MESSAGE_TASK_DETAIL t
        SET t.SEND_STATUS = #{state}
        WHERE t.ID IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>
</mapper>

