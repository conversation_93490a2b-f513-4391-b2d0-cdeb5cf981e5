package com.swcares.aps.basic.data.businessimpl.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName：BaggagePaxDTO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/7/26 11:36
 * @version： v1.0
 */

@Data
public class CompensationPaxSearchDTO {
    @ApiModelProperty(value = "旅客证件号、姓名、票号查询")
    private String keySearch;
    @ApiModelProperty(value = "旅客id")
    private String paxId;
    @ApiModelProperty(value = "航班号")
    @NotBlank
    private String flightNo;
    @ApiModelProperty(value = "航班日期")
    @NotBlank
    private String flightDate;
    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "姓名证件号")
    private String idNo;
    @ApiModelProperty(value = "票号")
    private String tktNo;
    @ApiModelProperty(value = "行李号")
    private String bagTag;
    @ApiModelProperty(value = "所选航段")
    private String choiceSegment;
    @ApiModelProperty(value = "包含取消旅客1包含,0不包含")
    private String containsCancel;
    @ApiModelProperty(value = "包含N舱1包含,0不包含")
    private String containsN;
    @ApiModelProperty(value = "购票时间开始")
    private String tktEndDateStart;
    @ApiModelProperty(value = "包购票时间结束")
    private String tktEndDateEnd;
    @ApiModelProperty(value = "取消开始时间开始")
    private String cancelDateStart;
    @ApiModelProperty(value = "取消开始时间结束")
    private String cancelDateEnd;
    @ApiModelProperty(value = "值机状态 全部，PT（出票），NA（未值机），AC（值机），XR（值机取消），CL（订座取消），SB（候补），DL（拉下）")
    private String checkStatus;
    @ApiModelProperty(value = "补偿单类型")
    private String compensateType;
}
