package com.swcares.aps.web.bff.service.impl;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.compensation.model.replace.dto.ReplaceBaseRuleDTO;
import com.swcares.aps.compensation.model.replace.dto.ReplacePayPeriodRuleDTO;
import com.swcares.aps.compensation.model.replace.dto.ReplaceRuleDTO;
import com.swcares.aps.compensation.model.replace.vo.ReplaceBaseRuleVO;
import com.swcares.aps.compensation.model.replace.vo.ReplacePayPeriodRuleVO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.aps.web.bff.service.ReplaceRuleService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * ClassName：com.swcares.aps.compensation.bff.web.service.impl <br>
 * Description：代领人规则<br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 01月10日 12:22 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
public class ReplaceRuleServiceImpl implements ReplaceRuleService {

    @Autowired
    private CompensationInfoApi compensationInfoApi;

    @Override
    public BaseResult<Object> saveBaseRule(ReplaceBaseRuleDTO replaceRuleDTO) {
        log.info("saveBaseRule:{}", JSONUtil.toJsonStr(replaceRuleDTO));
        BaseResult<Object> result = compensationInfoApi.saveReplaceBaseRule(replaceRuleDTO);
        log.info("saveBaseRule:{},result:{}", JSONUtil.toJsonStr(replaceRuleDTO), JSONUtil.toJsonStr(result));
        return result;
    }

    @Override
    public BaseResult<ReplaceBaseRuleVO> getBaseRule() {
        BaseResult<ReplaceRuleDTO> enableReplaceRule = compensationInfoApi.getReplaceRule();
        log.info("getBaseRule,result:{}", JSONUtil.toJsonStr(enableReplaceRule));
        ReplaceRuleDTO replaceRuleDTO = enableReplaceRule.getData();
        ReplaceBaseRuleVO replaceBaseRuleVO = ObjectUtils.copyBean(replaceRuleDTO, ReplaceBaseRuleVO.class);
        return BaseResult.ok(replaceBaseRuleVO);
    }

    @Override
    public BaseResult<Object> savePayWaitPeriod(ReplacePayPeriodRuleDTO replacePayPeriodRuleDTO) {
        log.info("savePayWaitPeriod:{}", JSONUtil.toJsonStr(replacePayPeriodRuleDTO));
        BaseResult<Object> result = compensationInfoApi.saveReplacePayWaitPeriodRule(replacePayPeriodRuleDTO);
        log.info("savePayWaitPeriod:{},result:{}", JSONUtil.toJsonStr(replacePayPeriodRuleDTO), JSONUtil.toJsonStr(result));
        return result;
    }

    @Override
    public BaseResult<ReplacePayPeriodRuleVO> getPayWaitPeriod() {
        BaseResult<ReplaceRuleDTO> enableReplaceRule = compensationInfoApi.getReplaceRule();
        log.info("getPayWaitPeriod,result:{}", JSONUtil.toJsonStr(enableReplaceRule));
        ReplaceRuleDTO replaceRuleDTO = enableReplaceRule.getData();
        ReplacePayPeriodRuleVO replacePayPeriodRuleVO=ObjectUtils.copyBean(replaceRuleDTO, ReplacePayPeriodRuleVO.class);
        return BaseResult.ok(replacePayPeriodRuleVO);
    }
}
