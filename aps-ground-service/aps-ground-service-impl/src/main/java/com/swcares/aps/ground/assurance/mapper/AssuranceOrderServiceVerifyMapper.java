package com.swcares.aps.ground.assurance.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.ground.models.assurance.dto.AssuranceVerifyQueryRequest;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderServiceVerifyDO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderServiceVerifyUserDetailVO;
import com.swcares.baseframe.common.mybatis.base.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @ClassName：AssuranceOrderServiceVerificateMapper
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/21 16:05
 * @version： v1.0
 */
@Mapper
public interface AssuranceOrderServiceVerifyMapper extends BaseMapper<AssuranceOrderServiceVerifyDO> {

    IPage<AssuranceOrderServiceVerifyUserDetailVO> getOrderServiceVerifyDetailList(@Param("dto") AssuranceVerifyQueryRequest request, Page<AssuranceOrderServiceVerifyUserDetailVO> page);

}
