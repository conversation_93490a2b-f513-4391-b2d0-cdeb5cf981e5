package com.swcares.aps.compensation.model.compensation.vo;

import com.swcares.aps.compensation.model.compensation.entity.CompensationMaterialInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationFlightInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：CompensationBaseExtInfoVO
 * @Description：补偿单详情信息，不同类型补偿单详情信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/9 17:20
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationDetailExtInfoVO对象", description="现金补偿单详情下信息VO")
public class CompensationDetailExtInfoVO extends CompensationOrderInfoDO {
    @ApiModelProperty(value = "创建时间(日期格式)")
    private String compensationCreatedTime;


    @ApiModelProperty(value = "是否自定义现金")
    private String isCustom;

    /**
     * 补偿单旅客信息
     */
    @ApiModelProperty(value = "补偿单旅客信息")
    private List<CompensationPaxInfoDO> compensationPaxInfo;

    /**
     * 赔偿的箱包
     */
    @ApiModelProperty(value = "赔偿的箱包")
    private List<CompensationMaterialInfoDO> compensationMaterialInfo;

    /**
     * 补偿单航班信息
     */
    @ApiModelProperty(value = "补偿单航班信息")
    private CompensationFlightInfoDO compensationFlightInfo;

    @ApiModelProperty(value = "是否可以进行审核，Y是 N否")
    private String toExamine;

    @ApiModelProperty(value = "审核节点id")
    private String taskId;

    @ApiModelProperty(value = "是否为发起人")
    private Boolean isSponsor;
}
