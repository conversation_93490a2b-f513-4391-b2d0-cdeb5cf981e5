package com.swcares.aps.compensation.model.compensation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CompensationMaterialDetailCompensationVO
 * @Description：箱包补偿单详情下赔偿单的信息VO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 唐康
 * @Date： 2022/4/14 14:55
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationMaterialDetailCompensationVO对象", description="箱包补偿单详情下赔偿单的信息VO")
public class CompensationMaterialDetailCompensationVO {
    @ApiModelProperty(value = "补偿单id")
    private String compensationId;

    @ApiModelProperty(value = "补偿总金额")
    private String sumMoney;

    @ApiModelProperty(value = "补偿单号")
    private String orderNo;

    @ApiModelProperty(value = "补偿单状态 0草稿,驳回,2审核中,3审核通过,4生效,5关闭,6逾期,7审核不通过")
    private String status;

    @ApiModelProperty(value = "服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "保障服务（0不存在，1存在）")
    private String ensureType;

    @ApiModelProperty(value = "补偿方式 1现金，2虚拟，3实物")
    private String compensateType;

    @ApiModelProperty(value = "补偿标准类型：1标准补偿金额 2自定义")
    private String compensateStandard;

    @ApiModelProperty(value = "补偿子方式(补偿方式 1微信，2银联，3数字人民币)")
    private String compensateSubType;

    @ApiModelProperty(value = "物品id")
    private String commodityId;

    @ApiModelProperty(value = "事故单id")
    private String accidentId;

    @ApiModelProperty(value = "事故单单号")
    private String accidentNo;

    @ApiModelProperty(value = "补偿事故单子类型- 补偿子类型-1延误，2取消，3备降，4返航，5补班; 21破损,22少收,23多收,24内件缺失,25丢失")
    private String accidentSubType;

    @ApiModelProperty(value = "补偿事故单类型- 1.不正常航班 2异常行李 3超售")
    private String accidentType;

    @ApiModelProperty(value = "异常行李事故单类型（21破损,22少收,23多收,24内件缺少,25丢失）")
    private String type;

    @ApiModelProperty(value = "旅客数据输入的来源 SYSTEM（系统查询）MANUAL_INPUT（人工输入），")
    private String inputSource;
}
