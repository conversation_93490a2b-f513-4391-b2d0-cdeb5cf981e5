package com.swcares.aps.usercenter.remote.api.innermail;

import com.swcares.aps.usercenter.model.innermail.dto.InnerMailDeleteDto;
import com.swcares.aps.usercenter.model.innermail.dto.InnerMailReceivePageDto;
import com.swcares.aps.usercenter.model.innermail.dto.InnerMailSendDto;
import com.swcares.aps.usercenter.model.innermail.dto.InnerMailSentPageDto;
import com.swcares.aps.usercenter.model.innermail.vo.InnerMailSentPageVo;
import com.swcares.aps.usercenter.model.innermail.vo.InnerMailStaticsByBusinessTypeVO;
import com.swcares.aps.usercenter.model.innermail.vo.InnerMailUnreadCountVO;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.components.msg.dto.InteriorMessageDepositoryDTO;
import com.swcares.components.msg.dto.InteriorMessageDepositoryPagedDTO;
import com.swcares.components.msg.dto.InteriorMessageSendDTO;
import com.swcares.components.msg.dto.InteriorReceiveInfoCollectDTO;
import com.swcares.components.msg.vo.InteriorMessageDepositoryVO;
import com.swcares.components.msg.vo.InteriorMessageVO;
import com.swcares.components.msg.vo.InteriorReceiveInfoVO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created on  2022/5/19
 * Title:       [站内信]
 * Description: [站内信]
 *
 * @author: like
 * @version: 1.0
 */
@FeignClient(name = "aps-user-center", path = "/api/aps-user-center", contextId = "messageInnerMailApi")
public interface MessageInnerMailApi {

    @PostMapping("/msg/innerMail/send_inner_page")
    @ApiOperation("站内信分页列表查询")
    PagedResult<List<InnerMailSentPageVo>> sentInnerPage(@RequestBody InnerMailSentPageDto dto);

    @PostMapping({"/msg/innerMail/send_msg"})
    @ApiOperation("内部消息发送接口，发送方式包括微信、钉钉、短信和站内信")
    BaseResult<Object> send(@RequestBody InteriorMessageSendDTO dto);

    @PostMapping({"/msg/innerMail/send_msg_as_user"})
    @ApiOperation("内部消息发送接口:以指定用户的身份发送")
    public BaseResult<Object> sendMessageAsUser(@RequestBody InnerMailSendDto dto);

    @PostMapping({"/msg/innerMail/send_msg_as_system_user"})
    @ApiOperation("内部消息发送接口，发送方式包括微信、钉钉、短信和站内信")
    BaseResult<Object> sendAsSystemUser(@RequestBody InteriorMessageSendDTO dto);

    @PostMapping("/msg/innerMail/delete")
    BaseResult<Object> deleteSend(@RequestBody InnerMailDeleteDto dto);

    @PostMapping({"/msg/innerMail/update"})
    @ApiOperation("修改内部消息存放管理表,包括站内,短信等消息,记录")
    BaseResult<Object> update(@RequestBody InteriorMessageDepositoryDTO dto);

    @GetMapping({"/msg/innerMail/get/{id}"})
    @ApiOperation("通过ID查询内部消息存放管理表,包括站内,短信等消息,记录")
    BaseResult<InteriorMessageDepositoryVO> get(@PathVariable @ApiParam(value = "主键id") Long id);

    @GetMapping({"/msg/innerMail/get"})
    @ApiOperation("通过ID查询内部消息存放管理表,包括站内,短信等消息,记录；URL传参")
    BaseResult<InteriorMessageDepositoryVO> getById(@RequestParam @ApiParam(value = "主键id") Long id);

    @PostMapping({"/msg/innerMail/page"})
    @ApiOperation("条件分页查询内部消息存放管理表,包括站内,短信等消息,记录")
    PagedResult<List<InteriorMessageVO>> page(@RequestBody InteriorMessageDepositoryPagedDTO dto);

    @PostMapping({"/msg/innerMail/receive_delete"})
    @ApiOperation("通过ID删除内部接收信息表记录")
    BaseResult<Object> receiveDel(@RequestBody InnerMailDeleteDto dto);

    @PostMapping({"/msg/innerMail/collect"})
    @ApiOperation("接收信息收藏/取消收藏接口")
    BaseResult<Object> collect(@RequestBody InteriorReceiveInfoCollectDTO dto);

    @PostMapping("/msg/innerMail/msg_read")
    @ApiOperation("接收信息已读接口")
    BaseResult<Object> receiveMsgRead(@RequestBody InnerMailDeleteDto dto);

    @PostMapping({"/msg/innerMail/receive_inner_page"})
    @ApiOperation("条件分页查询内部接收信息表记录")
    PagedResult<List<InteriorReceiveInfoVO>> receiveInnerPage(@RequestBody InnerMailReceivePageDto dto);

    @PostMapping({"/msg/innerMail//receive_inner_list"})
    @ApiOperation("条件分页查询内部接收信息表记录")
    BaseResult<List<InteriorReceiveInfoVO>> receiveInnerList(@RequestBody InnerMailReceivePageDto dto);

    @PostMapping({"/msg/innerMail/send_mobile"})
    @ApiOperation("给工作人员发送短信接口")
    BaseResult<Object> sendMobile(@RequestBody InteriorMessageSendDTO dto);


    @PostMapping({"/msg/innerMail/send_customer_mobile"})
    @ApiOperation("给旅客发送短信接口")
    BaseResult<Object> sendCustomerMobile(@RequestBody InteriorMessageSendDTO dto);


    @GetMapping({"/msg/innerMail/unread_inner_mail_count"})
    @ApiOperation("获取当前用户未读站内信信息。给定业务类型businessType，则返回指定类型下的未读数量;如果不指定businessType，则返回所有未读站内信数量。")
    public BaseResult<InnerMailUnreadCountVO> getUnreadInnermailCount(@RequestParam(value = "businessType", required = false) String businessType);

    @GetMapping({"/msg/innerMail/statics_by_businesstype"})
    @ApiOperation("获取当前用户收到的站内信按业务类型划分的统计信息，包括未读消息条数，最优先的第一条消息的信息。"
            + "<br/>如果指定了businessType，则返回指定businessType的统计信息；"
            + "<br/>如果没有指定businessType，则返回所有的BusinessType的统计信息。")
    public BaseResult<List<InnerMailStaticsByBusinessTypeVO>> getStaticsByBusinessType(
            @RequestParam(value = "businessType", required = false) String businessType);

    @PostMapping({"/msg/innerMail/paged_by_businesstype"})
    @ApiOperation("获取当前用户收到的指定类型站内信，按照未读在前、紧急在前、新站内信在前的规则排序")
    public PagedResult<List<InteriorReceiveInfoVO>> getInnerMailByBusinessType(
            @RequestBody InnerMailReceivePageDto pageDto);
}
