package com.swcares.aps.compensation.model.compensation.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * @ClassName：AddAccidentCompensationDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 16:07
 * @version： v1.0
 */
@Data
public class AddAccidentCompensationDTO {

    /***
     * 事故单ID
     */
    @ApiModelProperty(value = "事故单ID")
    @NotNull
    private String accidentId;

    /***
     * 事故单类型
     */
    @ApiModelProperty(value = "事故单类型  1.不正常航班 2异常行李 3超售")
    @NotNull
    @Pattern(regexp="^[1|2|3]$")
    private String accidentType;
}
