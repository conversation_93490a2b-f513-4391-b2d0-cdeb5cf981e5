package com.swcares.aps.compensation.model.dataconfig.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName：CabinConfig
 * @Description：按租户航司存储公务舱和经济舱
 * @Copyright：© 2023 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2024/5/14 16:04
 * @version： v1.0
 */
@Data
@TableName("compensation_cabin_config")
@ApiModel(value="cabin对象", description="按租户航司存储公务舱和经济舱，舱位用逗号拼接")
public class CabinConfigDO extends BaseEntity {

    @ApiModelProperty(value = "航司二字码")
    private String airlineCode;

    @ApiModelProperty(value = "航司简称")
    private String airlineName;

    @ApiModelProperty(value = "商务舱")
    private String businessCabin;

    @ApiModelProperty(value = "经济舱")
    private String economyCabin;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "租户code")
    private String tenantCode;


}
