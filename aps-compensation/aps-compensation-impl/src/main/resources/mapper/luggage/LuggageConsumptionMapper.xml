<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.baggage.luggage.mapper.LuggageConsumptionMapper">

    <update id="updateByEntity">
        update luggage_consumption_details set

        LUGGAGE_NO = #{dto.luggageId}
        COMPENSATION_ID  = #{dto.compensation_id}
        COMPENSATION_NO = #{dto.compensation_no}
        STATUS = #{dto.status}
        AMOUNT  = #{dto.amount}
        STOCK = #{dto.stock}
        EXECUTOR = #{dto.executor}
        REASON = #{dto.reason}
        UPDATED_TIME = SYSDATE

        where LUGGAGE_ID = #{dto.luggageId}




    </update>

    <select id="findDetailed" resultType="com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageConsumptionDetailVO" databaseId="mysql">
        select
        lcd.id,
        lcd.luggage_id,
        lcd.luggage_no,
        lcd.compensation_id,
        lcd.compensation_no,
        lcd.amount,
        lcd.stock,
        lcd.executor,
        lcd.created_time,
        lcd.status,
        lcd.reason
        from luggage_consumption_details lcd
        <where>
            lcd.luggage_id = #{dto.luggageId}
            <if test="dto.compensationNo != null and dto.compensationNo != ''">
                and lcd.compensation_no = #{dto.compensationNo}
            </if>
            <if test="dto.startDate != null and dto.startDate !=''">
                and date_format(lcd.created_time,'%Y-%m-%d') between #{dto.startDate} and #{dto.endDate}
            </if>
            <if test="dto.status != null and dto.status !=''">
                and lcd.status = #{dto.status}
            </if>
            <if test="dto.reason != null and dto.reason != ''">
                and lcd.reason = #{dto.reason}
            </if>
            <if test="dto.executor != null and dto.executor != ''">
                and lcd.executor like concat('%',#{dto.executor},'%')
            </if>
        </where>
        order by lcd.created_time desc
    </select>
    <select id="findDetailed" resultType="com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageConsumptionDetailVO" databaseId="oracle">
        select
        lcd.id,
        lcd.luggage_id,
        lcd.luggage_no,
        lcd.compensation_id,
        lcd.compensation_no,
        lcd.amount,
        lcd.stock,
        lcd.executor,
        lcd.created_time,
        lcd.status,
        lcd.reason
        from luggage_consumption_details lcd
        <where>
            lcd.luggage_id = #{dto.luggageId}
            <if test="dto.compensationNo != null and dto.compensationNo != ''">
                and lcd.compensation_no = #{dto.compensationNo}
            </if>
            <if test="dto.startDate != null and dto.startDate !=''">
                and TO_CHAR(lcd.created_time,'yyyy-mm-dd') between #{dto.startDate} and #{dto.endDate}
            </if>
            <if test="dto.status != null and dto.status !=''">
                and lcd.status = #{dto.status}
            </if>
            <if test="dto.reason != null and dto.reason != ''">
                and lcd.reason = #{dto.reason}
            </if>
            <if test="dto.executor != null and dto.executor != ''">
                and lcd.executor like concat(concat('%',#{dto.executor}),'%')
            </if>
        </where>
        order by lcd.created_time desc
    </select>

</mapper>