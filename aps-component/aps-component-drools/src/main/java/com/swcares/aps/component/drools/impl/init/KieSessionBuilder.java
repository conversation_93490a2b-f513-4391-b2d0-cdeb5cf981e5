package com.swcares.aps.component.drools.impl.init;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.aps.component.drools.impl.service.impl.DroolsRuleServiceImpl;
import com.swcares.aps.component.drools.model.entity.DroolsRuleDO;
import com.swcares.baseframe.common.core.util.RedisUtil;
import com.swcares.baseframe.common.exception.SystemException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.drools.core.impl.InternalKnowledgeBase;
import org.drools.core.impl.KnowledgeBaseFactory;
import org.kie.api.io.ResourceType;
import org.kie.api.runtime.KieSession;
import org.kie.internal.builder.KnowledgeBuilder;
import org.kie.internal.builder.KnowledgeBuilderFactory;
import org.kie.internal.io.ResourceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName：KieSessionBuilder
 * @Description：项目启动提前初始化好规则引擎需要的东西
 * @Copyright：© 2023 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 夏阳
 * @Date： 2023/6/26 14:06
 * @version： v1.0
 */
@Component
@Slf4j
public class KieSessionBuilder {

    @Deprecated
    //KieSession对象依赖太多，序列化会堆栈溢出
    private final String PREFIX = "DROOLS:COMPENSATION:";

    private Map<String, KieSession> KieSessionMap = new ConcurrentHashMap<>();

    @Autowired
    private DroolsRuleServiceImpl droolsRuleServiceImpl;

    @Autowired
    private RedisUtil redisUtil;

    @PostConstruct
    public void initKieSession() {
        log.info("【aps-component-drools】-------------开始创建规则引擎-------------");
        Map<String, DroolsRuleDO> map = loadRulesFromDB();
        log.info("【aps-component-drools】-------------从数据库加载的规则内容为【{}------------", JSONUtil.toJsonStr(map.keySet()));
        for (Map.Entry<String, DroolsRuleDO> entry : map.entrySet()) {
            KnowledgeBuilder kb = KnowledgeBuilderFactory.newKnowledgeBuilder();
            try {
                kb.add(ResourceFactory.newByteArrayResource(entry.getValue().getRuleContent().getBytes("utf-8")), ResourceType.DRL);
            } catch (UnsupportedEncodingException e) {
                log.error("【aps-component-drools】加载规则引擎信息后，转字符编码报错，从数据库加载内容为【{}】,异常为", JSONUtil.toJsonStr(entry.getValue()), e);
                continue;
            }
            if (kb.hasErrors()) {
                log.error("【aps-component-drools】规则语法异常【{}】", kb.getErrors().toString());
                continue;
            }

            InternalKnowledgeBase kBase = KnowledgeBaseFactory.newKnowledgeBase();
            kBase.addPackages(kb.getKnowledgePackages());
            KieSession kieSession = kBase.newKieSession();
            //业务来看，没有改变，暂时不设置过期时间，需要删除去数据库找
            //基础框架的序列化用的fastjson,这东西bug多，兼容性差,换hutool和jackson都不行，对象太大了，有循环依赖
            //redis存储不进去
            //redisUtil.set(PREFIX + entry.getKey(), JSONUtil.toJsonStr(kieSession));
            KieSessionMap.put(entry.getKey(), kieSession);
        }
        log.info("【aps-component-drools】-------------规则引擎创建完毕-------------");
    }

    public Map<String, DroolsRuleDO> loadRulesFromDB(){
        DroolsRuleDO droolsRuleDO = new DroolsRuleDO();
        LambdaQueryWrapper<DroolsRuleDO> wrapper = Wrappers.lambdaQuery(droolsRuleDO);
        List<DroolsRuleDO> list = droolsRuleServiceImpl.getBaseMapper().selectList(wrapper);
        if(ObjectUtils.isEmpty(list)){
            // 未从数据库加载到规则信息，无法创建规则引擎
            throw new SystemException(00000);
        }
        return list.stream().collect(Collectors.toMap(DroolsRuleDO:: getCode, Function.identity()));
    }

    public KieSession getKieSession(String code){
        KieSession kieSession =  KieSessionMap.get(code);
        if(ObjectUtils.isEmpty(kieSession)){
            //考虑了下不需要双重判断是否为空，一是这个是赔付建单并发不高，二是就算多刷一次也没关系
            reloadDrools();
            kieSession = (KieSession) JSONUtil.parseObj(redisUtil.get(PREFIX + code));
        }
        log.info("【aps-compensation-impl】耗时统计——规则引擎,获取到的kieSession的hashCode, 【{}】", kieSession.hashCode());
        return kieSession;
    }

    public synchronized void reloadDrools(){
        initKieSession();
    }
}
