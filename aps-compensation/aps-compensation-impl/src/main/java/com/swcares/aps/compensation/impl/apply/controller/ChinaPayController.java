package com.swcares.aps.compensation.impl.apply.controller;

import com.swcares.aps.compensation.impl.apply.service.ApplyChinaPayService;
import com.swcares.aps.component.pay.pay.service.chinapay.ChinaAuthUserInfoService;
import com.swcares.aps.component.pay.pay.service.chinapay.bean.ChinaPayAuthRequestDTO;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping("/api/chinaPay")
public class ChinaPayController extends BaseController {
    @Autowired
    private ChinaAuthUserInfoService chinaAuthUserInfoService;
    @Autowired
    ApplyChinaPayService applyChinaPayService;
    /***
     * @title authUnionPay
     * @description 微信端银联实名认证接口
     * <AUTHOR>
     * @date 2022/8/24 17:17
     * @return java.lang.Object
     */
    @PostMapping("/authUnionPay")
    @ResponseBody
    public BaseResult<Object> authUnionPay(@RequestBody @Validated ChinaPayAuthRequestDTO dto){
        BaseResult<Object> result = chinaAuthUserInfoService.authCarUserInfo(dto);
        //银联实名认证通过，添加认证通过标识。
        if(BaseResult.OK_CODE==result.getCode() && result.getData()!= null && (Boolean) result.getData()){
            applyChinaPayService.setTokenChinaPayPass(dto);
        }
        return result;
    }

}