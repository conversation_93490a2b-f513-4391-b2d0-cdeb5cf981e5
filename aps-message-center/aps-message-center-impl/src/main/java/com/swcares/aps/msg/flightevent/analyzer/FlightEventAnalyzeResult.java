/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：FlightEventAnalyzeResult <br>
 * Package：com.swcares.reaptv.msg.flightevent.analyzer <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.flightevent.analyzer;

import com.swcares.aps.msg.model.entity.FlightEvent;
import com.swcares.aps.msg.model.entity.FlightEventPassenger;
import lombok.Data;

import java.util.List;

/**
 * ClassName：com.swcares.reaptv.msg.flightevent.analyzer.FlightEventAnalyzeResult <br>
 * Description：TODO：(这里用一句话描述这个类的作用) <br>
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/5 22:02 <br>
 * @version:
 */
@Data
public class FlightEventAnalyzeResult {
    private FlightEvent flightEvent;
    private List<FlightEventPassenger> flightEventPassengers;
    private List<String> flightDates;
}
