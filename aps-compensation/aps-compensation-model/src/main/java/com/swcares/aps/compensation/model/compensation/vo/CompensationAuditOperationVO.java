package com.swcares.aps.compensation.model.compensation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;


/**
 * @ClassName：CompensationAuditOperationVO
 * @Description：补偿单审核结果VO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/5/9 21:00
 * @version： v1.0
 */
@Getter
@ApiModel(value="CompensationAuditOperationVO对象", description="补偿单审核结果VO")
public class CompensationAuditOperationVO {

    @ApiModelProperty(value = "orderId")
    private String orderId;

    @ApiModelProperty(value = "isCancel")
    private Boolean isCancel;

    @ApiModelProperty(value = "taskId")
    private String taskId;

    @ApiModelProperty(value = "orderAuditorList")
    private Object orderAuditorList;

    @ApiModelProperty(value = "isPrompt")
    private Boolean isPrompt;

    public CompensationAuditOperationVO(String orderId, Boolean isCancel, String taskId, Object orderAuditorList, Boolean isPrompt) {
        this.orderId = orderId;
        this.isCancel = isCancel;
        this.taskId = taskId;
        this.orderAuditorList = orderAuditorList;
        this.isPrompt = isPrompt;
    }
}
