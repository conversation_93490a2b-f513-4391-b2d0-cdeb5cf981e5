package com.swcares.aps.web.bff.util;

import cn.hutool.core.io.IoUtil;
import feign.Response;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;

/**
 * @Classname FeignUtil
 * @Description TODO
 * @Date 2022/6/9 9:00
 * @Created by HuangXin
 */
@Slf4j
public class FeignUtil {

    //导出方法
    public static void export(Response resp, HttpServletResponse response, String fileName) throws IOException {
        response.setCharacterEncoding("UTF-8");
        response.setHeader("content-Type", "application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + ".xlsx", "UTF-8"));

        Response.Body body = resp.body();
        try (InputStream inputStream = body.asInputStream();
             OutputStream outputStream = response.getOutputStream()) {
            IoUtil.copy(inputStream, outputStream);
        } catch (IOException e) {
            throw e;
        }
    }
}
