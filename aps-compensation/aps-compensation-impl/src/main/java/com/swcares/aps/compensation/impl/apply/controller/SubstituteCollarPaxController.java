package com.swcares.aps.compensation.impl.apply.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.compensation.impl.apply.service.SubstituteCollarService;
import com.swcares.aps.compensation.model.apply.dto.SubstituteCollarPaxPageDTO;
import com.swcares.aps.compensation.model.apply.dto.SubstituteCollarUpdDTO;
import com.swcares.aps.compensation.model.apply.vo.SubstituteCollarPaxDetailsVO;
import com.swcares.aps.compensation.model.apply.vo.SubstituteCollarPaxPageVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.security.UserContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ClassName：com.swcares.aps.apply.impl.controller <br>
 * Description：WEB代领旅客模块 <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 01月13日 14:03 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/web/substituteCollar")
@Api(tags = "WEB代领旅客模块接口")
@ApiVersion(value = "WEB代领旅客模块接口 v1.0")
public class SubstituteCollarPaxController extends BaseController {

    @Autowired
    private SubstituteCollarService substituteCollarService;

    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询代领审核列表")
    public PagedResult<List<SubstituteCollarPaxPageVO>> page(@RequestBody SubstituteCollarPaxPageDTO dto) {
        String auditorId=String.valueOf(UserContext.getUserId());
        IPage<SubstituteCollarPaxPageVO> result = substituteCollarService.webPage(dto,auditorId);
        return ok(result);
    }


    @GetMapping("/find")
    @ApiOperation(value = "查代领旅客审核详情")
    public BaseResult<SubstituteCollarPaxDetailsVO> find(@ApiParam(value = "主键id", required = true) Long id) {
        return ok(substituteCollarService.findDetails(id));
    }

    @PostMapping("/updQuickPay")
    @ApiOperation(value = "快速支付")
    public BaseResult<Object> updQuickPay(@RequestBody SubstituteCollarUpdDTO substituteCollarUpdDTO) {
        substituteCollarService.updQuickPay(substituteCollarUpdDTO.getId());
        return ok();
    }

}
