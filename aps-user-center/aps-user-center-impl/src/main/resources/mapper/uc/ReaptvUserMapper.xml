<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.usercenter.impl.uc.mapper.ReaptvUserMapper">
    <resultMap id="userAllVO" type="com.swcares.aps.usercenter.model.uc.vo.ReaptvUserAllVO">
        <result column="id" property="id"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="employee_id" property="employeeId"/>
        <result column="name" property="name"/>
        <result column="avatar" property="avatar"/>
        <result column="password" property="password"/>
        <result column="status" property="status"/>
        <result column="password_status" property="passwordStatus"/>
        <result column="type" property="type"/>
        <result column="deleted" property="deleted"/>
        <result column="last_login_at" property="lastLoginAt"/>
        <result column="modify_password_at" property="modifyPasswordAt"/>
        <result column="work_status" property="workStatus"/>
        <association property="employee" javaType="com.swcares.aps.usercenter.model.uc.vo.ReaptvEmployeeVO">
            <result column="uc_employee_id" property="id"/>
            <result column="uc_employee_created_by" property="createdBy"/>
            <result column="uc_employee_created_time" property="createdTime"/>
            <result column="uc_employee_updated_by" property="updatedBy"/>
            <result column="uc_employee_updated_time" property="updatedTime"/>
            <result column="uc_employee_name" property="name"/>
            <result column="uc_employee_gender" property="gender"/>
            <result column="uc_employee_employee_code" property="employeeCode"/>
            <result column="uc_employee_dingtalk" property="dingtalk"/>
            <result column="uc_employee_wx_openid" property="wxOpenid"/>
            <result column="uc_employee_job_title" property="jobTitle"/>
            <result column="uc_employee_job_position" property="jobPosition"/>
            <result column="uc_employee_phone" property="phone"/>
            <result column="uc_employee_photo_rul" property="photoRul"/>
            <result column="uc_employee_birthday" property="birthday"/>
            <result column="uc_employee_status" property="status"/>
            <result column="uc_employee_graduate_from" property="graduateFrom"/>
            <result column="uc_employee_political_status" property="politicalStatus"/>
            <result column="uc_employee_email_address" property="emailAddress"/>
            <result column="uc_employee_deleted" property="deleted"/>
            <result column="uc_employee_belong_to_org" property="belongToOrg"/>
        </association>
        <association property="organization" javaType="com.swcares.common.uc.vo.OrganizationVO">
            <result column="org_id" property="id"/>
            <result column="org_created_by" property="createdBy"/>
            <result column="org_created_time" property="createdTime"/>
            <result column="org_updated_by" property="updatedBy"/>
            <result column="org_updated_time" property="updatedTime"/>
            <result column="org_serial_no" property="serialNo"/>
            <result column="org_parent_id" property="parentId"/>
            <result column="org_name" property="name"/>
            <result column="org_name_full_path" property="nameFullPath"/>
            <result column="org_short_name" property="shortName"/>
            <result column="org_name_en" property="nameEn"/>
            <result column="org_type" property="type"/>
            <result column="org_code" property="code"/>
            <result column="org_person_in_charge" property="personInCharge"/>
            <result column="org_phone" property="phone"/>
            <result column="org_industry_type" property="industryType"/>
            <result column="org_business_type" property="businessType"/>
            <result column="org_address" property="address"/>
            <result column="org_introduction" property="introduction"/>
            <result column="org_deleted" property="deleted"/>
            <result column="org_path" property="path"/>
            <result column="org_exosystem_id" property="exosystemId"/>
        </association>


    </resultMap>
    <update id="changeStatus">
        update uc_user set status=#{dto.status},updated_by=#{dto.updatedBy},updated_time=#{dto.updatedTime}
        where id in
        <foreach item="id" index="index" collection="dto.ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <sql id="joinSelect">
        SELECT uu.*,
               ue.name          employee_name,
               uo.name          organization_name,
               ue.belong_to_org organization_id,
               ue.work_terminal,
               ue.job_position,
               ue.gender,
               ue.wx_openid,
               ue.id_card,
               uo.name_full_path,
               uo.path,
               ue.job_number,
               ue.phone,
               ue.email_address,
               ue.employee_code,
               ue.info_is_show
        FROM uc_user uu
                 LEFT JOIN uc_employee ue on
            uu.employee_id = ue.id
                 LEFT JOIN uc_organization uo on
            ue.belong_to_org = uo.id
    </sql>

    <select id="page" resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvUserVO">
        <include refid="joinSelect"></include>
        <where>
            uu.deleted = 0 and (ue.deleted = 0 or ue.deleted is null)
            <if test="dto.employeeOrganizationId != null and dto.employeeOrganizationId.size() > 0">
                AND ue.belong_to_org in(
                <!--include统一参数名parent-->
                <bind name="parent" value="dto.employeeOrganizationId" />
                <include refid="com.swcares.common.uc.mapper.OrganizationMapper.recursive_children"></include>
                )
            </if>
            <if test="dto.manageOrganizationId != null and dto.manageOrganizationId.size() > 0">
                AND (ue.belong_to_org in
                <foreach collection="dto.manageOrganizationId" item="orgId" index="index" separator="," open="("
                         close=")">
                    #{orgId}
                </foreach>
                )
            </if>
            <if test="dto.type != null">
                AND uu.type = #{dto.type}
            </if>
            <if test="dto.typeNot != null">
                AND uu.type not in
                <foreach collection="dto.typeNot" item="item" index="index" separator="," open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.status != null">
                AND uu.status = #{dto.status}
            </if>
            <if test="dto.name != null and dto.name != ''">
                AND (uu.name LIKE #{dto.name} OR ue.name LIKE #{dto.name} OR ue.job_number LIKE #{dto.name}  OR ue.email_address LIKE #{dto.name} OR ue.phone = #{dto.phone})
            </if>
            <if test="dto.createTimeStart != null and dto.createTimeEnd != null">
                AND uu.created_time BETWEEN #{dto.createTimeStart} AND #{dto.createTimeEnd}
            </if>
        </where>
        ORDER BY uu.id DESC
    </select>

    <select id="getUserDetail" resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvUserDetailVO">
        <include refid="joinSelect"></include>
        WHERE
        uu.id=#{id}
    </select>

    <select id="getUserByUsernameOrJobNumber" resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvUserDetailVO">
        <include refid="joinSelect"></include>
        WHERE
        uu.deleted = 0 and (ue.deleted = 0 or ue.deleted is null)
        AND
        (
        uu.name=#{keyWord}
        <if test="enableJobNumber">
            OR ue.job_number=#{keyWord}
        </if>
        )
    </select>

    <select id="selectUserAllInfo" resultMap="userAllVO">
        SELECT u.*,
        e.id uc_employee_id,
        e.gender uc_employee_gender,
        e.name uc_employee_name,
        e.employee_code uc_employee_employee_code,
        e.dingtalk uc_employee_dingtalk,
        e.wx_openid uc_employee_wx_openid,
        e.job_title uc_employee_job_title,
        e.job_position uc_employee_job_position,
        e.phone uc_employee_phone,
        e.photo_rul uc_employee_photo_rul,
        e.birthday uc_employee_birthday,
        e.status uc_employee_status,
        e.graduate_from uc_employee_graduate_from,
        e.political_status uc_employee_political_status,
        e.email_address uc_employee_email_address,
        e.deleted uc_employee_deleted,
        e.belong_to_org uc_employee_belong_to_org,
        e.created_by uc_employee_created_by,
        e.created_time uc_employee_created_time,
        e.updated_by uc_employee_updated_by,
        e.updated_time uc_employee_updated_time,
        e.info_is_show,
        o.id org_id,
        o.serial_no org_serial_no,
        o.parent_id org_parent_id,
        o.name org_name,
        o.name_full_path org_name_full_path,
        o.short_name org_short_name,
        o.name_en org_name_en,
        o.type org_type,
        o.code org_code,
        o.person_in_charge org_person_in_charge,
        o.phone org_phone,
        o.industry_type org_industry_type,
        o.business_type org_business_type,
        o.address org_address,
        o.introduction org_introduction,
        o.deleted org_deleted,
        o.path org_path,
        o.exosystem_id org_exosystem_id,
        o.created_by org_created_by,
        o.created_time org_created_time,
        o.updated_by org_updated_by,
        o.updated_time org_updated_time
        FROM uc_user u
        LEFT JOIN uc_employee e on u.employee_id=e.id
        LEFT JOIN uc_organization o on e.belong_to_org=o.id
        where u.id in
        <foreach collection="ids" item="id" index="index" separator="," open="("
                 close=")">
            #{id}
        </foreach>
    </select>

    <select id="workForOrg" resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvUserEmployeeVO">
        SELECT
        uu.*,
        ue.NAME employee_name,
        ue.employee_code,
        ue.job_number,
        ue.info_is_show,
        org.NAME organization_name,
        org.id organization_id,
        org.name_full_path
        FROM
        uc_user uu
        JOIN uc_employee ue ON uu.employee_id = ue.id
        LEFT JOIN uc_organization org ON ue.belong_to_org = org.id
        <where>
            uu.deleted = 0 and (ue.deleted = 0 or ue.deleted is null)
            <if test="dto.children">
                and uu.id in (
                select wo.user_id from uc_user_work_for_organization wo
                join(
                <!--include统一参数名parent-->
                <bind name="parent" value="{dto.organizationId}" />
                <include refid="com.swcares.common.uc.mapper.OrganizationMapper.recursive_children"></include>
                ) children2 on wo.organization_id=children2.id
                )
            </if>
            <if test="!dto.children">
                and uu.id in (
                select wo.user_id from uc_user_work_for_organization wo where wo.organization_id=#{dto.organizationId}
                )
            </if>
            <if test="dto.type != null">
                AND uu.type = #{dto.type}
            </if>
            <if test="dto.status != null">
                AND uu.status = #{dto.status}
            </if>
            <if test="dto.name != null and dto.name != ''">
                AND (uu.name LIKE #{dto.name} OR ue.name LIKE #{dto.name} OR ue.job_number LIKE #{dto.name} )
            </if>
            <if test="dto.jobTitle != null and dto.jobTitle != ''">
                AND ue.job_title = #{dto.jobTitle}
            </if>

            <if test="dto.jobPosition != null and dto.jobPosition != ''">
                AND ue.job_position like #{dto.jobPosition}
            </if>
        </where>
    </select>
    <select id="belongToOrgPage" resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvUserEmployeeVO">
        <include refid="belongToOrgSelect"/>
    </select>

    <select id="belongToOrg" resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvUserEmployeeVO" parameterType="com.swcares.common.uc.dto.UserBelongToOrgDTO">
        <include refid="belongToOrgSelect"/>
    </select>

    <sql id="belongToOrgSelect">
        SELECT
        uu.*,
        ue.NAME employee_name,
        ue.employee_code,
        ue.job_number,
        ue.info_is_show,
        org.NAME organization_name,
        org.id organization_id,
        org.name_full_path
        FROM
        uc_user uu
        LEFT JOIN uc_employee ue ON uu.employee_id = ue.id
        LEFT JOIN uc_organization org ON ue.belong_to_org = org.id
        <where>
            uu.deleted = 0 and (ue.deleted = 0 or ue.deleted is null)
            <if test="null != dto.children and dto.children and null != dto.organizationId and dto.organizationId != ''">
                and org.id in(
                select children2.id from (
                <!--include统一参数名parent-->
                <bind name="parent" value="{dto.organizationId}" />
                <include refid="com.swcares.common.uc.mapper.OrganizationMapper.recursive_children"></include>
                ) children2
                )
            </if>
            <if test="null != dto.children and!dto.children and null != dto.organizationId and dto.organizationId != ''">
                and org.id = #{dto.organizationId}
            </if>
            <if test="dto.type != null">
                AND uu.type = #{dto.type}
            </if>
            <if test="dto.status != null">
                AND uu.status = #{dto.status}
            </if>
            <if test="dto.name != null and dto.name != ''">
                AND (uu.name LIKE #{dto.name} OR ue.name LIKE #{dto.name} OR ue.job_number LIKE #{dto.name} )
            </if>
        </where>
    </sql>


    <select id="getLoginUserDetails" resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvUserVO">
        <include refid="joinSelect"></include>
        WHERE
        uu.deleted = 0 and (ue.deleted = 0 or ue.deleted is null)
        <choose>
            <when test="loginType==@com.swcares.common.enums.UserLoginTypeEnum@DING_TALK">
                AND ue.dingtalk = #{keyword}
            </when>
            <when test="loginType==@com.swcares.common.enums.UserLoginTypeEnum@MOBILE">
                AND ue.phone = #{keyword}
            </when>
            <when test="loginType==@com.swcares.common.enums.UserLoginTypeEnum@WECHAT">
                AND ue.wx_openid = #{keyword}
            </when>
            <otherwise>
                and 1=2
            </otherwise>
        </choose>
    </select>


    <select id="getLoginUserDetailsByWechatOpenid" resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvUserVO">
        SELECT uu.*,
        ue.name          employee_name,
        uo.name          organization_name,
        ue.belong_to_org organization_id,
        ue.work_terminal,
        ue.job_position,
        ue.gender,
        ue.wx_openid,
        ue.id_card,
        uo.name_full_path,
        uo.path,
        ue.job_number,
        ue.phone,
        ue.email_address,
        ue.employee_code,
        ue.info_is_show
        FROM uc_user uu
        JOIN uc_employee ue on
            uu.employee_id = ue.id
        join uc_employee_wechat uew on
            uu.employee_id = uew.employee_id
        LEFT JOIN uc_organization uo on
            ue.belong_to_org = uo.id
        WHERE
        uu.deleted = 0 and (ue.deleted = 0 or ue.deleted is null)
        and
            uew.client_id = #{clientId}
          and
            uew.openid = #{openid}
    </select>

    <select id="choosePage" resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvUserEmployeeVO">
        <include refid="joinSelect"></include>
        <where>
            uu.deleted = 0 and (ue.deleted = 0 or ue.deleted is null) and uo.id is not null
            <if test="dto.employeeOrganizationId != null and dto.employeeOrganizationId.size() > 0">
                and uu.id in(
                select wo.user_id from uc_user_work_for_organization wo
                join(
                <!--include统一参数名parent-->
                <bind name="parent" value="dto.employeeOrganizationId" />
                <include refid="com.swcares.common.uc.mapper.OrganizationMapper.recursive_children"></include>
                ) children2 on wo.organization_id=children2.id
                )
            </if>
            <if test="dto.manageOrganizationId != null and dto.manageOrganizationId.size() > 0">
                AND uu.id in(
                select wo.user_id from uc_user_work_for_organization wo where wo.organization_id in
                <foreach collection="dto.manageOrganizationId" item="orgId" index="index" separator="," open="("
                         close=")">
                    #{orgId}
                </foreach>
                )
            </if>
            <if test="dto.type != null">
                AND uu.type = #{dto.type}
            </if>
            <if test="dto.typeNot != null">
                AND uu.type not in
                <foreach collection="dto.typeNot" item="item" index="index" separator="," open="("
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.status != null">
                AND uu.status = #{dto.status}
            </if>
            <if test="dto.name != null and dto.name != ''">
                AND (uu.name LIKE #{dto.name} OR ue.name LIKE #{dto.name} OR ue.job_number LIKE #{dto.name} OR ue.email_address LIKE #{dto.name} OR ue.phone = #{dto.phone})
            </if>
            <if test="dto.createTimeStart != null and dto.createTimeEnd != null">
                AND uu.created_time BETWEEN #{dto.createTimeStart} AND #{dto.createTimeEnd}
            </if>
        </where>
        ORDER BY uu.id DESC
    </select>

    <select id="getNameListStartWith" resultType="java.lang.String">
        select u.name
        from uc_user u
        where u.name like #{likeName}
    </select>
    <select id="checkManageOrg" resultType="java.lang.Integer">
        SELECT
        count(*) cnt
        FROM
        uc_user u
        LEFT JOIN uc_user_manage_organization m ON u.id = m.user_id
        LEFT JOIN uc_organization o ON o.id = m.organization_id
        WHERE
        u.deleted = 0
        AND u.type = 4
        AND ( o.deleted = 0 OR o.deleted IS NULL )
        AND o.id in
        <foreach collection="path" item="orgId" index="index" separator="," open="("
                 close=")">
            #{orgId}
        </foreach>
    </select>
    <select id="pageCa" resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvUserVO">
        SELECT
        uu.*,
        uo.id organization_id,
        uo.name organization_name,
        uo.name_full_path,
        uo.path,
        uo.type organization_type
        FROM
        uc_user uu
        LEFT JOIN uc_user_manage_organization um ON uu.id = um.user_id
        LEFT JOIN uc_organization uo ON um.organization_id = uo.id
        <where>
            uu.deleted = 0 and uu.type = 4
            <if test="dto.manageId != null and dto.manageId.size() > 0">
                AND uo.id in (
                <!--include统一参数名parent-->
                <bind name="parent" value="dto.manageId" />
                <include refid="com.swcares.common.uc.mapper.OrganizationMapper.recursive_children"></include>
                )
            </if>
            <if test="dto.type != null">
                AND uo.type = #{dto.type}
            </if>
            <if test="dto.status != null">
                AND uu.status = #{dto.status}
            </if>
            <if test="dto.name != null and dto.name != ''">
                AND uu.name LIKE #{dto.name}
            </if>
        </where>
        ORDER BY uu.id DESC
    </select>
    <select id="existName" resultType="java.lang.Boolean">
        select count(*) from
        (
        select id from uc_user u where u.name = #{name} and u.deleted = 0
        <if test="employee!=null">
            and u.employee_id != #{employee}
        </if>
        union all
        select id from uc_employee e where e.deleted = 0
        and (
        e.job_number = #{name} or e.email_address = #{name}
        <if test="phone!=null and phone != ''">
            or e.phone = #{phone}
        </if>
        )
        <if test="employee!=null">
            and e.id != #{employee}
        </if>
        ) a
    </select>
    <select id="getByJobPositionCode" resultType="com.swcares.aps.usercenter.model.uc.vo.ReaptvUserVO">
        <include refid="joinSelect"></include>
        where
        uu.deleted = 0
        and ue.deleted = 0
        and (uo.deleted is null OR uo.deleted != 1)
        and uu.status = 1
        and
        <foreach collection="codeList" item="code" open="(" separator="or" close=")">
            ue.job_position like #{code}
        </foreach>
    </select>

    <select id="getBriefInfoById" resultType="com.swcares.aps.usercenter.vo.UserBriefInfoVO">
        select u.id user_id, u.name user_name, emp.id employee_id, emp.name employee_name, emp.job_number
        from uc_user u
        left join uc_employee emp on u.EMPLOYEE_ID = emp.ID
        where u.id = #{id}
    </select>

    <update id="cleanLockedStatusOfAllUser" databaseId="oracle">
        update uc_user set status=1,updated_by='system',updated_time=SYSDATE where status = 2
    </update>
</mapper>
