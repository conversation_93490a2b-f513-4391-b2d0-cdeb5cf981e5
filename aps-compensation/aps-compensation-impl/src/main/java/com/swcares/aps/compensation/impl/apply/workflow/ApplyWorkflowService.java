package com.swcares.aps.compensation.impl.apply.workflow;


import com.swcares.aps.compensation.model.apply.dto.ApplyAuditDTO;
import com.swcares.aps.component.workflow.dto.NodeNoticeDTO;

/**
 * ClassName：ApplyWorkflowService <br>
 * Description：申领单工作流接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/1/17 <br>
 * @version v1.0 <br>
 */
public interface ApplyWorkflowService {

    /**
     * @title startWorkflow
     * @description 发起审核流程
     * <AUTHOR> @date 2022/2/22 15:58
     * @param applyOrderId
     * @param operatorUserId
     * @return void
     */
    void startWorkflow(String applyOrderId,String operatorUserId) throws Exception ;


    /**
     * @title submitterWorkflow
     * @description 提交审核流程
     * <AUTHOR>
     * @date 2022/2/22 15:57
     * @param noticeDTO
     * @return void
     */
    void submitterWorkflow(NodeNoticeDTO noticeDTO) throws Exception;

    /** 
     * @title auditWorkflowProcess
     * @description 节点扭转操作
     * <AUTHOR> @date 2022/2/22 15:53
     * @param applyAudit
     * @return void
     */
    void auditWorkflowProcess(ApplyAuditDTO applyAudit) throws Exception ;

    /**
     * @title automaticWorkflowProcess
     * @description 自动审核节点通知处理
     * <AUTHOR> @date 2022/2/22 15:56
     * @param noticeDTO
     * @return void
     */
    void automaticWorkflowProcess(NodeNoticeDTO noticeDTO) throws Exception;

    /**
     * @title endWorkflowProcess
     * @description 结束节点通知处理
     * <AUTHOR> @date 2022/2/22 15:57
     * @param noticeDTO
     * @return void
     */
    void endWorkflowProcess(NodeNoticeDTO noticeDTO)throws Exception;
}
