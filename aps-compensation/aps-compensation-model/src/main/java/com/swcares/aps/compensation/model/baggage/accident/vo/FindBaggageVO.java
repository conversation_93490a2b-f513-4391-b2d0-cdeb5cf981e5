package com.swcares.aps.compensation.model.baggage.accident.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: FindBaggageVO
 * @projectName aps
 * @description: 箱包事故单信息列表VO
 * @date 2022/3/4 9:30
 */
@Data
@ApiModel(value="异常行李事故单信息列表VO", description="异常行李事故单信息列表VO")
public class FindBaggageVO {
    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "事故单状态(0草稿,1待处理,2处理中,3已结案,4作废)")
    private String accidentStatus;

    @ApiModelProperty(value = "异常行李类型(21破损,22少收,23多收,24内件缺少,25丢失)")
    private String type;

    @ApiModelProperty(value = "航班号")
    private String paxFlightNo;

    @ApiModelProperty(value = "航班日期")
    private String paxFlightDate;

    @ApiModelProperty(value = "航段")
    private String paxSegment;

    @ApiModelProperty(value = "补偿单数量")  //自定义的
    private String compensationNumber;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "行李号")
    private String baggageNo;

    @ApiModelProperty(value = "服务航站")
    private String serveSegment;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;


}
