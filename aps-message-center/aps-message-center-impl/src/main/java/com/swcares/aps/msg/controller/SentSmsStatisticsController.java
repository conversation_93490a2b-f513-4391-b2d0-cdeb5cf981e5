package com.swcares.aps.msg.controller;

import com.swcares.aps.msg.model.dto.SentSmsStatisticsSearchDTO;
import com.swcares.aps.msg.model.vo.SentSmsStatisticsResultVO;
import com.swcares.aps.msg.service.SentSmsReportService;
import com.swcares.baseframe.common.base.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @ClassName：SmsPersonTimeStatisticsController
 * @Description：已发短信统计模块
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/11/25 10:24
 * @version： v1.0
 */
@RestController
@RequestMapping("/sms/sent/report")
@Api(tags = "短信统计报表接口")
public class SentSmsStatisticsController {

    @Autowired
    SentSmsReportService sentSmsReportService;

    //列表查询
    @PostMapping("/statics")
    @ApiOperation("已发短信统计")
    public BaseResult<SentSmsStatisticsResultVO> getSentSmsStatistics(@RequestBody SentSmsStatisticsSearchDTO searchDTO){
        SentSmsStatisticsResultVO sentSmsStatistics = sentSmsReportService.getSentSmsStatistics(searchDTO);
        return BaseResult.ok(sentSmsStatistics);
    }

    //按照查询条件下载
    @PostMapping("/download")
    @ApiOperation("根据查询条件下载指定的旅客短信发送记录")
    public void sentDownload(@RequestBody SentSmsStatisticsSearchDTO searchDto, HttpServletRequest request, HttpServletResponse response){
        sentSmsReportService.downLoad(searchDto, request, response);
    }
}
