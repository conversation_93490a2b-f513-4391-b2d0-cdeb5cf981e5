package com.swcares.aps.web.bff.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.component.drools.impl.service.DroolsRuleService;
import com.swcares.aps.component.drools.model.dto.DroolsRuleDTO;
import com.swcares.aps.component.drools.model.dto.DroolsRuleDeleteDTO;
import com.swcares.aps.component.drools.model.dto.DroolsRulePageDTO;
import com.swcares.aps.component.drools.model.entity.DroolsRuleDO;
import com.swcares.aps.component.drools.model.vo.DroolsRuleVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ClassName：com.swcares.aps.component.bff.controller.DroolsRuleController <br>
 * Description：TODO <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021/11/9 15:40 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/droolsRule")
@Api(tags = "规则引擎接口")
@ApiVersion(value = "公共组件 v1.0")
public class DroolsRuleBffController extends BaseController {

    @Autowired
    private DroolsRuleService droolsRuleService;

    @PostMapping("/save")
    @ApiOperation(value = "新建规则")
    public BaseResult<Object> save(@RequestBody DroolsRuleDTO dto) {
        DroolsRuleDO droolsRuleDO = ObjectUtils.copyBean(dto, DroolsRuleDO.class);
        boolean created = droolsRuleService.save(droolsRuleDO);
        if (!created) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        return ok();
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "通过ID删除规则")
    public BaseResult<Object> delete(@RequestBody @ApiParam(value = "主键id", required = true) DroolsRuleDeleteDTO dto) {
        boolean deleted = droolsRuleService.logicRemoveById(dto.getId());
        if (!deleted) {
            throw new BusinessException(CommonErrors.DELETE_ERROR);
        }
        return ok();
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改规则")
    public BaseResult<Object> update(@RequestBody DroolsRuleDTO dto) {
        DroolsRuleDO droolsRuleDO = ObjectUtils.copyBean(dto, DroolsRuleDO.class);
        boolean updated = droolsRuleService.updateById(droolsRuleDO);
        if (!updated) {
            throw new BusinessException(CommonErrors.UPDATE_ERROR);
        }
        return ok();
    }

    @GetMapping("/get")
    @ApiOperation(value = "通过ID查询规则")
    public BaseResult<DroolsRuleVO> get(@ApiParam(value = "主键id", required = true) Long id) {
        DroolsRuleVO droolsRuleVO = ObjectUtils.copyBean(droolsRuleService.getById(id), DroolsRuleVO.class);
        return ok(droolsRuleVO);
    }

    @GetMapping("/page")
    @ApiOperation(value = "条件分页查询规则")
    public PagedResult<List<DroolsRuleVO>> page(DroolsRulePageDTO dto) {
        IPage<DroolsRuleVO> result = droolsRuleService.page(dto);
        return ok(result);
    }

}
