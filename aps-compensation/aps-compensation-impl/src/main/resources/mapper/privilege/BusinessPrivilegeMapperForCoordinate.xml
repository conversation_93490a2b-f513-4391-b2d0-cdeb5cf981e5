<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.privilege.mapper.BusinessPrivilegeMapperForCoordinate">
    <sql id="businessPrivilegeBaseSelect">
        select bp.id,
               bp.grantor_code,
               bp.grantor_category,
               bp.grantor_name,
               bp.recipient_code,
               bp.recipient_category,
               bp.recipient_name,
               bp.remark,
               bp.disabled,
               bp.updater_job_number,
               bp.updater_employee_name,
               bp.created_time,
               bp.created_by,
              bp.updated_time,
              bp.updated_by
        from business_privilege bp
    </sql>
    <resultMap id="businessPrivilegeResultMap" type="com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilege">
        <id column="id" property="id"/>
        <result column="grantor_code" property="grantorCode"/>
        <result column="grantor_category" property="grantorCategory"/>
        <result column="grantor_name" property="grantorName"/>
        <result column="recipient_code" property="recipientCode"/>
        <result column="recipient_category" property="recipientCategory"/>
        <result column="recipient_name" property="recipientName"/>
        <result column="remark" property="remark"/>
        <result column="disabled" property="disabled"/>
        <result column="updater_job_number" property="updaterJobNumber"/>
        <result column="updater_employee_name" property="updaterEmployeeName"/>
        <result column="created_time" property="createdTime"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="updated_by" property="updatedBy"/>
    </resultMap>

    <select id="getTeanantIdByTenantCode" resultType="java.lang.Long">
        select id from sys_tenant where tenant_code = #{tenantCode}
    </select>
    <select id="getBusinessPrivilegeBetweenCustomer"
            resultMap="businessPrivilegeResultMap">
        <include refid="businessPrivilegeBaseSelect"/>
        <where>
                 bp.grantor_code = #{grantorCode}
                and bp.recipient_code = #{recipientCode}
        </where>
    </select>
</mapper>