package com.swcares.aps.compensation.impl.apply.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.apply.dto.ApplyAuditDTO;
import com.swcares.aps.compensation.model.apply.dto.ApplyPaxBaseInfoDTO;
import com.swcares.aps.compensation.model.apply.entity.ApplyAuditDO;
import com.swcares.aps.compensation.model.apply.entity.ApplyOrderDO;

import java.util.List;

/**
 * ClassName：com.swcares.compensation.service.ApplyAuditRecordService <br>
 * Description：航延补偿申领单审核信息表 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-17 <br>
 * @version v1.0 <br>
 */
public interface ApplyAuditRecordService extends IService<ApplyAuditDO> {


    List<ApplyAuditDO> findByApplyId(String applyId);

    /**
     * 创建审核信息表
     * @param applyOrder
     * @param applyPaxBaseInfoDTOS
     * @param operatorUserId
     */
    void createAuditRecords(ApplyOrderDO applyOrder, List<ApplyPaxBaseInfoDTO> applyPaxBaseInfoDTOS, String operatorUserId);

    /**
     * 审核操作
     * @param applyAudit
     */
    void processOperatorAudit(ApplyAuditDTO applyAudit);

    /**
     * 自动审核节点操作
     * @param applyId
     */
    void processAutomaticAudit(String applyId);
}
