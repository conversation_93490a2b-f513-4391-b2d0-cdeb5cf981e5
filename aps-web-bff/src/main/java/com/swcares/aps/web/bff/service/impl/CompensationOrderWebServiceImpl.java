package com.swcares.aps.web.bff.service.impl;

import com.swcares.aps.compensation.model.irregularflight.vo.CompensationFromDetailsVO;
import com.swcares.aps.compensation.remote.api.irregularflight.AccidentInfoApi;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.aps.web.bff.service.CompensationOrderWebService;
import com.swcares.baseframe.common.base.BaseResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * ClassName：com.swcares.aps.compensation.bff.web.service.impl <br>
 * Description： <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 10月28日 17:18 <br>
 * @version v1.0 <br>
 */
@Service
public class CompensationOrderWebServiceImpl implements CompensationOrderWebService {

    private final CompensationInfoApi compensationInfoApi;
    private final AccidentInfoApi accidentInfoApi;

    private final Map<String, Function<Long, BaseResult>> ACCIDENT_TYPE_MAP = new HashMap<>();

    @Autowired
    public CompensationOrderWebServiceImpl(AccidentInfoApi accidentInfoApi, CompensationInfoApi compensationInfoApi){
        this.compensationInfoApi = compensationInfoApi;
        this.accidentInfoApi = accidentInfoApi;
        ACCIDENT_TYPE_MAP.put("1",accidentInfoApi::getFlightAccidentInfoDetails);
        ACCIDENT_TYPE_MAP.put("2",accidentInfoApi::getBaggageAccidentInfo);
        ACCIDENT_TYPE_MAP.put("3",accidentInfoApi::getOverBookDetail);
        ACCIDENT_TYPE_MAP.put("4",accidentInfoApi::getComplaintAccidentDetail);
    }

    @Override
    public Map<String, Object> getCompensationDetails(Long orderId, Long accidentId,String orderNo) {


        Map<String, Object> dataMap = new HashMap<>();
        //事故单详情vo
        dataMap.put("flightAccidentInfoDetails",compensationInfoApi.findById(accidentId).getData());
        //补偿单信息
        dataMap.put("compensationOrderInfo",compensationInfoApi.findOrderDetails(orderId).getData());
        //审核进度,不是草稿的可看
        dataMap.put("auditProgressInfo",compensationInfoApi.findAuditRecord(orderId,orderNo).getData());

        dataMap.put("compensationStandardList",compensationInfoApi.findOrderRule(orderId).getData());
        //补偿旅客列表
       // dataMap.put("compensationChoicePaxList",compensationInfoApi.findChoicePax(orderId));

        return dataMap;
    }

    @Override
    public CompensationFromDetailsVO getCompensationDetailsVo(Long orderId, Long accidentId, String orderNo,String accidentType) {
        CompensationFromDetailsVO compensationFromDetailsVO = new CompensationFromDetailsVO();
        // get Flight accident Info
        compensationFromDetailsVO.setAccidentInfoDetails(ACCIDENT_TYPE_MAP.get(accidentType).apply(accidentId).getData());
        // get Compensation order Info
        compensationFromDetailsVO.setCompensationOrderInfo(compensationInfoApi.detail(orderId).getData());
        // get Audit Progress
        compensationFromDetailsVO.setAuditProgressInfo(compensationInfoApi.findAuditRecord(orderId,orderNo).getData());
        // get Compensation Standard
        compensationFromDetailsVO.setCompensationStandardList(compensationInfoApi.findOrderRule(orderId).getData());
        return compensationFromDetailsVO;
    }

    @Override
    public Map<String, Object> getOrderEditEchoDetails(Long orderId, Long accidentId) {

        Map<String, Object> dataMap = new HashMap<>();
        //事故单详情vo
        dataMap.put("flightAccidentInfoDetails",compensationInfoApi.findById(accidentId).getData());
        //补偿单信息
        dataMap.put("compensationOrderInfo",compensationInfoApi.getOrderEditEchoById(orderId).getData());
        //已选旅客数据
        dataMap.put("compensationChoicePaxList",compensationInfoApi.findSelectedPax(orderId).getData());
        //补偿标准
        dataMap.put("compensationStandardList",compensationInfoApi.findOrderRule(orderId).getData());

        return dataMap;
    }
}