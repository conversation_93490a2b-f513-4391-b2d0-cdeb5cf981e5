/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：PassengerSmsRecordService <br>
 * Package：com.swcares.reaptv.msg.service <br> 
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.msg.model.dto.MessageStatusStaticsDTO;
import com.swcares.aps.msg.model.dto.PassengerSmsSearchDTO;
import com.swcares.aps.msg.model.dto.PassengerSmsSendDTO;
import com.swcares.aps.msg.model.entity.PassengerSmsRecord;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * InterfaceName：com.swcares.reaptv.msg.service.PassengerSmsRecordService <br>
 * Description：对旅客发送的短消息的发送记录的服务接口
 * <AUTHOR> <br>
 * date 2022/9/20 10:04 <br>
 * @version v1.0.0 <br>
 */
public interface PassengerSmsRecordService extends IService<PassengerSmsRecord> {
    IPage<PassengerSmsRecord> getPassengerSmsRecordPage(PassengerSmsSearchDTO dto);

    List<PassengerSmsRecord> getByIds(List<Long> ids);

    void download(PassengerSmsSearchDTO dto, HttpServletRequest request, HttpServletResponse response);

    void downLoad(List<Long> ids, HttpServletRequest request, HttpServletResponse response);

    public PassengerSmsRecord sendSms(PassengerSmsSendDTO dto);

    public PassengerSmsRecord sendSms(PassengerSmsSendDTO dto, String sendPhone);

    List<MessageStatusStaticsDTO> getMessagesStatusStatics(PassengerSmsSearchDTO dto);
}
