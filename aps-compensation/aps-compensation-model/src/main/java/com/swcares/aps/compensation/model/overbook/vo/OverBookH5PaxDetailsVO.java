package com.swcares.aps.compensation.model.overbook.vo;

import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：OverBookH5PaxDetailsVO
 * @Description：H5超售详情-旅客信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/27 15:58
 * @version： v1.0
 */
@SecretInfoEntity
@Data
@ApiModel(value="旅客信息VO对象", description="旅客信息VO对象")
public class OverBookH5PaxDetailsVO {

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "证件类型")
    private String idType;

    @ApiModelProperty(value = "证件号")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "手机号码")
    private String telephone;
}
