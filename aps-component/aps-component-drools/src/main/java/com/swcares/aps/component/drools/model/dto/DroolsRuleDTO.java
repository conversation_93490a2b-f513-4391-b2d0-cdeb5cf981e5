package com.swcares.aps.component.drools.model.dto;

import com.swcares.aps.component.drools.model.entity.DroolsRuleDO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.component.drools.model.dto.DroolsRuleDTO <br>
 * Description：规则引擎管理DTO <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021/10/20 10:33 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="规则引擎管理DTO", description="")
public class DroolsRuleDTO  extends DroolsRuleDO{

    private static final long serialVersionUID = 1L;
}
