package com.swcares.aps.compensation.model.irregularflight.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: CompensationOrderInfoExamineVO
 * @projectName aps
 * @description: 带审核操作的赔偿单详情
 * @date 2021/11/24 11:08
 */
@Data
@ApiModel(value="CompensationOrderInfoExamineVO对象", description="带审核的赔偿单信息")
public class CompensationOrderInfoExamineVO extends CompensationOrderInfoVO{

    @ApiModelProperty(value = "是否可以进行审核，Y是 N否")
    private String toExamine;

    @ApiModelProperty(value = "审核节点id")
    private String taskId;

    @ApiModelProperty(value = "系统账户id")
    private String userId;
}
