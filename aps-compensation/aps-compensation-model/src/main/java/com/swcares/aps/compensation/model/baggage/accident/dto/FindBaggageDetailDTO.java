package com.swcares.aps.compensation.model.baggage.accident.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @title: FindBaggageDetailDTO
 * @projectName aps
 * @description: 异常行李事故单详情DTO
 * @date 2022/3/7 15:48
 */
@Data
@ApiModel(value="异常行李事故单详情封装DTO", description="异常行李事故单详情封装DTO")
public class FindBaggageDetailDTO implements BaseDTO {
    @NotNull(message = "异常行李事故单id不能为空")
    @ApiModelProperty(value = "异常行李事故单id")
    private String accidentId;

    @ApiModelProperty(value = "关联的少收或多收的事故单号集 前端不用传 后端处理数据使用")
    private String[] accidentNos;
}
