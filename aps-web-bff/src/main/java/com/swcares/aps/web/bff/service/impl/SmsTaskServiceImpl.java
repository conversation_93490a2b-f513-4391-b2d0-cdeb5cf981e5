package com.swcares.aps.web.bff.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.collection.CollUtil;
import com.swcares.aps.msg.api.SmsTaskApi;
import com.swcares.aps.msg.model.constant.MessageCenterErrors;
import com.swcares.aps.msg.model.dto.*;
import com.swcares.aps.msg.model.entity.SmsTaskImportReciverInfo;
import com.swcares.aps.msg.model.vo.SmsTaskSendDetailVO;
import com.swcares.aps.web.bff.service.SmsTaskService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * All rights Reserved, Designed By <a href="http://xnky.travelsky.net/">西南凯亚</a>
 * ClassName：com.swcares.reaptv.msg.bff.service.impl.SmsTaskServiceImpl
 * Description：(用一句话描述这个类或者接口表示什么)
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2022-12-1 10:47
 */
@Service
@Slf4j
public class SmsTaskServiceImpl implements SmsTaskService {

    @Autowired
    SmsTaskApi smsTaskApi;

    private static final String DEFAULT_ENCODER_PARAM = "UTF-8";

    @Override
    public void getTemplate(String type, HttpServletResponse response){
        String templateDir = "templates/";
        String templateFileName=null;
        if ("0".equals(type)) {
            templateFileName = "PNR导入模板.xls";
        } else if ("1".equals(type)) {
            templateFileName = "票号导入模板.xls";
        } else if ("2".equals(type)) {
            templateFileName = "手机号导入模板.xls";
        } else {
            throw new BusinessException(CommonErrors.DOWNLOAD_ERROR);
        }

        ClassPathResource resource = new ClassPathResource(templateDir + templateFileName);
        if (!resource.exists()){
            throw new BusinessException(CommonErrors.DOWNLOAD_ERROR);
        }

        // 以流的形式下载文件。
        try{
            InputStream fis = new BufferedInputStream(resource.getInputStream());
            int totalLength = 0;
            // 清空response
            response.reset();
            // 设置response的Header
            String fileName = URLEncoder.encode(templateFileName, DEFAULT_ENCODER_PARAM);
            response.setCharacterEncoding(DEFAULT_ENCODER_PARAM);

            response.setHeader("content-Type", "application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName );

            OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            int length = 0;
            do{
                byte[] buffer = new byte[1024];
                length = fis.read(buffer);
                toClient.write(buffer);
                totalLength = totalLength + length;
            } while(length > 0);
            fis.close();
            response.addHeader("Content-Length", "" + totalLength);
            toClient.flush();
            toClient.close();
        } catch (IOException ex) {
            ex.printStackTrace();
            throw new BusinessException(CommonErrors.DOWNLOAD_ERROR);
        }

    }

    @Override
    public void downloadFail(String id, HttpServletResponse response) {
        BaseResult<List<SmsTaskImportReciverInfo>> result = smsTaskApi.downloadFail(id);
        List<SmsTaskImportReciverInfo> data = result.getData();
        String type = data.get(0).getType();

        List<Object> list = new ArrayList<>();
        String fileName = null;
        Class c = null;
        if ("4".equals(type)) {
            data.forEach(info ->{
                SmsTaskPnrForm form = new SmsTaskPnrForm();
                form.setPnr(info.getPnr());
                form.setFlightDate(info.getFlightDate());
                form.setFlightNo(info.getFlightNo());
                form.setFlightSegment(info.getFlightSegment());
                list.add(form);
            });
            fileName = "PNR导入失败信息";
            c = SmsTaskPnrForm.class;
        } else if ("3".equals(type)) {
            data.forEach(info ->{
                SmsTaskTktForm form = new SmsTaskTktForm();
                form.setTktNo(info.getTktNo());
                form.setFlightDate(info.getFlightDate());
                form.setFlightNo(info.getFlightNo());
                form.setFlightSegment(info.getFlightSegment());
                list.add(form);
            });
            fileName = "票号导入失败信息";
            c = SmsTaskTktForm.class;
        } else if ("2".equals(type)) {
            data.forEach(info ->{
                SmsTaskMobileForm form = new SmsTaskMobileForm();
                form.setMobile(info.getMobile());
                form.setFlightDate(info.getFlightDate());
                form.setFlightNo(info.getFlightNo());
                form.setFlightSegment(info.getFlightSegment());
                list.add(form);
            });
            fileName = "手机导入失败信息";
            c = SmsTaskMobileForm.class;
        } else {
            throw new BusinessException(CommonErrors.DOWNLOAD_ERROR);
        }
        createTamplate(c, list, fileName, response);
    }

    @Override
    public void getSmsTaskSendDetailExport(SmsTaskSendDetailDTO dto, HttpServletResponse response) {
        BaseResult<List<SmsTaskSendDetailVO>> result = smsTaskApi.getSmsTaskSendDetailExport(dto);
        List<SmsTaskSendDetailVO> data = result.getData();
        exportSmsTaskSendDetails(response, data);
    }

    private void exportSmsTaskSendDetails(HttpServletResponse response, List<SmsTaskSendDetailVO> data) {
        List list = new ArrayList<>();
        if (CollUtil.isNotEmpty(data)){
            String viewMode = data.get(0).getViewMode();

            String fileName = "短信任务"+data.get(0).getTaskId() +"发送详情导出";
            Class c = null;
            if ("0".equals(viewMode)) {
                    c = SmsTaskDetailPnrForm.class;
                data.forEach(item -> {
                    SmsTaskDetailPnrForm smsTaskDetailPnrForm = new SmsTaskDetailPnrForm();
                    smsTaskDetailPnrForm.setPnr(item.getPNR());
                    smsTaskDetailPnrForm.setFlightNo(item.getFlightNumber());
                    smsTaskDetailPnrForm.setFlightDate(item.getFlightDate());
                    smsTaskDetailPnrForm.setFlightSegment(item.getFlightSegment());
                    smsTaskDetailPnrForm.setSendUser(item.getSendUser());
                    smsTaskDetailPnrForm.setFinishTime(item.getFinishTime());
                    //发送状态  0-部分失败 1-发送成功 2-发送失败  3-待发送  4-发送中   5-不予发送
                    switch (item.getStatus()) {
                        case "0":
                            smsTaskDetailPnrForm.setStatus("部分失败");
                            break;
                        case "1":
                            smsTaskDetailPnrForm.setStatus("发送成功");
                            break;
                        case "2":
                            smsTaskDetailPnrForm.setStatus("发送失败");
                            break;
                        case "3":
                            smsTaskDetailPnrForm.setStatus("待发送");
                            break;
                        case "4":
                            smsTaskDetailPnrForm.setStatus("发送中");
                            break;
                        case "5":
                            smsTaskDetailPnrForm.setStatus("不予发送");
                            break;
                    }
                    list.add(smsTaskDetailPnrForm);
                });
            } else {
                    c = SmsTaskDetailObjectForm.class;
                data.forEach(item -> {
                    SmsTaskDetailObjectForm smsTaskDetailObjectForm = new SmsTaskDetailObjectForm();
                    smsTaskDetailObjectForm.setPhoneNumber(item.getPhoneNumber());
                    smsTaskDetailObjectForm.setTicketNumber(item.getTicketNumber());
                    smsTaskDetailObjectForm.setFlightNo(item.getFlightNumber());
                    smsTaskDetailObjectForm.setFlightDate(item.getFlightDate());
                    smsTaskDetailObjectForm.setFlightSegment(item.getFlightSegment());
                    smsTaskDetailObjectForm.setSendUser(item.getSendUser());
                    smsTaskDetailObjectForm.setFinishTime(item.getFinishTime());
                    //发送状态  0-部分失败 1-发送成功 2-发送失败  3-待发送  4-发送中   5-不予发送
                    switch (item.getStatus()) {
                        case "0":
                            smsTaskDetailObjectForm.setStatus("部分失败");
                            break;
                        case "1":
                            smsTaskDetailObjectForm.setStatus("发送成功");
                            break;
                        case "2":
                            smsTaskDetailObjectForm.setStatus("发送失败");
                            break;
                        case "3":
                            smsTaskDetailObjectForm.setStatus("待发送");
                            break;
                        case "4":
                            smsTaskDetailObjectForm.setStatus("发送中");
                            break;
                        case "5":
                            smsTaskDetailObjectForm.setStatus("不予发送");
                            break;
                    }
                    list.add(smsTaskDetailObjectForm);
                });
            }
            createTamplate(c, list, fileName, response);
        } else {

        }

    }


    public void exportSelectedSmsTaskSendDetails(SmsTaskSendDetailExportDTO dto, HttpServletResponse response){
        //前端传回的选中的id中有使用逗号分隔的复合的id的形式的值，需要把这些值拆分为单个的id
        dto.setSelectedIds(getAllSelectedIds(dto.getSelectedIds()));
        BaseResult<List<SmsTaskSendDetailVO>> result = smsTaskApi.exportSelectedSmsTaskSendDetails(dto);
        List<SmsTaskSendDetailVO> data = result.getData();
        exportSmsTaskSendDetails(response, data);
    }

    private List<String> getAllSelectedIds(List<String> originalSelectedIds){
        if (originalSelectedIds == null || originalSelectedIds.isEmpty()){
            return null;
        }

        List<String> allIds = new ArrayList<>();
        for(String selectedId : originalSelectedIds){
            String[] singleIds = selectedId.split(",");
            CollUtil.addAll(allIds, singleIds);
        }

        return allIds;
    }
    private void createTamplate(Class clazz, List dataList, String fileName, HttpServletResponse response) {
        try{
            ExportParams exportParams = new ExportParams(null,"sheet1");
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, clazz,  dataList);
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName + DateUtils.getDate(DateUtils.DEF_PTN_YMD) + ".xlsx", "UTF-8"));
            workbook.write(response.getOutputStream());
        } catch (IOException e){
            log.error("{}导出失败", fileName, e);
            throw new BusinessException(MessageCenterErrors.DOWNLOAD_ERROR);
        }
    }
}

