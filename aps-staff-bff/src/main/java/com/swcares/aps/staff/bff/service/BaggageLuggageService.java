package com.swcares.aps.staff.bff.service;

import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageInfoPageDTO;
import com.swcares.aps.staff.bff.vo.StaffBaggageLuggageVO;
import com.swcares.baseframe.common.base.PagedResult;

import java.util.List;

/**
 * ClassName：BaggageLuggageService <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/3 <br>
 * @version v1.0 <br>
 */
public interface BaggageLuggageService {
    PagedResult<List<StaffBaggageLuggageVO>> page(LuggageInfoPageDTO dto);
}
