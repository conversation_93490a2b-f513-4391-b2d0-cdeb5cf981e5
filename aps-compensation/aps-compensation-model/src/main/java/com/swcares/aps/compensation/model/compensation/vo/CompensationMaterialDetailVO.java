package com.swcares.aps.compensation.model.compensation.vo;

import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CompensationMaterialDetailVO
 * @Description：箱包补偿单详情信息VO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 唐康
 * @Date： 2022/4/14 14:55
 * @version： v1.0
 */
@Data
@SecretInfoEntity
@ApiModel(value="CompensationMaterialDetailVO对象", description="箱包补偿单详情信息VO")
public class CompensationMaterialDetailVO {
//--------------箱包补偿单详情下分段展示的补偿单信息---------------
    @ApiModelProperty(value = "补偿单id")
    private String compensationId;

    @ApiModelProperty(value = "补偿总金额")
    private String sumMoney;

    @ApiModelProperty(value = "补偿单号")
    private String orderNo;

    @ApiModelProperty(value = "补偿单状态 0草稿,1审核中,2审核不通过,3驳回,4审核通过,5生效,6关闭,7逾期")
    private String status;

    @ApiModelProperty(value = "服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "保障服务（0不存在，1存在）")
    private String ensureType;

    @ApiModelProperty(value = "补偿方式 1现金，2虚拟，3实物")
    private String compensateType;

    @ApiModelProperty(value = "补偿标准类型：1标准补偿金额 2自定义")
    private String compensateStandard;

    @ApiModelProperty(value = "补偿子方式(补偿方式 1微信，2银联，3数字人民币)")
    private String compensateSubType;

    @ApiModelProperty(value = "物品id")
    private String commodityId;

    @ApiModelProperty(value = "事故单id")
    private String accidentId;

    @ApiModelProperty(value = "事故单单号")
    private String accidentNo;

    @ApiModelProperty(value = "补偿事故单子类型- 补偿子类型-1延误，2取消，3备降，4返航，5补班; 21破损,22少收,23多收,24内件缺失,25丢失")
    private String accidentSubType;

    @ApiModelProperty(value = "补偿事故单类型- 1.不正常航班 2异常行李 3超售")
    private String accidentType;

    @ApiModelProperty(value = "异常行李事故单类型（21破损,22少收,23多收,24内件缺少,25丢失）")
    private String type;

    @ApiModelProperty(value = "创建人id")
    private String createdBy;

    @ApiModelProperty(value = "旅客数据输入的来源 SYSTEM（系统查询）MANUAL_INPUT（人工输入），")
    private String inputSource;

//--------------箱包补偿单详情下分段展示的航班信息----------------
    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "航段")
    private String paxSegment;

    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    @ApiModelProperty(value = "预计起飞时间")
    private String etd;
//---------------箱包补偿单详情下分段展示的补偿旅客信息--------------
    @ApiModelProperty(value = "姓名")
    private String paxName;

    @ApiModelProperty(value = "证件类型")
    private String idType;

    @ApiModelProperty(value = "证件号码")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "手机号")
    @SecretValue
    private String phone;

    @ApiModelProperty(value = "行李号")
    private String baggageNo;

    @ApiModelProperty(value = "旅客ID")
    private String paxId;

    @ApiModelProperty(value = "领取状态(0未领取,1已领取,2处理中3已逾期)")
    private String receiveStatus;
//---------------箱包补偿单详情下分段展示的补偿实物与成本信息----------
    @ApiModelProperty(value = "箱包品牌")
    private String materialBrand;

    @ApiModelProperty(value = "箱包名称")
    private String materialName;

    @ApiModelProperty(value = "尺寸")
    private String materialSize;

    @ApiModelProperty(value = "单价")
    private String materialUnivalent;

    @ApiModelProperty(value = "数量")
    private String amount;

    @ApiModelProperty(value = "补偿原因")
    private String remark;

    @ApiModelProperty(value = "赔偿实物id")
    private String materialId;

    @ApiModelProperty(value = "剩余库存")
    private String stock;
}
