package com.swcares.aps.basic.data.remoteapi.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.basic.data.remoteapi.mapper.FltFlightRealInfoMapper;
import com.swcares.aps.basic.data.remoteapi.model.entity.FltFlightRealInfo;
import com.swcares.aps.basic.data.remoteapi.service.FltFlightRealInfoService;
import org.springframework.stereotype.Service;

/**
 * @ClassName：FltFlightRealInfoServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2025/3/20 13:27
 * @version： v1.0
 */
@Service
public class FltFlightRealInfoServiceImpl extends ServiceImpl<FltFlightRealInfoMapper, FltFlightRealInfo> implements FltFlightRealInfoService {
}
