package com.swcares.aps.compensation.model.irregularflight.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * ClassName：com.swcares.aps.compensating.mode.irregularflight.dto.CompensationCalDroolsRuleDTO <br>
 * Description：规则引擎计算传入的DTO <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021/11/11 10:33 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "CompensationFlightInfoDTO对象", description = "赔偿航班信息")
public class CompensationCalDroolsRuleDTO implements BaseDTO {

    @ApiModelProperty(value = "规则标识")
    private String code;

    @ApiModelProperty(value = "旅客集合")
    private List<CompensationPaxInfoDTO> listPaxDTO;

    @ApiModelProperty(value = "经济舱规则")
    private CompensationRuleRecordDTO eCompensationRuleRecordDTO;

    @ApiModelProperty(value = "公务舱规则")
    private CompensationRuleRecordDTO bCompensationRuleRecordDTO;

    @ApiModelProperty(value = "总计赔偿金额")
    private BigDecimal sumMoney = new BigDecimal(0.0);
}
