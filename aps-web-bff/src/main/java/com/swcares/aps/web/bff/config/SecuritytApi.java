/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：SecretApi <br>
 * Package：com.swcares.reaptv.msg.config <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.web.bff.config;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * ClassName：com.swcares.reaptv.msg.config.SecretApi <br>
 * Description：用在需要对参数进行解密的http请求接口上
 * @author: 王翼 <br>
 * @CreatedAt: 2022/12/1 10:05 <br>
 * @version:
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD,ElementType.PARAMETER})
public @interface SecuritytApi {
}
