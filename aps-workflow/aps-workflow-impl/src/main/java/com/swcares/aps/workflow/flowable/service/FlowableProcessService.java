package com.swcares.aps.workflow.flowable.service;

import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.dto.CompleteProcessParamsDTO;
import com.swcares.aps.workflow.dto.StartProcessParamsDTO;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;


/**
 * ClassName：FlowableProcessService <br>
 * Description：flowable流程service <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/2/10 <br>
 * @version v1.0 <br>
 */
public interface FlowableProcessService {

    /**
     * @param params
     * @return 返回流程实例
     */
    ProcessInstance startProcess(StartProcessParamsDTO params);

    /**
     * 完成task节点操作
     * @param params
     * @return 返回流程实例ID
     */
    String completeTask(CompleteProcessParamsDTO params);


    /**
     *
     * @param params
     * @return
     */
    HistoricProcessInstance historicProcessInstance(BaseQueryParamDTO params);

    /**
     *
     * @param processInstanceId
     * @return
     */
    HistoricProcessInstance historicProcessInstance(String processInstanceId);
}
