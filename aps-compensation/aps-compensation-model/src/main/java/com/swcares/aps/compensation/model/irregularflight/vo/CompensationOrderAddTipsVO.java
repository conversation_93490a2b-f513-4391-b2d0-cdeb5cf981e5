package com.swcares.aps.compensation.model.irregularflight.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @title: CompensationOrderAddTipsVO
 * @projectName aps
 * @description:
 * @date 2021/12/13 10:39
 */
@Data
@ApiModel(value="CompensationOrderAddTipsVO对象", description="新建赔偿单同类型提示")
public class CompensationOrderAddTipsVO {

    @ApiModelProperty(value = "赔偿单ID")
    private Long orderId;

    @ApiModelProperty(value = "事故单ID")
    private Long accidentId;

    @ApiModelProperty(value = "赔偿单号")
    private String orderNo;

    @ApiModelProperty(value = "赔偿单状态")
    private String status;

    @ApiModelProperty(value = "所选航段")
    private String choiceSegmentCh;

    @ApiModelProperty(value = "总金额")
    private String sumMoney;

    @ApiModelProperty(value = "总人数")
    private String sumPersons;

    @ApiModelProperty(value = "创建人")
    private String createdBy;
}
