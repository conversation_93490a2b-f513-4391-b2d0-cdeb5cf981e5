package com.swcares.aps.web.bff.controller;

import com.swcares.aps.compensation.model.apply.dto.ApplyAuditDTO;
import com.swcares.aps.compensation.model.apply.dto.SubstituteCollarPaxPageDTO;
import com.swcares.aps.compensation.model.apply.dto.SubstituteCollarUpdDTO;
import com.swcares.aps.compensation.model.apply.vo.SubstituteCollarPaxDetailsVO;
import com.swcares.aps.compensation.model.apply.vo.SubstituteCollarPaxPageVO;
import com.swcares.aps.compensation.remote.api.irregularflight.ApplyApi;
import com.swcares.aps.web.bff.service.ReplaceRuleService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.security.UserContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ClassName：com.swcares.aps.apply.impl.controller <br>
 * Description：WEB代领旅客模块 <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 01月13日 14:03 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/substituteCollar")
@Api(tags = "WEB代领旅客模块接口")
@ApiVersion(value = "WEB代领旅客模块接口 v1.0")
public class SubstituteCollarPaxInfoController extends BaseController {

    @Autowired
    private ApplyApi applyApi;

    @Autowired
    private ReplaceRuleService replaceRuleService;

    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询代领审核列表")
    public PagedResult<List<SubstituteCollarPaxPageVO>> page(@RequestBody SubstituteCollarPaxPageDTO dto) {
        return applyApi.webPage(dto);
    }


    @GetMapping("/find")
    @ApiOperation(value = "查代领旅客审核详情")
    public BaseResult<SubstituteCollarPaxDetailsVO> find(@ApiParam(value = "主键id", required = true) Long id) {
        return applyApi.find(id);
    }


    @PostMapping("/updQuickPay")
    @ApiOperation(value = "修改代领审核为快速支付")
    public BaseResult<Object> updQuickPay(@RequestBody SubstituteCollarUpdDTO substituteCollarUpdDTO) {
        applyApi.updQuickPay(substituteCollarUpdDTO);
        return ok();
    }

    @PostMapping("/audit")
    @ApiOperation(value = "审核代领人领取信息")
    public BaseResult<Object> audit(@RequestBody ApplyAuditDTO applyAuditDTO) {
        Long userId = UserContext.getUserId();
        applyAuditDTO.setAuditorUserId(String.valueOf(userId));
        return applyApi.applyOrderAudit(applyAuditDTO);
    }
}
