package com.swcares.aps.usercenter.remote.api.tenant;

import com.swcares.aps.cpe.coordinate.model.basicdata.dto.CustomerDTO;
import com.swcares.aps.usercenter.model.tenant.dto.TenantAdditionalInfoDTO;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.common.sys.dto.TenantDTO;
import com.swcares.common.sys.dto.TenantDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> <PERSON> Yi
 * @Classname TenantAPI
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/25 13:23
 * @Version 1.0
 */
@FeignClient(name= "aps-user-center", path= "/api/aps-user-center", contextId = "aps-tenant")
public interface TenantInfoApi {
    @GetMapping("/tenantAdditionalInfo/getItem")
    public BaseResult<TenantAdditionalInfoDTO> getItem(@RequestParam("item") String item);

    @GetMapping("/tenantAdditionalInfo/getAll")
    public BaseResult<List<TenantAdditionalInfoDTO>> getAllItems();

    @PostMapping("/coordinateTenant/saveOrUpdate")
    public BaseResult<Object> coordinateCustomer(@RequestBody CustomerDTO dto);

}
