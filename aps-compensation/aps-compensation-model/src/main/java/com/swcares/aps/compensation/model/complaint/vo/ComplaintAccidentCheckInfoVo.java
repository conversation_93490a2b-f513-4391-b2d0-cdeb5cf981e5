package com.swcares.aps.compensation.model.complaint.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ComplaintAccidentCheckInfoVo对象", description = "新增旅客投诉事故单校验展示重复保障单信息")
public class ComplaintAccidentCheckInfoVo {

    @ApiModelProperty(value = "主键id")
    private String id;

    @ApiModelProperty(value = "旅客投诉事故单单号")
    private String accidentId;

    @ApiModelProperty(value = "事故单状态")
    private String accidentStatus;

    @ApiModelProperty(value = "事故类型")
    private String accidentSubType;

    @ApiModelProperty(value = "航段")
    private String segment;

    @ApiModelProperty(value = "创建人")
    private String createdUser;
}
