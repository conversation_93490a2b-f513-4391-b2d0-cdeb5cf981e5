package com.swcares.aps.compensation.model.irregularflight.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * ClassName：com.swcares.compensation.entity.AuditInfo <br>
 * Description：赔偿单-待审核记录 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-11-24 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="AuditInfo对象", description="赔偿单-待审核记录")
@Builder
public class CompensationAuditInfoDTO  {

    @ApiModelProperty(value = "赔偿单id")
    @NotNull
    private Long orderId;

    @ApiModelProperty(value = "选择的审核人id数组")
    @NotNull
    private Long[] auditorIds;

    @ApiModelProperty(value = "审核节点id")
    @NotEmpty
    private String taskId;


}
