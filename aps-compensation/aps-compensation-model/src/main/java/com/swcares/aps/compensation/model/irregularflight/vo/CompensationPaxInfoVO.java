package com.swcares.aps.compensation.model.irregularflight.vo;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/modelCompensationFlightInfo.java/irregularflight.vo.CompensationPaxInfoVO <br>
 * Description：返回展示对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="CompensationPaxInfoVO对象", description="")
public class CompensationPaxInfoVO extends CompensationPaxInfoDO {

    private static final long serialVersionUID = 1L;

}
