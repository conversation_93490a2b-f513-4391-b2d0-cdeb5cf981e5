package com.swcares.aps.staff.bff.service.impl;

import com.swcares.aps.compensation.model.baggage.accident.dto.CompensationExpressInfoDTO;
import com.swcares.aps.compensation.model.compensation.dto.*;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditOperationVO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationBaseInfoVO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationMaterialDetailFinalVO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationMaterialQueriesListVO;
import com.swcares.aps.compensation.model.irregularflight.dto.FreezeOrderPaxDTO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationFromDetailsVO;
import com.swcares.aps.compensation.model.irregularflight.vo.GeneralCompensationOrderInfoDetailVO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.aps.staff.bff.service.CompensationOrderService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @ClassName：CompensationOrderServiceImpl
 * @Description：补偿单相关聚合服务
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/16 9:59
 * @version： v1.0
 */
@Service
public class CompensationOrderServiceImpl implements CompensationOrderService {

    private CompensationInfoApi compensationInfoApi;

    @Autowired
    public CompensationOrderServiceImpl(CompensationInfoApi compensationInfoApi){
        this.compensationInfoApi=compensationInfoApi;
    }

    /**
     * @title addMaterialCompensation
     * @description 创建实物补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单id
     */
    @Override
    public BaseResult<String> addMaterialCompensation(CompensationMaterialAddCommandDTO request) {
        return compensationInfoApi.addMaterialCompensation(request);
    }

    /**
     * @title editMaterialCompensation
     * @description 编辑实物补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单ID
     */
    @Override
    public BaseResult<String> editMaterialCompensation(CompensationMaterialEditCommandDTO request) {
        return compensationInfoApi.editMaterialCompensation(request);
    }

    /**
     * @title addCashCompensation
     * @description 创建现金补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单id
     */
    @Override
    public BaseResult<String> addCashCompensation(CompensationCashAddCommandDTO request) {
        return compensationInfoApi.addCashCompensation(request);
    }

    /**
     * @title editCashCompensation
     * @description 编辑现金补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单ID
     */
    @Override
    public BaseResult<String> editCashCompensation(CompensationCashEditCommandDTO request) {
        return compensationInfoApi.editCashCompensation(request);
    }

    /**
     * @title submit
     * @description 提交补偿单
     * <AUTHOR>
     * @date 2022/3/14 12:29
     * @param orderId
     * @return
     */
    @Override
    public BaseResult<CompensationAuditOperationVO> submit(String orderId) {
        return compensationInfoApi.submitCompensation(orderId);
    }

    /**
     * @title delete
     * @description 删除补偿单
     * <AUTHOR>
     * @date 2022/3/14 12:30
     * @param orderId
     * @return
     */
    @Override
    public BaseResult<String> delete(String orderId) {
        return compensationInfoApi.deleteCompensation(orderId);
    }

    /**
     * @title compensationBaseInfoPage
     * @description 条件分页查询赔偿单最基本信息记录
     * <AUTHOR>
     * @date 2022/3/16 9:28
     * @param request
     * @return
     */
    @Override
    public PagedResult<List<CompensationBaseInfoVO>> compensationBaseInfoPage(CompensationBaseInfoRequestDTO request) {
        return compensationInfoApi.compensationBaseInfoPage(request);
    }

    /**
     * @title compensationDetailSearch
     * @description 条件查询补偿单详情信息
     * <AUTHOR>
     * @date 2022/3/16 9:28
     * @param orderId
     * @return
     */
    @Override
    public BaseResult<Object> compensationDetailSearch(String orderId) {
        return compensationInfoApi.compensationDetailSearch(orderId);
    }

    /**
     * @title findCompensationLuggageList
     * @description 箱包补偿单列表查询
     * <AUTHOR>
     * @date 2022/4/15 16:28
     * @param dto
     * @return com.swcares.baseframe.common.base.PagedResult<java.util.List<com.swcares.aps.compensation.model.compensation.vo.CompensationMaterialQueriesListVO>>
     */
    @Override
    public PagedResult<List<CompensationMaterialQueriesListVO>> findCompensationLuggageList(CompensationMaterialListDTO dto) {
        return compensationInfoApi.findCompensationLuggageList(dto);
    }

    /**
     * @title findCompensationLuggageDetailInfo
     * @description 箱包补偿单详情信息查询
     * <AUTHOR>
     * @date 2022/4/15 16:29
     * @param dto
     * @return com.swcares.baseframe.common.base.BaseResult<com.swcares.aps.compensation.model.compensation.vo.CompensationMaterialDetailFinalVO>
     */
    @Override
    public BaseResult<CompensationMaterialDetailFinalVO> findCompensationLuggageDetailInfo(CompensationMaterialDetailDTO dto) {
        return compensationInfoApi.findCompensationLuggageDetailInfo(dto);
    }

    /**
     * @title mailGrant
     * @description 邮寄发放
     * <AUTHOR>
     * @date 2022/5/7 9:26
     * @param compensationExpressInfoDTO
     * @return BaseResult<Object>
     */
    @Override
    public BaseResult<Object> mailGrant(CompensationExpressInfoDTO compensationExpressInfoDTO) {
        return compensationInfoApi.mailGrant(compensationExpressInfoDTO);
    }

    @Override
    public BaseResult<Object> offlineGrant(OfflineGrantDTO offlineGrantDTO) {
        return compensationInfoApi.offlineGrant(offlineGrantDTO);
    }

    /**
     * @title auditPassToTakeEffect
     * @description 确认发放补偿单
     * <AUTHOR>
     * @date 2022/4/14 16:25
     * @param orderId
     * @return BaseResult<String>
     */
    @Override
    public BaseResult<String> auditPassToTakeEffect(String orderId) {
        return compensationInfoApi.auditPassToTakeEffect(orderId);
    }

    /**
     * @title closeCompensation
     * @description 关闭补偿单
     * <AUTHOR>
     * @date 2022/4/14 16:43
     * @param orderId
     * @return BaseResult<String>
     */
    @Override
    public BaseResult<String> closeCompensation(String orderId) {
        return compensationInfoApi.closeCompensation(orderId);
    }

    /**
     * @title findCashCompensationList
     * @description 查询现金补偿单列表
     * <AUTHOR>
     * @date 2022/4/22 11:40
     * @param request
     * @return PageResult<Object>
     */
    @Override
    public PagedResult<List<Object>> findCashCompensationList(CompensationBaseInfoRequestDTO request) {
        return compensationInfoApi.findCashCompensationList(request);
    }

    /**
     * @title freezeOrderPax
     * @description 冻结 || 解冻旅客
     * <AUTHOR>
     * @date 2022/4/22 11:39
     * @param freezeOrderPaxDTO
     * @return BaseResult<Object>
     */
    @Override
    public BaseResult<Object> freezeOrderPax(FreezeOrderPaxDTO freezeOrderPaxDTO) {
        return compensationInfoApi.freezeOrderPax(freezeOrderPaxDTO);
    }

    @Override
    public CompensationFromDetailsVO getCompensationDetailsVo(Long orderId) {
        CompensationFromDetailsVO compensationFromDetailsVO = new CompensationFromDetailsVO();
        // get Compensation order Info
        GeneralCompensationOrderInfoDetailVO orderInfoDetailVO = compensationInfoApi.detail(orderId).getData();
        compensationFromDetailsVO.setCompensationOrderInfo(orderInfoDetailVO);
        // get Audit Progress
        compensationFromDetailsVO.setAuditProgressInfo(compensationInfoApi.findAuditRecord(orderId,orderInfoDetailVO!=null?orderInfoDetailVO.getOrderNo():null).getData());
        // get Compensation Standard
        compensationFromDetailsVO.setCompensationStandardList(compensationInfoApi.findOrderRule(orderId).getData());
        return compensationFromDetailsVO;
    }

}
