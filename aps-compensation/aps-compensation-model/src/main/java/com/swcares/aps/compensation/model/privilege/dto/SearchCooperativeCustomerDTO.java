package com.swcares.aps.compensation.model.privilege.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> <PERSON>
 * @Classname SearchCustomerDTO
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/20 09:05
 * @Version 1.0
 */
@Data
@ApiModel(value="SearchCooperativeCustomerDTO", description="客户查询条件DTO")
public class SearchCooperativeCustomerDTO {
    @ApiModelProperty(value = "客户代码")
    private String code;

    @ApiModelProperty(value = "客户简称")
    private String shortName;

    @ApiModelProperty(value = "客户类别")
    private String customerCategory;

    @ApiModelProperty(value = "客户状态，0-启用，1-禁用，不传递表示所有")
    private String status;
}
