package com.swcares.aps.basic.data.remoteapi.model.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：FltFlightRealInfo <br>
 * Package：com.swcares.aps.flght.model.entity <br>
 * Copyright ? 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2022年 03月08日 15:56 <br>
 * @version v1.0 <br>
 */
@Data
@TableName("flt_flight_real_info")
public class FltFlightRealInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 航班状态 C 取消 D 延误
     */
    public static final String FLIGHT_STATE_DELAY = "D";
    public static final String FLIGHT_STATE_CANCEL = "C";

    @ApiModelProperty(value = "主键")
    @TableField("ID")
    @JSONField(name="id")
    private String id;

    @ApiModelProperty(value = "FOCID")
    @TableField("FOC_ID")
    @JSONField(name="FOC_ID")
    private String focId;

    @ApiModelProperty(value = "航班号")
    @TableField("FLIGHT_NUMBER")
    @JSONField(name="FLIGHT_NUMBER")
    private String flightNumber;

    @ApiModelProperty(value = "航班日期")
    @TableField("FLIGHT_DATE")
    @JSONField(name="FLIGHT_DATE",format = "yyyy-MM-dd HH:mm:ss")
    private Date flightDate;

    @TableField("LOCAL_DATE")
    @JSONField(name="LOCAL_DATE")
    private Date localDate;

    @ApiModelProperty(value = "返航备降标识")
    @TableField("FLG_VR")
    @JSONField(name="FLG_VR")
    private String flgVr;

    @ApiModelProperty(value = "出发机场")
    @TableField("ORG")
    @JSONField(name="ORG")
    private String org;

    @ApiModelProperty(value = "到达机场")
    @TableField("DST")
    @JSONField(name="DST")
    private String dst;

    @ApiModelProperty(value = "计划起飞时间")
    @TableField("STD")
    @JSONField(name="STD",format = "yyyy-MM-dd HH:mm:ss")
    private Date std;

    @ApiModelProperty(value = "预计起飞时间")
    @TableField("ETD")
    @JSONField(name="ETD")
    private Date etd;

    @ApiModelProperty(value = "实际起飞时间")
    @TableField("ATD")
    @JSONField(name="ATD")
    private Date atd;

    @ApiModelProperty(value = "计划到达时间")
    @TableField("STA")
    @JSONField(name="STA",format = "yyyy-MM-dd HH:mm:ss")
    private Date sta;

    @ApiModelProperty(value = "预计到达时间")
    @TableField("ETA")
    @JSONField(name="ETA")
    private Date eta;

    @ApiModelProperty(value = "实际到达时间")
    @TableField("ATA")
    @JSONField(name="ATA")
    private Date ata;

    @ApiModelProperty(value = "飞机号")
    @TableField("PLANECODE")
    @JSONField(name="PLANECODE")
    private String planecode;

    @ApiModelProperty(value = "航班运行状态")
    @TableField("FLIGHT_RUN_STATE")
    private String flightRunState;

    @ApiModelProperty(value = "航班调整状态")
    @TableField("FLIGHT_ADJUST_STATE")
    private String flightAdjustState;

   /* @ApiModelProperty(value = "航班状态")
    @TableField("FLIGHT_STATE")
    @JSONField(name="FLIGHT_STATE")
    private String flightState;*/

    @ApiModelProperty(value = "航班类型 D-国内|I-国际")
    @TableField("FLIGHT_TYPE")
    @JSONField(name="FLIGHT_TYPE")
    private String flightType;

    @ApiModelProperty(value = "可销售座位数")
    @TableField("SALE_SEAT")
    @JSONField(name="SALE_SEAT")
    private BigDecimal saleSeat;

    @ApiModelProperty(value = "飞行组")
    @TableField("FLIGHT_GROUP")
    @JSONField(name="FLIGHT_GROUP")
    private String flightGroup;

    @ApiModelProperty(value = "乘务组")
    @TableField("CABIN_CREW")
    @JSONField(name="CABIN_CREW")
    private String cabinCrew;

    @ApiModelProperty(value = "座位布局")
    @TableField("SEAT_LAYOUT")
    @JSONField(name="SEAT_LAYOUT")
    private String seatLayout;

    @ApiModelProperty(value = "不正常对内原因")
    @TableField("INNER_REASON")
    @JSONField(name="INNER_REASON")
    private String innerReason;

    @ApiModelProperty(value = "不正常对外原因")
    @TableField("EXTER_REASON")
    @JSONField(name="EXTER_REASON")
    private String exterReason;

    @ApiModelProperty(value = "登机口")
    @TableField("GATE")
    @JSONField(name="GATE")
    private String gate;

    @ApiModelProperty(value = "飞机型号")
    @TableField("FLIGHT_MODEL")
    @JSONField(name="FLIGHT_MODEL")
    private String flightModel;

    @ApiModelProperty(value = "是否有氧舱(Y|N)")
    @TableField("HAS_OXYGEN")
    @JSONField(name="HAS_OXYGEN")
    private String hasOxygen;

    @ApiModelProperty(value = "备降机场1")
    @TableField("ALTERNATE_AIRPORT")
    @JSONField(name="ALTERNATE_AIRPORT")
    private String alternateAirport;

    @ApiModelProperty(value = "备降机场2")
    @TableField("ALTERNATE_AIRPORT2")
    @JSONField(name="ALTERNATE_AIRPORT2")
    private String alternateAirport2;

    /*@ApiModelProperty(value = "备降状态")
    @TableField("ALTERNATE_STATUS")
    private String alternateStatus;*/

    @ApiModelProperty(value = "进港机位")
    @TableField("BAY_INCOMING")
    @JSONField(name="BAY_INCOMING")
    private String bayIncoming;

    @ApiModelProperty(value = "离港机位")
    @TableField("BAY_OUTGOING")
    @JSONField(name="BAY_OUTGOING")
    private String bayOutgoing;

    @ApiModelProperty(value = "计划性和临时性的明细，4位数值，分别代表延误，备降，取消，换机型。0表示未发生，1表示计划性，2表示临时性，3既有计划性又有临时性；如0201，则表示临时性取消，计划性换机型")
    @TableField("PLAN_TEMP_DETAIL")
    @JSONField(name="PLAN_TEMP_DETAIL")
    private String planTempDetail;

    @ApiModelProperty(value = "批复起飞时间")
    @TableField("HTD")
    @JSONField(name="HTD")
    private Date htd;

    @ApiModelProperty(value = "批复到达时间")
    @TableField("HTA")
    @JSONField(name="HTA")
    private Date hta;

    @ApiModelProperty(value = "航线(aslink)")
    @TableField("FLT_ROUTE")
    @JSONField(name="FLT_ROUTE")
    private String fltRoute;

    @ApiModelProperty(value = "状态(aslink)")
    @TableField("FLT_STATUS")
    @JSONField(name="FLT_STATUS")
    private String fltStatus;

    @ApiModelProperty(value = "登机号(aslink)")
    @TableField("FLT_GATE")
    @JSONField(name="FLT_GATE")
    private String fltGate;

    @ApiModelProperty(value = "餐食")
    @TableField("FLT_MEAL")
    @JSONField(name="FLT_MEAL")
    private String fltMeal;

    @ApiModelProperty(value = "GoShow限额")
    @TableField("FLT_GOSHOW_LIMT")
    @JSONField(name="FLT_GOSHOW_LIMT")
    private String fltGoshowLimt;

    @ApiModelProperty(value = "值机限额")
    @TableField("FLT_CKI_LIMT")
    @JSONField(name="FLT_CKI_LIMT")
    private String fltCkiLimt;

    @ApiModelProperty(value = "CI初始关闭时间")
    @TableField("FLT_CI_TIME")
    @JSONField(name="FLT_CI_TIME",format = "yyyy-MM-dd HH:mm:ss")
    private Date fltCiTime;

    @ApiModelProperty(value = "CL中间关闭时间")
    @TableField("FLT_CL_TIME")
    @JSONField(name="FLT_CL_TIME")
    private Date fltClTime;

    @ApiModelProperty(value = "CC完全关闭时间")
    @TableField("FLT_CC_TIME")
    @JSONField(name="FLT_CC_TIME",format = "yyyy-MM-dd HH:mm:ss")
    private Date fltCcTime;

    @ApiModelProperty(value = "座位布局信息")
    @TableField("FLT_SEAT_CONFIG")
    @JSONField(name="FLT_SEAT_CONFIG")
    private String fltSeatConfig;

    @ApiModelProperty(value = "机型")
    @TableField("FLT_CODE")
    @JSONField(name="FLT_CODE")
    private String fltCode;

    @ApiModelProperty(value = "飞机型号")
    @TableField("FLT_MODEL")
    @JSONField(name="FLT_MODEL")
    private String fltModel;

    @ApiModelProperty("注册号")
    @TableField("FLT_CONFIG_ID")
    @JSONField(name="FLT_CONFIG_ID")
    private String fltConfigId;

    @ApiModelProperty(value = "登机时间")
    @TableField("FLT_BOARDING_DATE")
    @JSONField(name="FLT_BOARDING_DATE")
    private Date fltBoardingDate;

    @ApiModelProperty(value = "出发航站楼")
    @TableField("FLT_DEPA_TERMINAL")
    @JSONField(name="FLT_DEPA_TERMINAL")
    private String fltDepaTerminal;

    @ApiModelProperty(value = "到达航站楼")
    @TableField("FLT_ARRI_TERMINAL")
    @JSONField(name="FLT_ARRI_TERMINAL")
    private String fltArriTerminal;

    @ApiModelProperty("航班锁定标识")
    @TableField("FLT_ENTIRE_HOLD")
    @JSONField(name="FLT_ENTIRE_HOLD")
    private String fltEntireHold;

    @ApiModelProperty(value = "最大载客量")
    @TableField("FLT_CAP_NUM")
    @JSONField(name="FLT_CAP_NUM")
    private String fltCapNum;

    @ApiModelProperty(value = "布局载客量")
    @TableField("FLT_CNF_NUM")
    @JSONField(name="FLT_CNF_NUM")
    private String fltCnfNum;

    @ApiModelProperty(value = "可利用座位")
    @TableField("FLT_AV_NUM")
    @JSONField(name="FLT_AV_NUM")
    private String fltAvNum;

   /* @ApiModelProperty(value = "通知上客时间")
    @TableField("ON_BOARD_TIME")
    private Date onBoardTime;*/

    @ApiModelProperty(value = "是否共享（Y-是）")
    @TableField("FLT_IS_SHARE")
    @JSONField(name="FLT_IS_SHARE")
    private String fltIsShare;

    @ApiModelProperty(value = "Aslink数据源")
    @TableField("SOURCE_HIS_IDS")
    @JSONField(name="SOURCE_HIS_IDS")
    private String sourceHisIds;

    @TableField("AVERAGE_FARE")
    @ApiModelProperty(value = "平均票价")
    private String averageFare;
}
