package com.swcares.aps.compensation.model.complaint.dto;

import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@ApiModel(value = "ComplaintWorkflowSubmitDo",description = "投诉审核补偿单单提交参数")
public class ComplaintWorkflowSubmitDo {
    //补偿总金额
    private String compensationAmount;
    //事故类型(补偿子类型)
    private String compensationSubType;
    // 补偿航站
    private String compensationAirport;
    // 发起人归属单位
    private String belongUnit;
    // 原因类型
    private String reasonType;
    // 补偿来源
    private String source;
}
