package com.swcares.aps.compensation.model.baggage.accident.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @title: CompensationExpressInfoDTO
 * @projectName aps
 * @description: CompensationExpressInfoDTO对象
 * @date 2022/3/3 17:16
 */
@Data
@ApiModel(value="CompensationExpressInfoDTO对象", description="赔偿_快递信息对象")
public class CompensationExpressInfoDTO {

    @ApiModelProperty(value = "补偿旅客主键id")
    private String id;

    @ApiModelProperty(value = "旅客id")
    private String paxId;

    @ApiModelProperty(value = "收件人")
    @NotNull
    private String addressee;

    @ApiModelProperty(value = "收件地址")
    @NotNull
    private String address;

    @ApiModelProperty(value = "联系电话")
    @NotNull
    private String phone;

    @ApiModelProperty(value = "快递类型(手动添加，补偿单线下发放等)")
    private String expressType;

    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    @ApiModelProperty(value = "快递公司")
    private String expressCompany;

    @ApiModelProperty(value = "邮寄物品")
    @NotNull
    private String expressGoods;

    @ApiModelProperty(value = "补偿单号")
    private String orderNo;

    @ApiModelProperty(value = "补偿单ID")
    private String compensationId;

    @ApiModelProperty(value = "事故单id")
    @NotNull
    private Long accidentId;
}
