package com.swcares.aps.compensation.impl.apply.service;

import com.swcares.aps.compensation.model.apply.dto.ApplyBehalfOrderDTO;
import com.swcares.aps.compensation.model.apply.dto.ApplyOrderDTO;
import com.swcares.baseframe.common.base.BaseResult;

/**
 * @ClassName：TransactionLockService
 * @Description：集群申领建单时，做并发控制
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 夏阳
 * @Date： 2022/9/20 14:06
 * @version： v1.0
 */
public interface TransactionLockService {

    /**
     * @title
     * @description 本人领取做并发接口控制的入口
     * <AUTHOR>
     * @date 2022/9/20 14:12
     * @param dto
     * @return void
     */
    void saveApplyNormalOrder(ApplyOrderDTO dto);

    /**
     * @title
     * @description 代人领取做并发接口控制的入口
     * <AUTHOR>
     * @date 2022/9/20 14:12
     * @param dto
     * @return void
     */
    void saveApplyBehalfOrder(ApplyBehalfOrderDTO dto);

    /**
     * @title
     * @description 协助领取做并发接口控制的入口
     * <AUTHOR>
     * @date 2022/9/20 14:12
     * @param dto
     * @return void
     */
    BaseResult<Object> saveApplyAssistOrder(ApplyBehalfOrderDTO dto);
}
