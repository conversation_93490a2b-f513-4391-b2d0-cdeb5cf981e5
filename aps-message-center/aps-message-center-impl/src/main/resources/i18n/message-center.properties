## 编码使用规则：(5-系统类异常、4-业务类异常)
## 1. 第1位数字表示异常类型
## 2. 第2、3、4位数字表示业务模块
## 3. 第5、6、7位数字表示异常编码
4101001=下载文件错误
4101002=客户端id{0}错误,请检查输入的id与配置的值是否一致
4101003=用户{0}没有在微信客户端{1}进行绑定
4101004=无效的模板{0}




4101005=导入文件格式错误

4101006=表格表头与模板不一致

4101007=短信任务{0}不存在

4101008=此短信任务不能进行停用

4101009={0}短信任务不能进行审核

4101010=通过{0}：{1}获取电话号码失败



4101011=未知的客户端{0}
4101012=消息格式不正确:{0}
4101013=请求签名不正确
4101014=消息加密不正确，无法解密
4101015=航变消息{0}内容不正确:{1}
4101016=发送短信时发送错误
4101017=处理航变事件消息{0}失败

4101018=导入文件中没有数据

4101019=修改模板失败。非部门成员，无法编辑该模板。

4101020=无效的导入类型:{1}

4101021=短信类型和选择接收人的方式不匹配。航班保障业务通知只能使用电话号码导入或指定接收人的方式选择接收人。
##  下面是系统错误代码 ##
5101001=调用微信接口{0}错误：{1}
