package com.swcares.aps.compensation.model.privilege.dto;

import cn.hutool.core.bean.BeanUtil;
import com.swcares.aps.compensation.model.privilege.entity.CooperativeCustomer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <PERSON>
 * @Classname CooperativeCustomerDTO
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/20 09:05
 * @Version 1.0
 */
@ApiModel(value="CooperativeCustomerDTO", description="客户合作的客户的DTO")
@Data
public class CooperativeCustomerDTO {
    @ApiModelProperty(value = "客户ID")
    private Long id;
    @ApiModelProperty(value = "客户代码")
    private String code;

    @ApiModelProperty(value = "客户名称")
    private String name;

    @ApiModelProperty(value = "客户简称")
    private String shortName;

    @ApiModelProperty(value = "客户类别")
    private String customerCategory;

    @ApiModelProperty(value = "是否禁用")
    private boolean disabled;

    @ApiModelProperty(value = "备注")
    private String remark;

    public static CooperativeCustomerDTO fromEntity(CooperativeCustomer entity) {
        if (entity == null){
            return null;
        }

        CooperativeCustomerDTO dto = new CooperativeCustomerDTO();
        BeanUtil.copyProperties(entity, dto);
        return dto;
    }

    public static List<CooperativeCustomerDTO> fromEntityList(List<CooperativeCustomer> entityList) {
        if (entityList == null){
            return null;
        }

        return entityList.stream().map(CooperativeCustomerDTO::fromEntity).collect(Collectors.toList());
    }
}
