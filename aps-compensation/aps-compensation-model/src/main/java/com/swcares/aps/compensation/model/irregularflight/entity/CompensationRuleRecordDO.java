package com.swcares.aps.compensation.model.irregularflight.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.entity.CompensationRuleRecord <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-29 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="CompensationRuleRecordDO对象", description="")
@TableName(value = "compensation_rule_record")
public class CompensationRuleRecordDO {

    private static final long serialVersionUID = 1L;

    private Long tenantId;
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "创建者")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新者")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;
    @ApiModelProperty(value = "舱位类型(1-经济舱，2-公务舱)")
    @NotNull
    private String classType;

    @ApiModelProperty(value = "补偿金额")
    @Min(value = 1,message = "金额需为大于0的正整数")
    @Max(value = 9999,message = "金额需为小于10000的正整数")
    private BigDecimal cpsNum;

    @ApiModelProperty(value = "是否自定义金额（0否 1是）")
    private String isCustom;

    @ApiModelProperty(value = "成人赔偿标准")
    private BigDecimal adultStd;

    @ApiModelProperty(value = "成人赔偿标准是百分比还是实际金额,1是百分比2是实际金额")
    private Integer isPercentageAdult;

    @ApiModelProperty(value = "婴儿赔偿标准")
    private BigDecimal babyStd;

    @ApiModelProperty(value = "婴儿赔偿标准是百分比还是实际金额,1是百分比2是实际金额")
    private Integer isPercentageBaby;

    @ApiModelProperty(value = "儿童赔偿标准")
    private BigDecimal childStd;

    @ApiModelProperty(value = "儿童赔偿标准是百分比还是实际金额,1是百分比2是实际金额")
    private Integer isPercentageChild;

    @ApiModelProperty(value = "赔偿单ID")
    private Long orderId;

    @ApiModelProperty(value = "事故单ID")
    private Long accidentId;

}
