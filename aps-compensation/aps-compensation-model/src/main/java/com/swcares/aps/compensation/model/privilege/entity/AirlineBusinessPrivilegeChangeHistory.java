package com.swcares.aps.compensation.model.privilege.entity;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Classname DataPrivilegeChangeHistory
 * @Description 数据授权配置数据变更记录
 * @Version 1.0.0
 * @Date 2024/5/13 10:21
 * @Created by Wang Yi
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("business_privilege_chg_his")
public class AirlineBusinessPrivilegeChangeHistory extends BaseEntity {
    private Long businessPrivilegeId;

    private String grantorCode;

    private String recipientCode;

    private String updateType;

    private String oldValue;

    private String newValue;

    private LocalDateTime changeTime;

    private String updaterEmployeeName;

    private String updaterJobNumber;

    private Long tenantId;

    public static final String CHANGE_TYPE_UPDATE = "修改";
    public static final String CHANGE_TYPE_ADD = "新增";
    public static final String CHANGE_TYPE_DELETE = "删除";
    public static AirlineBusinessPrivilegeChangeHistory creatAddBusinessPrivilegeHistory(AirlineBusinessPrivilege businessPrivilege, List<AirlineBusinessPrivilegeItem> businessPrivilegeItems){
        AirlineBusinessPrivilegeChangeHistory history = new AirlineBusinessPrivilegeChangeHistory();
        history.setGrantorCode(businessPrivilege.getGrantorCode());
        history.setRecipientCode(businessPrivilege.getRecipientCode());
        history.setUpdaterEmployeeName(businessPrivilege.getUpdaterEmployeeName());
        history.setUpdaterJobNumber(businessPrivilege.getUpdaterJobNumber());
        history.setBusinessPrivilegeId(businessPrivilege.getId());

        history.setUpdateType(CHANGE_TYPE_ADD);

        history.setChangeTime(LocalDateTime.now());
        Map<String, Object> newValue = new HashMap<>();
        newValue.put("businessPrivilege", businessPrivilege);
        newValue.put("businessPrivilegeItems", businessPrivilegeItems);
        history.setNewValue(JSONUtil.toJsonStr(newValue));

        return history;
    }

    public static AirlineBusinessPrivilegeChangeHistory createUpdateBusinessPrivilegeHistory(AirlineBusinessPrivilege oldBusinessPrivilege, AirlineBusinessPrivilege newBusinessPrivilege,
                                                                                             List<AirlineBusinessPrivilegeItem> oldBusinessPrivilegeItems, List<AirlineBusinessPrivilegeItem> newBusinessPrivilegeItems) {
        if (!StrUtil.equals(oldBusinessPrivilege.getGrantorCode(), newBusinessPrivilege.getGrantorCode(), true)
                || !StrUtil.equals(oldBusinessPrivilege.getRecipientCode(), newBusinessPrivilege.getRecipientCode(), true)
                || !StrUtil.equals(oldBusinessPrivilege.getGrantorCategory(), newBusinessPrivilege.getGrantorCategory(), true)
                || !StrUtil.equals(oldBusinessPrivilege.getRecipientCategory(), newBusinessPrivilege.getRecipientCategory(), true)) {
            //不是同一个授权对象，则不生成变更记录
            return null;
        }
        AirlineBusinessPrivilegeChangeHistory changeHistory = new AirlineBusinessPrivilegeChangeHistory();
        changeHistory.setChangeTime(LocalDateTime.now());
        changeHistory.setGrantorCode(oldBusinessPrivilege.getGrantorCode());
        changeHistory.setRecipientCode(oldBusinessPrivilege.getRecipientCode());
        changeHistory.setUpdateType(CHANGE_TYPE_UPDATE);
        changeHistory.setUpdaterEmployeeName(newBusinessPrivilege.getUpdaterEmployeeName());
        changeHistory.setUpdaterJobNumber(newBusinessPrivilege.getUpdaterJobNumber());
        changeHistory.setBusinessPrivilegeId(newBusinessPrivilege.getId());

        Map<String, Object> newValue = new HashMap<>();
        newValue.put("businessPrivilege", newBusinessPrivilege);
        newValue.put("businessPrivilegeItems", newBusinessPrivilegeItems);
        changeHistory.setNewValue(JSONUtil.toJsonStr(newValue));

        Map<String, Object> oldValue = new HashMap<>();
        oldValue.put("businessPrivilege", oldBusinessPrivilege);
        oldValue.put("businessPrivilegeItems", oldBusinessPrivilegeItems);
        changeHistory.setOldValue(JSONUtil.toJsonStr(oldValue));

        return changeHistory;
    }
}
