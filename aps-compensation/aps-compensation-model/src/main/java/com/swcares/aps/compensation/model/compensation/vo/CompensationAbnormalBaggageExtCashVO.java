package com.swcares.aps.compensation.model.compensation.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CompensationMaterialExtCashVO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 31947
 * @Date： 2022/4/18 16:52
 * @version： v1.0
 */
@Data
@ApiModel(value="CompensationAbnormalBaggageExtCashVO对象", description="现金补偿单（异常行李）列表VO")
public class CompensationAbnormalBaggageExtCashVO extends CompensationBaseExtCashVO{

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;
}
