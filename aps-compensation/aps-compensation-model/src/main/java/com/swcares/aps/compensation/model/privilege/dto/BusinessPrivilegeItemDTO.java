package com.swcares.aps.compensation.model.privilege.dto;

import cn.hutool.core.bean.BeanUtil;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilegeItem;
import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <PERSON>
 * @Classname BusinessPrivilegeDetailDTO
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/11 15:40
 * @Version 1.0
 */
@Data
@ApiModel(value="BusinessPrivilegeDetailDTO", description="业务权限明细DTO")
public class BusinessPrivilegeItemDTO implements BaseDTO {
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "业务权限主键")
    private long businessPrivilegeId;

    @ApiModelProperty(value = "业务类型编码")
    private String businessTypeCode;

    @ApiModelProperty(value = "业务类型名称")
    private String businessTypeName;

    @ApiModelProperty(value = "是否授权资金权限")
    private boolean grantBankroll;

    @ApiModelProperty(value = "旅客领取的url")
    private String passengerApplyUrl;

    public static final BusinessPrivilegeItemDTO fromEntity(AirlineBusinessPrivilegeItem entity){
        BusinessPrivilegeItemDTO dto = new BusinessPrivilegeItemDTO();

        BeanUtil.copyProperties(entity, dto);
        return dto;
    }

    public AirlineBusinessPrivilegeItem toEntity(){
        AirlineBusinessPrivilegeItem entity = new AirlineBusinessPrivilegeItem();
        BeanUtil.copyProperties(this, entity);

        return entity;
    }

    public static final List<BusinessPrivilegeItemDTO> fromEntityList(List<AirlineBusinessPrivilegeItem> items){
        return items.stream().map(item -> fromEntity(item)).collect(Collectors.toList());
    }

    public static final List<AirlineBusinessPrivilegeItem> toEntityList(List<BusinessPrivilegeItemDTO> items){

        if (items == null || items.isEmpty()){
            return new ArrayList<AirlineBusinessPrivilegeItem>();
        }
        return items.stream().map(item -> item.toEntity()).collect(Collectors.toList());
    }
}
