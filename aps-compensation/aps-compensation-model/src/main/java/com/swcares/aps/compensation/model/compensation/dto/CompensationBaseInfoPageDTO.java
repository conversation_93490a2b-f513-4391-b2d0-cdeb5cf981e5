package com.swcares.aps.compensation.model.compensation.dto;

import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：CompensationBaseInfoPageDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/9 13:49
 * @version： v1.0
 */
@Data
public class CompensationBaseInfoPageDTO extends CompensationBaseInfoRequestDTO{

    /**
     * 事故单类型
     * 用于运行时行权限的实现，字段可能会有多个值，需要拼接到对应查询的sql中
     */
    private List<String> accidentTypes;

    /**
     * 工作航站
     * 用于运行时行权限的实现，字段可能会有多个值，需要拼接到对应查询的sql中
     */
    private List<String> workStations;

    public static CompensationBaseInfoPageDTO of(CompensationBaseInfoRequestDTO request){
        CompensationBaseInfoPageDTO compensationBaseInfoPageDTO = ObjectUtils.copyBean(request, CompensationBaseInfoPageDTO.class);
        return compensationBaseInfoPageDTO;
    }
}
