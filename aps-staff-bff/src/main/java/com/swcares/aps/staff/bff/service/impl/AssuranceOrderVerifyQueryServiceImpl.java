package com.swcares.aps.staff.bff.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.component.com.platform.PlatformTool;
import com.swcares.aps.ground.AssuranceQueryApi;
import com.swcares.aps.ground.enums.AssuranceOrderState;
import com.swcares.aps.ground.enums.VerificationQueryType;
import com.swcares.aps.ground.enums.VerificationState;
import com.swcares.aps.ground.models.assurance.dto.AssuranceVerifyPaxScanQueryRequest;
import com.swcares.aps.ground.models.assurance.dto.AssuranceVerifyQueryRequest;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderServiceDetailsVo;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderServiceVerifyUserDetailVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderVerifyQueryResultVO;
import com.swcares.aps.staff.bff.service.AssuranceOrderVerifyQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @ClassName：AssuranceOrderVerifyQueryServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/9/27 10:54
 * @version： v1.0
 */
@Service
@Slf4j
public class AssuranceOrderVerifyQueryServiceImpl implements AssuranceOrderVerifyQueryService {

    @Autowired
    private AssuranceQueryApi assuranceQueryApi;

    @Autowired
    private PlatformTool platformTool;

    @Override
    public IPage<AssuranceOrderVerifyQueryResultVO> assuranceWaitVerifyByScan(AssuranceVerifyPaxScanQueryRequest request) {
        AssuranceVerifyQueryRequest queryRequest = new AssuranceVerifyQueryRequest();
        BeanUtil.copyProperties(request,queryRequest);
        queryRequest.setWaitVerifyQueryType(VerificationQueryType.SCAN.getCode());
        queryRequest.setOrderState(AssuranceOrderState.SERVICE_ING.getCode());
        queryRequest.setVerificationState(Arrays.asList(VerificationState.WAIT.getCode()));

        //航司端搜索，因此也只能搜索该旅客服务项存在航司供应商保障的服务项信息
        //如果航司工作人员扫码/输入证件号后发现并没有航司自有供应商供核销，则弹框提示：该旅客待核销项没有航司自有供应商支持，无法使用航司端进行核销
        if(platformTool.isAirlinePlatform()){
            queryRequest.setChoiceSupplierBelongTenantId(platformTool.getCurrentTenantId());
        }

//        排序：按保障单号倒序排序，保障单号相同按待核销服务项首字母排序
        queryRequest.setItems(OrderItem.descs("orderNo","serviceName"));
        //查询待核销数据
        List<AssuranceOrderServiceVerifyUserDetailVO> waitVerifyList = assuranceQueryApi.getOrderVerifyPage(queryRequest).getData();
/*        if(platformTool.isAirlinePlatform() && waitVerifyList.size() == 0){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"该旅客待核销项没有航司自有供应商支持，无法使用航司端进行核销");
        }*/


        List<AssuranceOrderVerifyQueryResultVO> orderVerifyQueryResultVO = getOrderVerifyQueryResultVO(waitVerifyList);

        IPage<AssuranceOrderVerifyQueryResultVO> iPage = new Page<>(queryRequest.getPageNumber(), queryRequest.getPageSize());
        // 设置总记录数
        iPage.setTotal(orderVerifyQueryResultVO.size());
        // 设置数据列表
        iPage.setRecords(orderVerifyQueryResultVO);
        return iPage;
    }

    @Override
    public IPage<AssuranceOrderVerifyQueryResultVO> assuranceWaitVerifyByIdNo(String idNo) {
        AssuranceVerifyQueryRequest queryRequest = new AssuranceVerifyQueryRequest();
        queryRequest.setIdNo(idNo);
        queryRequest.setWaitVerifyQueryType(VerificationQueryType.IDNO.getCode());
        queryRequest.setOrderState(AssuranceOrderState.SERVICE_ING.getCode());
        queryRequest.setVerificationState(Arrays.asList(VerificationState.WAIT.getCode()));
        queryRequest.setPageNumber(1);
        queryRequest.setPageSize(30);
        //航司端搜索，因此也只能搜索该旅客服务项存在航司供应商保障的服务项信息
        //如果航司工作人员扫码/输入证件号后发现并没有航司自有供应商供核销，则弹框提示：该旅客待核销项没有航司自有供应商支持，无法使用航司端进行核销
        if(platformTool.isAirlinePlatform()){
            queryRequest.setChoiceSupplierBelongTenantId(platformTool.getCurrentTenantId());
        }
        //查询待核销数据
        List<AssuranceOrderServiceVerifyUserDetailVO> waitVerifyList = assuranceQueryApi.getOrderVerifyPage(queryRequest).getData();

        List<AssuranceOrderVerifyQueryResultVO> orderVerifyQueryResultVO = getOrderVerifyQueryResultVO(waitVerifyList);

        IPage<AssuranceOrderVerifyQueryResultVO> iPage = new Page<>(queryRequest.getPageNumber(), queryRequest.getPageSize());
        // 设置总记录数
        iPage.setTotal(orderVerifyQueryResultVO.size());
        // 设置数据列表
        iPage.setRecords(orderVerifyQueryResultVO);
        return iPage;
    }

    public List<AssuranceOrderVerifyQueryResultVO> getOrderVerifyQueryResultVO(List<AssuranceOrderServiceVerifyUserDetailVO> waitVerifyList){
        //查询待核销数据
        List<AssuranceOrderVerifyQueryResultVO> resultVOList = new ArrayList<>();
        waitVerifyList.forEach(d->{
            //
            AssuranceOrderVerifyQueryResultVO queryResultVO = new AssuranceOrderVerifyQueryResultVO();
            queryResultVO.setVerifyUserDetailVO(d);
            AssuranceOrderServiceDetailsVo data = assuranceQueryApi.getOrderServiceDetails(d.getOrderServiceId()).getData();
            queryResultVO.setOrderServiceChoiceDOS(data.getOrderServiceChoiceDOS());
            resultVOList.add(queryResultVO);
        });
        return resultVOList;
    }

    @Override
    public Object waitVerifyNum(AssuranceVerifyPaxScanQueryRequest request) {
//        该旅客+航班+航班日期+服务项下剩余待核销数量
        AssuranceVerifyQueryRequest queryRequest = new AssuranceVerifyQueryRequest();
        queryRequest.setOrderState(AssuranceOrderState.SERVICE_ING.getCode());
        queryRequest.setVerificationState(Arrays.asList(VerificationState.WAIT.getCode()));
        queryRequest.setIdNo(request.getIdNo());
        queryRequest.setFlightNo(request.getFlightNo());
        queryRequest.setFlightDate(request.getFlightDate());
        queryRequest.setServiceId(request.getServiceId());
        queryRequest.setPageNumber(1);
        queryRequest.setPageSize(299);
        //查询待核销数据
        List<AssuranceOrderServiceVerifyUserDetailVO> waitVerifyList = assuranceQueryApi.getOrderVerifyPage(queryRequest).getData();
        return waitVerifyList.size();
    }
}
