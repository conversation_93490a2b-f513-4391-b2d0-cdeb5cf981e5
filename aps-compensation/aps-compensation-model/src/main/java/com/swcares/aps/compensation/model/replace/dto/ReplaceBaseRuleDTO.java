package com.swcares.aps.compensation.model.replace.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * ClassName：com.swcares.aps.compensation.model.replace.dto.ReplaceRuleDTO <br>
 * Description： 数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-10 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="ReplaceBaseRuleDTO对象", description="代人领取规则DTO,除开支付等待期外的所有RULE配置")
public class ReplaceBaseRuleDTO implements BaseDTO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否包含本人 0:不包含 1:包含",example = "1",required = true)
    @NotNull
    @Pattern(regexp="^[0|1]$")
    private String isContainSelf;

    @ApiModelProperty(value = "是否同航班旅客 0:不是 1:是",example = "1",required = true)
    @NotNull
    @Pattern(regexp="^[0|1]$")
    private String isSameFlight;

    @ApiModelProperty(value = "最大可申领取乘机人数",example = "10",required = true)
    @NotNull
    @Min(value = 1)
    private Integer maxApplyPassenger;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

}
