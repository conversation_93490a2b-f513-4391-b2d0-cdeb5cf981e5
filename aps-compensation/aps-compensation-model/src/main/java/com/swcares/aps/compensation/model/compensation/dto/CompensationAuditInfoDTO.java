package com.swcares.aps.compensation.model.compensation.dto;

import com.swcares.baseframe.utils.lang.ObjectUtils;

/**
 * @ClassName：CompensationAuditInfoDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/9 16:10
 * @version： v1.0
 */
public class CompensationAuditInfoDTO extends CompensationAuditInfoRequestDTO{

    public static CompensationAuditInfoDTO of(CompensationAuditInfoRequestDTO request){
        CompensationAuditInfoDTO compensationAuditInfoDTO = ObjectUtils.copyBean(request, CompensationAuditInfoDTO.class);
        return compensationAuditInfoDTO;
    }
}
