package com.swcares.aps.compensation.model.irregularflight.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * ClassName：com.swcares.aps.compensation.model.irregularflight.vo <br>
 * Description：赔偿单详情表单vo（包含事故单信息、赔偿单信息、审核进度信息） <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 01月11日 13:54 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="CompensationFromDetailsVO对象", description="赔偿单信息详情")
public class CompensationFromDetailsVO {

    @ApiModelProperty(value = "事故单信息")
    private Object accidentInfoDetails;
    @ApiModelProperty(value = "赔偿单信息")
    private GeneralCompensationOrderInfoDetailVO compensationOrderInfo;
    @ApiModelProperty(value = "审核进度")
    private Object auditProgressInfo;
    @ApiModelProperty(value = "赔偿规则")
    private Object compensationStandardList;
}
