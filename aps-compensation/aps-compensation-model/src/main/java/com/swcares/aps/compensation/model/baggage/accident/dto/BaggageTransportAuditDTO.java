package com.swcares.aps.compensation.model.baggage.accident.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * ClassName：BaggageTransportAuditDTO <br>
 * Description：运输单审核DTO <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2025/1/28 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "BaggageTransportAuditDTO对象", description = "运输单审核DTO")
public class BaggageTransportAuditDTO {

    @ApiModelProperty(value = "运输单ID", required = true)
    @NotNull(message = "运输单ID不能为空")
    private Long transportId;

    @ApiModelProperty(value = "任务ID", required = true)
    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    @ApiModelProperty(value = "审核状态：AGREE-同意，REJECT-不同意，BACK-驳回", required = true)
    @NotBlank(message = "审核状态不能为空")
    private String auditStatus;

    @ApiModelProperty(value = "审核意见")
    private String comment;

    @ApiModelProperty(value = "审核人用户ID，前端不用传")
    private String auditorUserId;
}