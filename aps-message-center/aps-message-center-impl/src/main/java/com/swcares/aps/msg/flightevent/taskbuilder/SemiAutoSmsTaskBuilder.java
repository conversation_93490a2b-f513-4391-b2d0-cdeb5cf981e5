/*
 * All rights Reserved, Designed By 王翼(<EMAIL>) <br>
 * Title：SmsTaskBuilder <br>
 * Package：com.swcares.reaptv.msg.flightevent.taskbuilder <br>
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 */
package com.swcares.aps.msg.flightevent.taskbuilder;



import com.swcares.aps.msg.model.dto.SemiAutoSmsTaskDTO;
import com.swcares.aps.msg.model.entity.FlightEvent;
import com.swcares.aps.msg.model.entity.FlightEventPassenger;

import java.util.List;

/**
 * InterfaceName：com.swcares.reaptv.msg.flightevent.taskbuilder.SmsTaskBuilder <br>
 * Description：短信任务构造器接口定义
 * <AUTHOR> <br>
 * date 2022/12/5 9:01 <br>
 * @version v1.0.0 <br>
 */
public interface SemiAutoSmsTaskBuilder {
    public boolean isSupport(FlightEvent flightEvent);
    
    public List<SemiAutoSmsTaskDTO> buildSemiAutoTask(FlightEvent flightEvent, List<FlightEventPassenger> passengers);
}
