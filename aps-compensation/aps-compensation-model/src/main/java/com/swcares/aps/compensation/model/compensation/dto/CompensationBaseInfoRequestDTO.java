package com.swcares.aps.compensation.model.compensation.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @ClassName：CompensationBaseInfoRequestDTO
 * @Description：补偿单最基本的信息查询接口参数，所有类型补偿单都有的信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/9 12:37
 * @version： v1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="CompensationBaseInfoRequestDTO对象", description="补偿单最基本信息查询接口参数")
public class CompensationBaseInfoRequestDTO extends PagedDTO {

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班起始日期")
    private String flightStartDate;

    @ApiModelProperty(value = "航班结束日期")
    private String flightEndDate;

    @ApiModelProperty(value = "起始航站")
    private String orgCity;

    @ApiModelProperty(value = "到达航站")
    private String dstCity;

    @ApiModelProperty(value = "赔偿单单号")
    private String orderNo;

    @ApiModelProperty(value = "服务航站")
    private List<String> serviceCity;

    @ApiModelProperty(value = "补偿类型-1不正常航班，2异常行李，3超售")
    private String accidentType;

    @ApiModelProperty(value = "补偿子类型-1延误，2取消，3备降，4返航，5补班; 21破损,22少收,23多收,24内件缺失,25丢失")
    private List<String> accidentSubType;

    @ApiModelProperty(value = "事故单ID")
    private Long accidentId;

    @ApiModelProperty(value = "事故单编号")
    private String accidentNo;

    @ApiModelProperty(value = "补偿方式 1现金，2虚拟，3实物")
    private String compensateType;

    @ApiModelProperty(value = "1草稿,2审核中,3审核不通过,4驳回,5审核通过,6生效,7关闭,8逾期")
    private List<String> status;

    @ApiModelProperty(value = "提交人ID")
    private String createdBy;

    @ApiModelProperty(value = "当前操作人Id（前端不传）")
    private String userId;

}
